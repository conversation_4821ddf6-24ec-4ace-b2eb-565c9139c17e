# 舱室详情界面需求文档

## 📋 实现状态概览
- ✅ **底层背景结构** - 完全实现
- ✅ **16孔椭圆排列** - 完全实现，支持胚胎状态显示
- ✅ **患者信息显示** - 完全实现，支持中英文姓名
- ✅ **环境监控数据** - 完全实现，实时数据同步
- ✅ **图像控制功能** - 新增实现
- ✅ **患者管理按钮** - 完全实现
- ✅ **数据同步机制** - 完全实现

## 🎯 界面功能概述
### 舱室数据区域（左）
1. 环境监控数据
2. 患者操作及患者信息显示
   1）当前舱室未分配患者时，显示"分配患者"按钮
   2）点击"分配患者"按钮，右侧弹出患者列表弹窗![分配患者](<Chamber - New - Add Patient.png>)
   3）当前舱室已分配患者时，根据患者后台推送患者信息并显示到对应为止，并隐藏"分配患者"按钮和文字，显示"移除患者"和"编辑患者"按钮
   4）点击"移除患者"按钮，进入移除患者确认界面![移除患者](<Well Dish View - Remove Patient pop pup.png>)
   5）点击"编辑患者"按钮，进入编辑患者界面![编辑患者](<Chamber - New - Image Capture – Edit Patient.png>)

### 操作区（右）
3. 图像控制及孔位图像显示
   当患者分配好，显示图像控制区域，包括捕获图像按钮、调整图像按钮，以及操作引导文字
   
   3）图像预览显示区域
   4）图像信息显示
4. 引导文字显示

## 🏗️ 界面层级结构 (三层架构)

### 界面层级结构图
以下图表展示了舱室详情界面的完整层级结构和组件关系：

```mermaid
graph TB
    A[舱室详情界面 3840×720] --> B[Home背景 #1E2233]

    B --> C[底层 - 基础背景与16孔显示区域]
    B --> D[中间层 - 图像控制区域]
    B --> E[顶层 - 患者数据显示区域]

    C --> C1[底层背景结构<br/>1890×590<br/>#313542<br/>特殊圆角]
    C --> C2[16孔显示区域<br/>574×574<br/>椭圆排列]
    C --> C3[开始录制按钮<br/>176×73<br/>居中显示]
    C --> C4[引导文字区域<br/>300×590<br/>状态驱动]

    D --> D1[图像控制区域结构<br/>920×590<br/>#3D404D<br/>左圆角]
    D --> D2[捕获图像按钮<br/>80×140<br/>Image Preview]
    D --> D3[调整图像按钮<br/>80×140<br/>Adjust Image]
    D --> D4[右侧阴影条<br/>30×590<br/>渐变效果]

    E --> E1[患者数据区域结构<br/>708×590<br/>#3D404D<br/>左圆角]
    E --> E2[舱室序号显示<br/>57×138<br/>大号数字]
    E --> E3[患者信息区域<br/>姓名+详细信息]
    E --> E4[环境监控数据<br/>289×155<br/>实时同步]
    E --> E5[患者管理按钮<br/>108×78<br/>状态切换]

    C4 --> C41[Step 1引导]
    C4 --> C42[Step 2引导]
    C4 --> C43[图像捕获引导]
    C4 --> C44[空孔检测引导]
    C4 --> C45[空孔确认引导]
    C4 --> C46[调整图像引导]

    style A fill:#1E2233,color:#fff
    style C fill:#313542,color:#fff
    style D fill:#3D404D,color:#fff
    style E fill:#3D404D,color:#fff
```

### 1. 底层 - 基础背景与16孔显示区域 ✅

#### 1.1 底层背景结构 ✅
![舱室界面未分配患者状态](<Chamber - New.png>)
**使用Home界面背景** (#1E2233)，在该背景之上显示舱室详情模块

![舱室区域的背景](image-2.png)
**底层背景规格**：
- 位置：x: 20px, y: 110px
- 尺寸：width: 1890px, height: 590px
- 颜色：#313542
- 圆角：border-radius: 40px 295px 295px 40px
- **实现方式**：使用Canvas绘制精确圆角路径

#### 1.2 右侧16孔显示区域 ✅
![16孔显示](image-6.png)![单个孔的图片](image-7.png)![序号](image-8.png)

##### 16孔区域整体规格
- **位置**：x: 1305px, y: 8px (距离底层背景右侧8px，上下居中)
- **尺寸**：width: 574px, height: 574px (正方形区域)
- **背景色**：transparent (完全透明)

##### 椭圆排列算法参数
- **椭圆中心**：X = 286.5px, Y = 287.5px
- **椭圆半径**：radiusX = 226px, radiusY = 226px
- **关键孔位坐标**：
  - 1号孔（上）：圆心Y = 61.5px (距上边界20px)
  - 5号孔（右）：圆心X = 512.5px (距右边界20px)
  - 9号孔（下）：圆心Y = 513.5px (距下边界19px)
  - 13号孔（左）：圆心X = 60.5px (距左边界19px)

##### 单个孔位规格
- **尺寸**：width: 83px, height: 83px
- **形状**：圆形 (radius: 41.5px)
- **角度计算**：从1号孔开始，顺时针每22.5度一个孔位
- **位置计算**：使用椭圆参数方程精确定位

##### 序号显示规格
- **字体**：Helvetica, 30px, #81828B
- **尺寸**：width: 34px, height: 36px
- **位置**：在16个孔的内侧区域，距离圆边缘1px
- **偏移计算**：向椭圆中心方向偏移 (孔半径 + 1px + 文字半径)

##### 胚胎状态显示规格
通过5种微孔标记显示当前孔位的胚胎状态
- **空孔**：No_Embryo_Well.png
- **移植**：Accepted_Embryo.png
- **废弃**：Cancel_Embryo.png
- **冷冻**：Freeze_Embryo.png
- **移除**：Remove_Tag.png

##### 微孔标记显示规格
- **位置**：显示在每个孔的外延，![微孔标记](image-13.png)
- **对齐方式**：微孔标记的圆心和孔的圆心及16孔显示区域的圆心共线
- **尺寸**：width: 39px, height: 39px
- **动画效果**：无动画

##### 微孔标记交互逻辑

###### 空孔标记阶段
- **触发条件**：空孔标注确认区域显示时
- **交互方式**：点击孔的区域
- **功能**：显示/隐藏空孔标记
- **标记类型**：仅限空孔标记 (No_Embryo_Well.png)

###### 培养阶段标记选择
- **步骤1**：点击孔位 → 显示选中边框
- **边框样式**：
  ```css
  width: 83px;
  height: 83px;
  border: 4px solid #00FFFF;
  ```
- **步骤2**：在标记下拉框中选择标记类型
- **步骤3**：在选中孔的外延显示对应标记

##### 标记下拉框规格
- **显示条件**：有孔位被选中时显示
- **选项内容**：
  - 移植 (Accepted_Embryo.png)
  - 废弃 (Cancel_Embryo.png)
  - 冷冻 (Freeze_Embryo.png)
  - 移除 (Remove_Tag.png)
- **操作结果**：选择后在对应孔位外延显示标记

##### 孔位内容显示
- **胚胎阶段文字**：
  - 字体：Helvetica, 10px, #FFFFFF
  - 位置：孔位中心上方
- **质量等级文字**：
  - 字体：Helvetica, 14px, #FFFFFF, bold
  - 位置：孔位中心
- **选择标记**：
  - 文字："✓"
  - 字体：Helvetica, 16px, #FFFFFF, bold
  - 位置：孔位中心下方

##### 数据绑定
- **数据源**：`ChamberDataManager.getHoleData(chamberId)`
- **属性绑定**：hasEmbryo, embryoStage, quality, isSelected
- **实时更新**：监听Python后端数据变化信号

#### 1.3 开始录制按钮 ✅

##### 显示条件
- **默认状态**：不显示
- **显示条件**：空孔标记确认完成后显示
- **触发时机**：点击空孔确认OK按钮后

##### 按钮位置
- **定位方式**：在16孔区域中心显示 (anchors.centerIn: rightHoleArea)
- **相对位置**：16孔区域的几何中心点

##### 按钮规格
- **容器尺寸**：width: 176px, height: 73px
- **背景色**：transparent (透明背景)

##### 图标规格
- **图片源**：Start_Icon.png
- **尺寸**：width: 176px, height: 73px
- **填充模式**：Image.PreserveAspectFit
- **位置**：anchors.fill: parent

##### 文字规格
- **位置**：x: 70px, y: 22px (显示在图标之上)
- **尺寸**：width: 60px, height: 29px
- **字体**：Helvetica-Bold, 24px, Font.Bold
- **颜色**：#DA0000 (红色)
- **对齐**：水平居中，垂直居中
- **文字内容**：
  - 未录制状态："Start"
  - 录制中状态："Stop"

##### 交互功能
- **点击事件**：切换录制状态
- **状态管理**：isRecording 属性控制
- **鼠标区域**：anchors.fill: parent

##### 可见性控制
- **控制属性**：showStartRecording
- **显示条件**：emptyWellsConfirmed && hasPatientAssigned
- **隐藏条件**：未确认空孔或未分配患者

#### 1.4 引导文字区域 ✅
![Step1](image-9.png)
当前舱室未分配患者时，显示引导文字区域,默认显示该图标和文字

##### Step 1 引导区域

###### 显示条件
- **显示状态**：currentStep === 1
- **隐藏条件**：图像捕获中、空孔检测中、空孔已检测

###### 容器规格
- **位置**：x: 0px, y: 60px (相对于引导文字区域)
- **尺寸**：width: 300px, height: 200px
- **背景色**：transparent

###### 三角形图标规格
- **位置**：x: 2px, y: 101px (相对于Step1容器)
- **绝对位置**：x: 952px, y: 161px (相对于界面)
- **尺寸**：width: 15px, height: 38px
- **图片源**：triangle.png
- **填充模式**：Image.PreserveAspectFit
- **旋转角度**：-90deg (注释中，实际未应用)

###### 引导文字规格
- **位置**：x: 59px, y: 105px (相对于Step1容器)
- **绝对位置**：x: 1009px, y: 165px (相对于界面)
- **尺寸**：width: 250px, height: 116px
- **字体**：Helvetica, 24px, #FFFFFF
- **换行模式**：Text.WordWrap

###### 文字内容
```
Step 1 of 2

Press 'Image preview'
to get a preview of
all the wells and detect
any empty wells.
```

###### 显示逻辑
- **前置条件**：已分配患者
- **显示时机**：进入舱室详情界面初始状态
- **隐藏时机**：点击"Image preview"按钮后

##### Step 2 引导区域
![step2](image-10.png)

###### 显示条件
- **显示状态**：currentStep === 2 && emptyWellsConfirmed
- **隐藏条件**：调整图像过程中

###### 容器规格
- **位置**：x: 0px, y: 310px (相对于引导文字区域)
- **尺寸**：width: 300px, height: 200px
- **背景色**：transparent

###### 三角形图标规格
- **位置**：x: 2px, y: 67px (相对于Step2容器)
- **绝对位置**：x: 952px, y: 377px (相对于界面)
- **尺寸**：width: 15px, height: 38px
- **图片源**：triangle.png
- **填充模式**：Image.PreserveAspectFit
- **旋转角度**：-90deg (注释中，实际未应用)

###### 引导文字规格
- **位置**：x: 59px, y: 70px (相对于Step2容器)
- **绝对位置**：x: 1009px, y: 380px (相对于界面)
- **尺寸**：width: 250px, height: 116px
- **字体**：Helvetica, 24px, #FFFFFF
- **换行模式**：Text.WordWrap

###### 文字内容
```
Step 2 of 2

Press 'Adjust image' to adjust the camera and image settings.
```

###### 显示逻辑
- **前置条件**：空孔标注已确认
- **显示时机**：点击空孔确认OK按钮后
- **隐藏时机**：点击"Adjust image"按钮后

##### 图像捕获过程引导

###### 图像捕获交互流程图
以下流程图展示了从进入界面到完成空孔确认的完整交互流程：

```mermaid
flowchart TD
    A[进入舱室详情界面] --> B{是否已分配患者?}
    B -->|否| C[显示分配患者按钮]
    B -->|是| D[显示Step 1引导]

    C --> C1[点击分配患者] --> C2[弹出患者列表] --> C3[选择患者] --> D

    D --> E[显示Image Preview按钮<br/>显示Step 1引导文字]
    E --> F[点击Image Preview按钮]

    F --> G[开始图像捕获流程]
    G --> G1[设置 isCapturingImages = true]
    G1 --> G2[隐藏Step 1引导]
    G2 --> G3[显示图像捕获引导<br/>Images being captured...]
    G3 --> G4[显示旋转等待图标]

    G4 --> H[按序捕获16个孔位图像]
    H --> H1[拍照: z-stack_0基准焦平面]
    H1 --> H2[裁切1: 1472×1472]
    H2 --> H3[识别: well中心位置]
    H3 --> H4[裁切2: 928×928]
    H4 --> H5[映射: 显示到对应孔位]

    H5 --> I{16个孔位都完成?}
    I -->|否| H
    I -->|是| J[图像捕获完成]

    J --> J1[设置 isCapturingImages = false]
    J1 --> J2[隐藏图像捕获引导]
    J2 --> K[开始空孔检测流程]

    K --> K1[设置 isDetectingEmptyWells = true]
    K1 --> K2[显示空孔检测引导<br/>Empty wells being detected]
    K2 --> K3[显示旋转等待图标]
    K3 --> K4[AI算法识别空孔]

    K4 --> L[空孔检测完成]
    L --> L1[设置 isDetectingEmptyWells = false]
    L1 --> L2[设置 emptyWellsDetected = true]
    L2 --> L3[隐藏空孔检测引导]
    L3 --> M[显示空孔标注确认区域]

    M --> M1[显示空孔图标]
    M1 --> M2[显示确认文字]
    M2 --> M3[显示OK确认按钮]
    M3 --> M4[在检测到的空孔外围显示空孔标记]

    M4 --> N[用户可点击孔位修正标注]
    N --> O[点击OK确认按钮]

    O --> O1[设置 emptyWellsConfirmed = true]
    O1 --> O2[设置 currentStep = 2]
    O2 --> O3[隐藏空孔确认区域]
    O3 --> O4[显示Step 2引导]
    O4 --> O5[显示开始录制按钮]
    O5 --> P[Adjust Image按钮变为可用状态]

    style A fill:#e1f5fe
    style G fill:#fff3e0
    style K fill:#f3e5f5
    style M fill:#e8f5e8
    style P fill:#fff
```

###### 图像捕获流程
1. **拍照阶段**：按well1至well16顺序进行图像捕获，默认拍z-stack_0基准焦平面画面
2. **裁切1阶段**：按照"相机调整-对齐"设置的参数，裁切well画面为1472*1472
3. **识别阶段**：图像算法识别well中心位置
4. **裁切2阶段**：以算法模型返回的well中心位置为中心点，进行well图像裁切928*928
5. **映射阶段**：将裁切后图像映射至对应微孔中

###### 显示条件
- **显示状态**：isCapturingImages === true
- **触发时机**：点击"Image preview"按钮后

###### 容器规格
![图像捕获](image-12.png)
- **位置**：x: 0px, y: 207px (相对于引导文字区域)
- **尺寸**：width: 300px, height: 200px
- **背景色**：transparent

###### 捕获文字规格
- **位置**：width: 250px, height: 87px
- **绝对位置**：x: 1009px, y: 207px (相对于界面)
- **字体**：Helvetica, 24px, #FFFFFF
- **对齐**：水平居中，垂直居中
- **换行模式**：Text.WordWrap
- **布局**：Column布局，居中对齐，spacing: 20px

###### 文字内容
```
Images being
captured.
Please wait...
```

###### 等待图标规格
- **位置**：anchors.horizontalCenter: parent.horizontalCenter
- **绝对位置**：x: 1014px, y: 324px (相对于界面)
- **尺寸**：width: 59.5px, height: 58.95px
- **图片源**：wait.png
- **填充模式**：Image.PreserveAspectFit

###### 旋转动画
- **动画类型**：RotationAnimation
- **旋转范围**：from: 0, to: 360
- **动画时长**：duration: 2000ms
- **循环模式**：Animation.Infinite
- **运行条件**：running: isCapturingImages

###### 隐藏条件
- **隐藏时机**：16个孔的图像按序都显示完成后
- **状态变更**：isCapturingImages 设为 false

##### 空孔标记过程引导

###### 空孔标记流程
1. **识别阶段**：图像算法识别well区域是否存在胚胎
2. **标注阶段**：按照算法模型返回的结果，将无胚胎的well，自动标注上空孔标签
3. **确认阶段**：自动标注完成后，进入人工确认空孔页面，支持人工修正空孔标注结果

###### 显示条件
- **显示状态**：isDetectingEmptyWells === true
- **触发时机**：图像捕获完成后自动开始

###### 容器规格
![空孔识别](image-11.png)
- **位置**：x: 0px, y: 311px (相对于引导文字区域)
- **尺寸**：width: 300px, height: 200px
- **背景色**：transparent

###### 空孔识别文字规格
- **位置**：x: 59px, y: 0px (相对于检测容器)
- **绝对位置**：x: 1009px, y: 311px (相对于界面)
- **尺寸**：width: 250px, height: 87px
- **字体**：Helvetica, 24px, #FFFFFF
- **对齐**：水平居中，垂直居中
- **换行模式**：Text.WordWrap

###### 文字内容
```
Empty wells
being detected
```

###### 空孔识别图标规格
- **位置**：x: 154px, y: -89px (相对于检测容器)
- **绝对位置**：x: 1104px, y: 222px (相对于界面)
- **尺寸**：width: 59.5px, height: 58.95px
- **图片源**：wait.png
- **填充模式**：Image.PreserveAspectFit

###### 旋转动画
- **动画类型**：RotationAnimation
- **旋转范围**：from: 0, to: 360
- **动画时长**：duration: 2000ms
- **循环模式**：Animation.Infinite
- **运行条件**：running: isDetectingEmptyWells

###### 完成后状态变更
- **孔位标记**：识别为空孔的孔外围显示"空孔"图标
- **界面切换**：隐藏检测文字和图标，显示空孔标注确认区域
- **状态更新**：isDetectingEmptyWells = false, emptyWellsDetected = true

##### 空孔标注确认区域
![空孔标注](<Chamber - New - Image Capture 4 - empty wells.png>)

###### 显示条件
- **显示状态**：emptyWellsDetected && !emptyWellsConfirmed
- **触发时机**：空孔检测完成后

###### 容器规格
- **位置**：x: 0px, y: 0px (相对于引导文字区域)
- **尺寸**：width: 300px, height: 590px (覆盖整个引导区域)
- **背景色**：transparent

###### 空孔图标规格
- **位置**：x: 165px, y: 152px (相对于确认容器)
- **绝对位置**：x: 1115px, y: 152px (相对于界面)
- **尺寸**：width: 39px, height: 39px
- **图片源**：No_Embryo_Well.png
- **填充模式**：Image.PreserveAspectFit

###### 空孔标注文字规格
- **位置**：x: 59px, y: 221px (相对于确认容器)
- **绝对位置**：x: 1009px, y: 221px (相对于界面)
- **尺寸**：width: 250px, height: 150px
- **字体**：Helvetica, 24px, #FFFFFF
- **对齐**：水平居中，垂直顶部对齐
- **换行模式**：Text.WordWrap

###### 文字内容
```
Empty wells detected.

Tap on the well to
add or remove
the tag to confirm
the selection.
```

###### 确认按钮规格
- **位置**：x: 109px, y: 492px (相对于确认容器)
- **绝对位置**：x: 1059px, y: 492px (相对于界面)
- **尺寸**：width: 150px, height: 78px
- **背景色**：transparent

###### 确认按钮内容
- **布局**：Row布局，居中对齐，spacing: 24px
- **OK文字**：
  - 尺寸：width: 35px, height: 29px
  - 字体：Helvetica, 24px, #FFFFFF
  - 对齐：右对齐，垂直居中
- **OK图标**：
  - 尺寸：width: 60px, height: 60px
  - 图片源：OK_Icon_2.png
  - 填充模式：Image.PreserveAspectFit

###### 点击OK按钮后的状态变更
1. **隐藏元素**：空孔标注图标、文字、确认按钮均隐藏
2. **按钮状态**：adjustImage按钮变为可点击状态
3. **图标更新**：图片资源改为Adjust_Image_1.png
4. **界面切换**：显示Step 2引导文字区域
5. **功能激活**：显示开始录制图标
6. **状态更新**：emptyWellsConfirmed = true, currentStep = 2

##### 调整图像引导文字区域

###### 图像调整交互流程图
以下流程图展示了图像调整的完整交互流程：

```mermaid
flowchart TD
    A[空孔确认完成] --> B[显示Step 2引导]
    B --> C[Adjust Image按钮变为可用状态<br/>图标: Adjust_Image_1.png<br/>文字: 白色]

    C --> D[点击Adjust Image按钮]
    D --> E[开始图像调整流程]

    E --> E1[设置 isAdjustingImage = true]
    E1 --> E2[隐藏Step 2引导]
    E2 --> E3[显示调整图像引导文字<br/>Select wells 1, 5, 9 or 13...]

    E3 --> F[16孔状态变更]
    F --> F1[1、5、9、13号孔: 可点击状态]
    F --> F2[其他孔位: 灰色不可点击]
    F --> F3[默认选中1号孔<br/>显示青色边框 #00FFFF]

    F3 --> G[用户交互阶段]
    G --> G1[点击1号孔] --> H1[选中1号孔<br/>显示青色边框]
    G --> G2[点击5号孔] --> H2[选中5号孔<br/>显示青色边框]
    G --> G3[点击9号孔] --> H3[选中9号孔<br/>显示青色边框]
    G --> G4[点击13号孔] --> H4[选中13号孔<br/>显示青色边框]

    H1 --> I[相机调整功能]
    H2 --> I
    H3 --> I
    H4 --> I

    I --> I1[调整相机参数]
    I1 --> I2[调整图像设置]
    I2 --> I3[实时预览效果]

    I3 --> J{调整完成?}
    J -->|否| G
    J -->|是| K[完成图像调整]

    K --> K1[设置 isAdjustingImage = false]
    K1 --> K2[隐藏调整图像引导]
    K2 --> K3[恢复所有孔位正常状态]
    K3 --> K4[移除选中边框]

    K4 --> L[返回正常显示状态]
    L --> L1[显示开始录制按钮]
    L1 --> L2[等待用户开始录制]

    style A fill:#e8f5e8
    style E fill:#fff3e0
    style G fill:#f3e5f5
    style I fill:#e1f5fe
    style L fill:#fff
```

###### 显示条件
- **显示状态**：isAdjustingImage === true
- **触发时机**：点击"Adjust image"按钮后

###### 容器规格
- **位置**：x: 59px, y: 252px (相对于引导文字区域)
- **绝对位置**：x: 1009px, y: 252px (相对于界面)
- **尺寸**：width: 250px, height: 97px
- **背景色**：transparent

###### 引导文字规格
- **位置**：anchors.centerIn: parent
- **尺寸**：width: 250px, height: 87px
- **字体**：Helvetica, 24px, #FFFFFF
- **对齐**：左对齐，顶部对齐
- **换行模式**：Text.WordWrap

###### 文字内容
```
Select wells
1, 5, 9 or 13
to adjust image
```

###### 孔位交互状态
- **可点击孔位**：1、5、9、13号孔
- **不可点击孔位**：其他孔位变为灰色不可点击
- **默认选中**：1号孔为选中状态

###### 选中状态样式
- **边框规格**：
  - 尺寸：width: 83px, height: 83px
  - 边框：border: 4px solid #00FFFF (青色边框)
  - 位置：覆盖在孔位之上

###### 交互逻辑
- **点击事件**：只有1、5、9、13号孔响应点击
- **状态切换**：点击后切换选中状态
- **视觉反馈**：选中孔位显示青色边框
- **功能目的**：用于相机图像调整和对焦


图像调整操作区域![图像调整区域](image-14.png)
1.选中孔位的大尺寸图像（仅1、5、9、13号孔可选中）
1）序号显示（在最底层，根据点击的孔位显示对应的序号数字）
width: 56px;
height: 56px;
border: 4px solid #00FFFF;

font-family: Helvetica;
font-size: 30px;
color: #00FFFF;

2）胚胎图像（中间层，根据点击的孔位显示对应的胚胎图像，图像尺寸928*928，默认将图像居中填充显示）
x:1930
y:1
width: 588px;
height: 588px;

图像显示在一个直径为588的圆里，显示效果与16孔显示图像原理一致，
3）对齐辅助
width: 448px;
height: 448px;
border: 2px solid #00FFFF;

一个直径为448的圆环，位于胚胎图像的中间，用于辅助对齐
2.对齐
3.对焦
4.对比度

### 2. 中间层 - 图像控制区域 ✅

#### 2.1 图像控制区域结构
- **位置**：x: 20px, y: 110px
- **尺寸**：width: 950px, height: 590px
- **颜色**：#3D404D
- **圆角**：border-radius: 40px 0 0 40px

#### 2.2 图像控制按钮

##### 捕获图像按钮

###### 按钮容器规格
- **位置**：x: 754px, y: 130px (相对于图像控制区域)
- **尺寸**：width: 80px, height: 140px
- **背景色**：transparent

###### 图标规格
- **位置**：anchors.horizontalCenter: parent.horizontalCenter, anchors.top: parent.top
- **边距**：anchors.topMargin: 10px
- **尺寸**：width: 120px, height: 80px
- **图片源**：Image_Preview.png
- **填充模式**：Image.PreserveAspectFit

###### 标签文字规格
- **位置**：anchors.bottom: parent.bottom, anchors.horizontalCenter: parent.horizontalCenter
- **尺寸**：width: 120px, height: 58px
- **字体**：Helvetica, 24px, #FFFFFF
- **对齐**：水平居中，垂直居中
- **文字内容**：
```
Image
preview
```

###### 交互功能
- **点击事件**：startImageCaptureProcess()
- **功能描述**：启动图像捕获流程
- **状态管理**：设置 isCapturingImages = true

##### 调整图像按钮

###### 按钮容器规格
- **位置**：x: 754px, y: 342px (相对于图像控制区域)
- **尺寸**：width: 80px, height: 140px
- **背景色**：transparent

###### 图标规格
- **位置**：anchors.horizontalCenter: parent.horizontalCenter, anchors.top: parent.top
- **边距**：anchors.topMargin: 10px
- **尺寸**：width: 120px, height: 80px
- **图片源**：
  - 不可用状态：Adjust_Image.png
  - 可用状态：Adjust_Image_1.png
- **填充模式**：Image.PreserveAspectFit
- **透明度**：
  - 不可用状态：opacity: 0.5
  - 可用状态：opacity: 1.0

###### 标签文字规格
- **位置**：anchors.bottom: parent.bottom, anchors.horizontalCenter: parent.horizontalCenter
- **尺寸**：width: 120px, height: 58px
- **字体**：Helvetica, 24px
- **颜色**：
  - 不可用状态：#888888 (灰色)
  - 可用状态：#FFFFFF (白色)
- **对齐**：水平居中，垂直居中
- **文字内容**：
```
Adjust
image
```

###### 交互功能
- **启用条件**：emptyWellsConfirmed === true
- **点击事件**：startImageAdjustmentProcess()
- **功能描述**：启动图像调整流程
- **状态管理**：设置 isAdjustingImage = true

#### 2.3 阴影效果

##### 右侧阴影条规格
- **位置**：x: 920px, y: 0px (紧贴图像控制区域右边缘)
- **尺寸**：width: 30px, height: 590px
- **渐变方向**：Gradient.Horizontal (水平渐变)

##### 渐变停止点
- **GradientStop 0.0**：color: "#40000000" (25%透明度黑色)
- **GradientStop 0.3**：color: "#28000000" (15.6%透明度黑色)
- **GradientStop 0.6**：color: "#15000000" (8.2%透明度黑色)
- **GradientStop 1.0**：color: "#00000000" (完全透明)

##### 视觉效果
- **作用**：创建图像控制区域向右的柔和阴影过渡
- **目的**：增强层次感，与右侧16孔区域形成视觉分离
- **匹配度**：经过多次调整以完美匹配设计图效果

### 3. 顶层 - 患者数据显示区域 ✅

#### 3.1 患者数据区域结构
- **位置**：x: 20px, y: 0px
- **尺寸**：width: 708px, height: 590px
- **颜色**：#3D404D
- **圆角**：border-radius: 40px 0 0 40px (左侧圆角，右侧直角)
- **阴影**：渐变阴影效果

#### 3.2 舱室序号显示 ✅
![舱室号码区域——第3层](image-5.png)
- **位置**：x: 19px, y: -5px
- **尺寸**：width: 57px, height: 138px
- **字体**：Helvetica, 115px, #FFFFFF
- **字间距**：-7.5px
- **数据源**：从Python后端动态获取舱室编号

#### 3.3 患者信息区域 ✅
##### 姓名显示
- **支持模式**：中英文模式切换
  - 英文模式：名 + 姓 分行显示
    - 名：x: 143px, y: 16px
    - 姓：x: 143px, y: 66px
  - 中文模式：完整姓名单行显示
    - 姓名：x: 143px, y: 41px
- **字体**：Helvetica, 42px, #FFFFFF

##### 患者详细信息

###### 容器规格
- **位置**：x: 24px, y: 142px (相对于患者数据区域)
- **尺寸**：width: 660px, height: 280px
- **背景色**：transparent

###### 患者ID显示
- **ID1显示**：
  - 位置：x: 0px, y: 0px
  - 字体：Helvetica, 24px, 行高: 29px
  - 格式：富文本显示
  - 内容：`<span style='color: #81828B'>ID1: </span><span style='color: #FFFFFF'>[患者ID1]</span>`

- **ID2显示**：
  - 位置：x: 0px, y: 31px
  - 字体：Helvetica, 24px, 行高: 29px
  - 格式：富文本显示
  - 内容：`<span style='color: #81828B'>ID2: </span><span style='color: #FFFFFF'>[患者ID2]</span>`

###### 分隔线1
- **位置**：x: 0px, y: 69px
- **尺寸**：width: 660px, height: 1px
- **颜色**：#81828B, opacity: 0.3

###### 详细信息区域
- **位置**：x: 0px, y: 82px
- **尺寸**：width: 660px, height: 158px
- **布局**：Column布局, spacing: 2px

###### 详细信息项目
1. **出生日期**：
   - 格式：`<span style='color: #81828B'>Date of birth: </span><span style='color: #FFFFFF'>[出生日期]</span>`
   - 字体：Helvetica, 20px, 行高: 24px

2. **卵子年龄**：
   - 格式：`<span style='color: #81828B'>Egg age (Years): </span><span style='color: #FFFFFF'>[卵龄]</span>`
   - 字体：Helvetica, 20px, 行高: 24px

3. **发育时长**：
   - 格式：`<span style='color: #81828B'>Development hours: </span><span style='color: #FFFFFF'>[发育小时数]</span>`
   - 字体：Helvetica, 20px, 行高: 24px

4. **预计受精时间**：
   - 格式：`<span style='color: #81828B'>Estimated insemination time: </span><span style='color: #FFFFFF'>[受精时间]</span>`
   - 字体：Helvetica, 20px, 行高: 24px

5. **周期类型**：
   - 格式：`<span style='color: #81828B'>Cycle type: </span><span style='color: #FFFFFF'>[周期类型]</span>`
   - 字体：Helvetica, 20px, 行高: 24px

6. **Z-Stack数量**：
   - 格式：`<span style='color: #81828B'>Z-Stack number: </span><span style='color: #FFFFFF'>11</span>`
   - 字体：Helvetica, 20px, 行高: 24px

###### 分隔线2
- **位置**：x: 0px, y: 252px
- **尺寸**：width: 660px, height: 1px
- **颜色**：#81828B, opacity: 0.3

#### 3.4 环境监控数据 ✅
![舱室数据区域——第3层](image-4.png)
- **位置**：距离底部19px，距离左侧20px
- **尺寸**：width: 289px, height: 155px
- **实时数据同步**：与看板界面对应舱室数据完全同步

##### 三个监控区域

###### 1. 温度区域
- **位置**：x: 0px, y: 0px (相对于环境信息区域)
- **尺寸**：width: 191px, height: 49px
- **圆角**：radius: 24px (只保留左上圆角)
- **背景色**：transparent (初始状态)

**温度背景渐变**：
- **显示条件**：tempStatus !== "initial"
- **实现方式**：Canvas绘制-83度渐变
- **渐变角度**：-83度
- **起始位置**：0.04
- **颜色配置**：
  - 正常状态：#00A605 → #00C89B (绿色渐变)
  - 异常状态：#DA0000 → #FA007D (红色渐变)

**温度数值显示**：
- **位置**：左侧区域
- **字体**：Helvetica, 24px, #FFFFFF, bold
- **内容**：实时温度值 + "°C"

**温度状态指示器**：
- **位置**：右侧区域
- **尺寸**：width: 24px, height: 24px
- **形状**：圆形指示器
- **颜色**：根据温度状态动态变化

###### 2. 流量区域
- **位置**：x: 0px, y: 52px (相对于环境信息区域)
- **尺寸**：width: 191px, height: 49px
- **背景色**：根据流量状态显示渐变

**流量数值显示**：
- **字体**：Helvetica, 24px, #FFFFFF, bold
- **内容**：流量值 + 单位

**吹扫状态显示**：
- **位置**：右侧区域
- **显示内容**：吹扫状态指示
- **状态类型**：正常/异常/关闭

###### 3. 湿度区域
- **位置**：x: 0px, y: 104px (相对于环境信息区域)
- **尺寸**：width: 191px, height: 49px
- **背景色**：根据湿度状态显示

**湿度状态显示**：
- **字体**：Helvetica, 24px, #FFFFFF, bold
- **内容**：湿度状态文字
- **状态类型**：开启/关闭/异常

##### 报警/警告区域

###### 报警区域 (右侧上方)
- **位置**：x: 194px, y: 0px (相对于环境信息区域)
- **尺寸**：width: 95px, height: 76px
- **布局**：2x2网格布局

**报警类型**：
1. **开盖超时报警** (lidOpenAlarm)
2. **热切断报警** (thermalCutoffAlarm)
3. **舱室检修报警** (serviceAlarm)
4. **照明异常报警** (illuminationAlarm)

**报警显示规格**：
- **图标尺寸**：根据网格自适应
- **激活颜色**：红色背景
- **非激活颜色**：灰色背景

###### 警告区域 (右侧下方)
- **位置**：x: 194px, y: 79px (相对于环境信息区域)
- **尺寸**：width: 95px, height: 76px
- **布局**：2x1网格布局

**警告类型**：
1. **相机故障警告** (cameraOffline)
2. **舱室断联警告** (controllerOffline)

**警告显示规格**：
- **图标尺寸**：根据网格自适应
- **激活颜色**：橙色背景
- **非激活颜色**：灰色背景

#### 3.5 患者管理按钮 ✅

##### 患者管理交互流程图
以下流程图展示了患者管理的完整交互流程：

```mermaid
flowchart TD
    A[进入舱室详情界面] --> B{检查患者分配状态}

    B -->|未分配患者| C[显示分配患者按钮]
    B -->|已分配患者| D[显示移除患者+编辑患者按钮]

    C --> C1[分配患者按钮<br/>#00A605 绿色<br/>Allocate_Patient_2.png]
    C1 --> C2[点击分配患者按钮]
    C2 --> C3[弹出患者列表弹窗<br/>Chamber - New - Add Patient.png]

    C3 --> C4[显示未分配患者列表]
    C4 --> C5[用户选择患者]
    C5 --> C6{确认分配?}
    C6 -->|取消| C3
    C6 -->|确认| C7[分配患者到当前舱室]

    C7 --> C8[更新舱室数据]
    C8 --> C9[触发界面状态更新]
    C9 --> D

    D --> D1[移除患者按钮<br/>#DA0000 红色<br/>Delete_Patient_Icon.png]
    D --> D2[编辑患者按钮<br/>#0041EE 蓝色<br/>Edit_patient_icon.png]

    D1 --> D3[点击移除患者按钮]
    D3 --> D4[弹出移除患者确认弹窗<br/>Well Dish View - Remove Patient pop pup.png]
    D4 --> D5{确认移除?}
    D5 -->|取消| D
    D5 -->|确认| D6[移除患者分配]
    D6 --> D7[清空舱室患者数据]
    D7 --> D8[触发界面状态更新]
    D8 --> C

    D2 --> D9[点击编辑患者按钮]
    D9 --> D10[弹出编辑患者信息弹窗<br/>Chamber - New - Image Capture – Edit Patient.png]
    D10 --> D11[显示当前患者信息]
    D11 --> D12[用户编辑患者信息]
    D12 --> D13{确认保存?}
    D13 -->|取消| D
    D13 -->|保存| D14[更新患者信息]
    D14 --> D15[触发界面数据更新]
    D15 --> D

    D --> E[显示患者详细信息]
    E --> E1[舱室序号显示]
    E --> E2[患者姓名显示<br/>中英文模式切换]
    E --> E3[患者ID显示<br/>ID1、ID2]
    E --> E4[详细信息显示<br/>出生日期、卵龄等]
    E --> E5[环境监控数据显示]

    E --> F[启用图像控制功能]
    F --> F1[显示Image Preview按钮]
    F --> F2[显示Step 1引导]
    F --> F3[开始图像捕获流程]

    style A fill:#e1f5fe
    style C fill:#fff3e0
    style D fill:#e8f5e8
    style E fill:#f3e5f5
    style F fill:#fff
```

##### 按钮基础规格
- **位置**：x: 590px, y: 110px
- **尺寸**：width: 108px, height: 78px

##### 按钮类型

###### 分配患者按钮 (未分配患者时显示)
- **显示条件**：!hasPatientAssigned
- **位置**：x: 590px, y: 110px (相对于界面)
- **尺寸**：width: 108px, height: 78px
- **背景色**：#00A605 (绿色)
- **图标**：Allocate_Patient_2.png
- **文字**：
  - 内容："分配患者" / "Allocate Patient"
  - 字体：Helvetica, 18px, #FFFFFF
  - 位置：图标下方居中
- **交互功能**：
  - 点击事件：弹出患者列表弹窗
  - 弹窗内容：未分配患者列表
  - 选择结果：分配患者到当前舱室

###### 移除患者按钮 (已分配患者时显示)
- **显示条件**：hasPatientAssigned
- **位置**：x: 590px, y: 110px (相对于界面)
- **尺寸**：width: 108px, height: 78px
- **背景色**：#DA0000 (红色)
- **图标**：Delete_Patient_Icon.png
- **文字**：
  - 内容："移除患者" / "Remove Patient"
  - 字体：Helvetica, 18px, #FFFFFF
  - 位置：图标下方居中
- **交互功能**：
  - 点击事件：弹出移除患者确认弹窗
  - 确认内容：确认移除当前患者
  - 操作结果：清空舱室患者分配

###### 编辑患者按钮 (已分配患者时显示)
- **显示条件**：hasPatientAssigned
- **位置**：x: 590px, y: 200px (相对于界面，移除按钮下方)
- **尺寸**：width: 108px, height: 78px
- **背景色**：#0041EE (蓝色)
- **图标**：Edit_patient_icon.png
- **文字**：
  - 内容："编辑患者" / "Edit Patient"
  - 字体：Helvetica, 18px, #FFFFFF
  - 位置：图标下方居中
- **交互功能**：
  - 点击事件：弹出编辑患者信息弹窗
  - 编辑内容：当前患者的详细信息
  - 操作结果：更新患者信息

###### 编辑患者信息弹窗
整体效果![编辑患者](<Chamber - New - Image Capture – Edit Patient (1).png>)
点击编辑患者按钮时弹出该弹窗，在最顶层，弹出效果如患者列表从底部弹出方式一致；
点击弹窗中的输入控件后，右侧弹出键盘

- **弹窗样式参数**：
1.参考患者列表弹窗需求.md文件，背景尺寸、颜色、圆角、cancel按钮、OK按钮与患者列表弹窗一致！
2.标题文字改为患者姓名；标题图标规格改为Active_Patients.png
3.患者信息编辑区![患者信息](image-15.png)
分两列显示（标题：输入框控件及下拉框）
第一列：First names:、Last name:、ID1:、ID2:Date of birth:
第二列：Egg age (Years):、Cycle type:

标题样式：
width: 133px;
height: 29px;
font-family: Helvetica;
font-size: 24px;
color: #81828B;

输入框：
width: 620px;
height: 68px;
background: #FFFFFF;
border-radius: 34px;

输入文字：
width: 57px;
height: 29px;
font-family: Helvetica-Bold;
font-weight: 700;
font-size: 24px;
color: #000000;

光标：#008FB9


下拉框（Cycle type:）
显示内容从设置界面的中的周期类型模块获取（暂未实现）
默认显示内容：卵胞浆内单精子显微注射 (ICSI)、温热/解冻卵母细胞、体外人工授精(1VF)、温热/解冻第2天的胚胎、温热/解冻第3天的胚胎、温热/解冻第4天的胚胎、温热/解冻第5天的胚胎、温热/解冻第6天的胚胎、ICSI补救

样式参数：![周期类型下拉框](image-16.png)
width: 634px;
height: 68px;
background: #313542;
border-radius: 34px;

下拉项显示样式，![下拉项](image-17.png)
采用高斯模糊的样式 ，
width: 634px;
height: 68px;
border-radius: 34px;
中间有个分割线，长度为60

当下拉框显示时，三角图标变为向上，当下拉框合上后，显示向下
三角图标样式
width: 78px;
height: 78px;
resource：Down_Icon_1.png

###### 按钮状态管理
- **数据绑定**：监听 ChamberDataManager 的患者分配状态
- **动态切换**：根据 hasPatientAssigned 属性自动切换显示
- **实时更新**：患者分配状态变化时立即更新按钮显示

## 🔧 技术实现特性

### 数据同步机制 ✅
- **Python后端集成**：`import ChamberBackend 1.0`
- **实时数据绑定**：`ChamberDataManager.getChamberDataByDeviceId()`
- **信号槽机制**：监听数据变化并自动更新界面
- **属性绑定**：使用readonly property确保数据一致性

### 交互功能 ✅
- **孔位点击**：支持孔位选择和状态切换
- **按钮交互**：患者管理、图像控制功能
- **键盘支持**：ESC键返回上级界面
- **导航集成**：与看板界面无缝切换

### 视觉效果 ✅
- **精确圆角**：使用Canvas实现特殊圆角效果
- **渐变阴影**：柔和的阴影过渡效果
- **状态颜色**：根据胚胎质量动态显示颜色
- **悬停效果**：鼠标交互反馈

### 状态流程管理 ✅

#### 状态管理流程图
以下状态图展示了舱室详情界面的完整状态管理流程：

```mermaid
stateDiagram-v2
    [*] --> 初始状态

    初始状态 --> 未分配患者状态: hasPatientAssigned = false
    初始状态 --> 已分配患者状态: hasPatientAssigned = true

    未分配患者状态 --> 已分配患者状态: 分配患者成功
    已分配患者状态 --> 未分配患者状态: 移除患者成功

    已分配患者状态 --> Step1引导状态: currentStep = 1
    Step1引导状态 --> 图像捕获状态: 点击Image Preview

    图像捕获状态 --> 空孔检测状态: isCapturingImages = true
    空孔检测状态 --> 空孔确认状态: isDetectingEmptyWells = true
    空孔确认状态 --> Step2引导状态: emptyWellsConfirmed = true

    Step2引导状态 --> 图像调整状态: 点击Adjust Image
    图像调整状态 --> 录制准备状态: isAdjustingImage = true
    录制准备状态 --> 录制状态: 点击Start按钮

    录制状态 --> 录制准备状态: 点击Stop按钮

    state 未分配患者状态 {
        显示分配患者按钮
        隐藏图像控制区域
        隐藏引导文字区域
    }

    state 已分配患者状态 {
        显示移除患者按钮
        显示编辑患者按钮
        显示患者详细信息
        显示环境监控数据
    }

    state Step1引导状态 {
        显示Step1引导文字
        显示Image_Preview按钮
        currentStep_=_1
    }

    state 图像捕获状态 {
        isCapturingImages_=_true
        显示捕获进度文字
        显示旋转等待图标
        按序捕获16个孔位
    }

    state 空孔检测状态 {
        isDetectingEmptyWells_=_true
        显示检测进度文字
        显示旋转等待图标
        AI算法识别空孔
    }

    state 空孔确认状态 {
        emptyWellsDetected_=_true
        显示空孔图标和文字
        显示OK确认按钮
        用户可修正标注
    }

    state Step2引导状态 {
        currentStep_=_2
        显示Step2引导文字
        Adjust_Image按钮可用
        显示开始录制按钮
    }

    state 图像调整状态 {
        isAdjustingImage_=_true
        显示调整引导文字
        1_5_9_13孔可点击
        默认选中1号孔
    }

    state 录制准备状态 {
        显示Start按钮
        所有功能准备就绪
        等待用户开始录制
    }

    state 录制状态 {
        isRecording_=_true
        显示Stop按钮
        正在录制胚胎发育
    }
```

#### 状态流程简化版
```
初始状态 → 图像捕获中 → 空孔检测中 → 空孔标注确认 → Step 2引导 → 调整图像引导
```

## 📊 数据显示规格

### 患者信息 ✅
- **中英文姓名**：自动检测并切换显示模式
- **患者ID**：ID1、ID2 分别显示
- **详细信息**：6项完整患者数据
- **标签系统**：VIP、Priority等状态标签

### 环境数据 ✅
- **温度**：实时温度值 + 状态指示
- **流量**：流量值 + 吹扫状态
- **湿度**：湿度状态显示
- **报警/警告**：4项报警 + 2项警告状态

### 孔位数据 ✅
- **胚胎阶段**：发育阶段显示
- **质量等级**：A/B/C级别标识
- **选择状态**：选中标记显示
- **空孔检测**：自动识别空孔位

## 🎮 交互流程说明

### 图像捕获流程
1. **点击"Image preview"按钮** → 开始图像捕获
2. **显示捕获进度** → "Images being captured. Please wait..."
3. **空孔检测** → "Empty wells being detected"
4. **空孔标注确认** → 显示空孔图标和确认界面
5. **点击OK确认** → 进入Step 2，显示开始录制按钮

### 图像调整流程
1. **点击"Adjust image"按钮** → 开始图像调整
2. **显示调整引导** → "Select wells 1, 5, 9 or 13 to adjust image"
3. **孔位选择** → 只有1、5、9、13号孔可点击
4. **默认选中** → 1号孔显示青色边框

### 患者管理流程
1. **未分配状态** → 显示"分配患者"按钮
2. **点击分配** → 弹出患者列表弹窗
3. **已分配状态** → 显示"移除患者"和"编辑患者"按钮
4. **患者操作** → 对应的确认弹窗

## 📐 界面尺寸规格
- **总界面尺寸**：3840×720像素
- **背景色**：#1E2233
- **三层结构总宽度**：1930px (20px边距 + 1890px内容 + 20px边距)
- **各层高度**：590px
- **层间阴影**：30px宽度渐变阴影
