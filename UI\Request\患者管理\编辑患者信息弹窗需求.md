z# 编辑患者信息弹窗技术需求文档

## 📋 实现状态概览
- ✅ **弹窗基础结构** - 完全实现
- ✅ **动画效果** - 从底部渐入弹出
- ✅ **表单设计** - 7个输入字段
- ✅ **虚拟键盘集成** - 支持输入编辑
- ✅ **数据绑定** - 与患者数据同步
- ✅ **确认取消按钮** - 完整交互流程

## 🎯 弹窗功能概述

### 触发条件
- **触发位置**：舱室详情界面
- **触发操作**：点击"编辑患者"按钮
- **显示条件**：当前舱室已分配患者时

### 主要功能
1. **患者信息编辑**：编辑当前患者的详细信息
2. **表单验证**：输入数据的格式验证
3. **虚拟键盘**：集成键盘支持中英文输入
4. **数据保存**：保存修改后的患者信息
5. **取消编辑**：放弃修改，恢复原始数据

## 🏗️ 弹窗结构设计

### 整体布局规格
- **弹窗尺寸**：width: 1890px, height: 620px
- **弹窗位置**：x: 20px, y: 0px (左侧覆盖弹出)
- **弹窗颜色**：#313542 (与患者列表弹窗一致)
- **圆角设计**：radius: 40px (仅顶部圆角)
- **层级关系**：显示在舱室详情界面之上

### 弹出动画效果
```javascript
// 位置动画 - 从底部渐入到指定位置
y: isVisible ? 0 : 720  // 从屏幕底部弹出到y:0位置

// 动画参数
Behavior on y {
    NumberAnimation {
        duration: 300
        easing.type: Easing.OutCubic
    }
}

// 透明度动画
Behavior on opacity {
    NumberAnimation {
        duration: 300
        easing.type: Easing.OutCubic
    }
}
```

## 🎨 标题栏设计规格

### 标题栏整体规格
- **位置**：弹窗顶部
- **尺寸**：width: 1890px, height: 78px
- **背景色**：transparent
- **布局**：左中右三区域布局

### 标题图标规格
- **位置**：x: 856px, y: 8px 
- **尺寸**：width: 56.51 px, height: 63.96 px
- **图片源**：Active_Patients.png
- **填充模式**：Image.PreserveAspectFit

### 标题文字规格
- **位置**：图标右侧21px
- **尺寸**：width: 300px, height: 29px
- **字体**：Helvetica, 24px, #FFFFFF
- **内容**：患者姓名 (如 "Jessica Smith"),文字就是《舱室详情界面需求——重构版》中的3.3 患者信息区域 - 姓名显示 对应的内容
- **对齐**：水平居左，垂直居中

### Cancel按钮规格
- **位置**：左侧，距离边缘20px
- **尺寸**：width: 153px, height: 78px
- **布局**：Row布局，图标+文字
- **样式**：与患者列表弹窗完全一致

### OK按钮规格
- **位置**：右侧，距离边缘20px
- **尺寸**：width: 150px, height: 78px
- **启用条件**：默认可点击，无验证限制
- **样式**：与患者列表弹窗完全一致

## 📝 表单设计规格

### 表单整体布局
- **位置**：标题栏下方
- **尺寸**：width: 1860px, height: 520px
- **布局方式**：两列布局，每列内标签和输入框左右排列
- **列间距**：914px (第二列x:974 - 第一列x:60)
- **标签与输入框间距**：20px
- **行间距**：12px

### 第一列字段 (左侧)
- **列位置**：x: 60px, y: 40px
- **字段列表**：
  1. First name
  2. Last name
  3. ID1
  4. ID2
  5. Date of birth

### 第二列字段 (右侧)
- **列位置**：x: 974px, y: 40px
- **字段列表**：
  1. Egg age (Years)
  2. Cycle type

### 每行字段结构
- **标签宽度**：200px
- **输入框宽度**：620px
- **标签与输入框间距**：20px
- **行高**：68px

## 🏷️ 字段标签规格

### 标签样式规格
- **尺寸**：width: 200px, height: 68px (与输入框同高)
- **字体**：Helvetica, 24px
- **颜色**：#81828B (灰色)
- **对齐**：左对齐，垂直居中
- **位置**：输入框上方，间距10px

### 标签文字内容
- **First name:**
- **Last name:**
- **ID1:**
- **ID2:**
- **Date of birth:**
- **Egg age (Years):**
- **Cycle type:**

## 📱 输入框设计规格

### 输入框样式规格
- **尺寸**：width: 620px, height: 68px
- **背景色**：#FFFFFF (白色)
- **圆角**：border-radius: 34px
- **边框**：无边框
- **阴影**：轻微内阴影效果

### 输入文字规格
- **字体**：Helvetica-Bold, 24px, Font.Bold
- **颜色**：#000000 (黑色)
- **对齐**：左对齐，垂直居中
- **内边距**：左右各30px

### 光标样式
- **颜色**：#008FB9 (蓝色)
- **宽度**：2px
- **闪烁**：标准闪烁效果

### 占位符文字
- **字体**：Helvetica, 24px
- **颜色**：#CCCCCC (浅灰色)
- **内容**：对应字段的示例文本

## 📊 字段布局详细规格

### 第一列字段位置
```javascript
// First name
labelY: 120px, inputY: 159px

// Last name  
labelY: 227px, inputY: 266px

// ID1
labelY: 334px, inputY: 373px

// ID2
labelY: 441px, inputY: 480px

// Date of birth
labelY: 548px, inputY: 587px
```

### 第二列字段位置
```javascript
// Egg age (Years)
labelY: 120px, inputY: 159px

// Cycle type
labelY: 227px, inputY: 266px
```

### 字段间距规格
- **标签到输入框**：垂直间距10px
- **字段组间距**：垂直间距40px
- **列间距**：水平间距60px
- **边缘间距**：左右各60px

## 🎮 交互功能规格

### 输入框交互
- **获得焦点**：点击输入框，显示光标
- **失去焦点**：点击其他区域，隐藏光标
- **键盘弹出**：获得焦点时弹出虚拟键盘
- **文字选择**：支持文字选择和编辑

### 虚拟键盘集成
- **触发条件**：任意输入框获得焦点
- **键盘类型**：FixedDesignKeyboard组件
- **输入模式**：支持中英文输入
- **位置调整**：键盘弹出时调整弹窗位置

### 表单验证
- **实时验证**：输入时进行格式验证
- **错误提示**：无效输入时显示错误状态
- **提交验证**：点击OK时进行完整验证

## 📋 数据结构规格

### 患者数据格式
```javascript
{
    firstName: "Jessica",           // 患者名
    lastName: "Smith",              // 患者姓
    id1: "11456768-1-3",           // 患者ID1
    id2: "893456781-2-2",          // 患者ID2
    dateOfBirth: "26th Nov 1978",  // 出生日期
    eggAge: "45",                  // 卵子年龄
    cycleType: "Fresh"             // 周期类型
}
```

### 验证规则
- **firstName/lastName**：非空，1-50字符
- **id1/id2**：格式 "XXXXXXXX-X-X"
- **dateOfBirth**：日期格式验证
- **eggAge**：数字，范围18-60
- **cycleType**：预定义选项 (Fresh/Frozen)

## 🔧 技术实现规格

### 组件结构
```javascript
Rectangle {
    id: editPatientPopup
    
    // 基础属性
    property bool isVisible: false
    property var patientData: null
    property string chamberId: ""
    
    // 信号定义
    signal cancelled()
    signal confirmed(var updatedData)
    signal dataChanged()
}
```

### 状态管理
- **isVisible**：控制弹窗显示/隐藏
- **patientData**：当前编辑的患者数据
- **chamberId**：患者所在的舱室ID
- **hasChanges**：数据是否有修改

### 数据绑定
```javascript
// 加载患者数据
function loadPatientData(chamberId) {
    var chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberId)
    patientData = {
        firstName: chamberData.firstName,
        lastName: chamberData.lastName,
        id1: chamberData.id1,
        id2: chamberData.id2,
        dateOfBirth: chamberData.birthDate,
        eggAge: chamberData.eggAge,
        cycleType: chamberData.cycleType
    }
}
```

## 🎨 视觉设计规格

### 颜色规范
- **弹窗背景**：#313542 (深灰色)
- **输入框背景**：#FFFFFF (白色)
- **标签文字**：#81828B (浅灰色)
- **输入文字**：#000000 (黑色)
- **光标颜色**：#008FB9 (蓝色)
- **占位符**：#CCCCCC (浅灰色)

### 字体规范
- **标签字体**：Helvetica, 24px, normal
- **输入字体**：Helvetica-Bold, 24px, bold
- **标题字体**：Helvetica, 24px, normal

### 尺寸规范
- **弹窗尺寸**：1890×620px
- **输入框尺寸**：620×68px
- **标签尺寸**：200×29px
- **按钮尺寸**：与患者列表弹窗一致

## 🔄 工作流程

### 编辑流程
1. **触发编辑**：点击编辑患者按钮
2. **加载数据**：从ChamberDataManager获取患者数据
3. **显示弹窗**：弹窗从底部渐入显示
4. **填充表单**：将患者数据填充到输入框
5. **用户编辑**：用户修改输入框内容
6. **验证数据**：实时验证输入格式
7. **保存修改**：点击OK保存修改
8. **更新数据**：调用ChamberDataManager更新数据
9. **关闭弹窗**：弹窗渐出隐藏

### 取消流程
1. **点击取消**：用户点击Cancel按钮
2. **确认取消**：如有修改，显示确认对话框
3. **恢复数据**：恢复到原始数据状态
4. **关闭弹窗**：弹窗渐出隐藏

## 📱 响应式处理

### 键盘适配
- **键盘弹出**：弹窗向上移动200px
- **键盘收起**：弹窗恢复原始位置
- **输入框聚焦**：确保当前输入框可见

### 滚动处理
- **内容溢出**：表单区域支持垂直滚动
- **滚动条样式**：与患者列表弹窗一致
- **滚动动画**：平滑滚动效果

## 🧪 测试用例

### 基础功能测试
1. **弹窗显示**：点击编辑按钮，弹窗正确显示
2. **数据加载**：患者数据正确填充到表单
3. **输入编辑**：各输入框正常编辑功能
4. **数据保存**：修改后点击OK，数据正确保存

### 验证测试
1. **格式验证**：ID格式、日期格式验证
2. **必填验证**：必填字段的空值验证
3. **范围验证**：年龄范围等数值验证
4. **实时验证**：输入时的即时验证反馈

### 交互测试
1. **键盘集成**：虚拟键盘正确弹出和收起
2. **焦点管理**：输入框焦点切换正常
3. **取消操作**：取消时数据正确恢复
4. **动画效果**：弹窗动画流畅自然

## 📐 实际实现参数

### 组件文件位置
- **主组件**：`UI/views/Chamber/EditPatientPopup.qml`
- **集成位置**：`UI/views/Chamber/ChamberInfo.qml`
- **需求文档**：`UI/Request/患者管理/编辑患者信息弹窗需求.md`

### 实际布局参数
```javascript
// 弹窗主体
x: 20, y: isVisible ? 0 : 720
width: 1890, height: 620
color: "#313542", radius: 40

// 表单字段布局
firstColumn: x: 60, y: 40, spacing: 12
secondColumn: x: 974, y: 40, spacing: 12
fieldRow: spacing: 20 (标签与输入框间距)
labelWidth: 200, inputWidth: 620, rowHeight: 68

// 标题栏
width: 1890, height: 78

// 标题图标
x: 779, y: 18
width: 90.08, height: 41.98
source: "Active_Patients.png"

// 第一列字段位置
firstColumn: x: 60, y: 40, spacing: 40

// 第二列字段位置
secondColumn: x: 990, y: 40, spacing: 40

// 输入框规格
width: 620, height: 68
color: "#FFFFFF", radius: 34
font: "Helvetica-Bold", 24px, bold
```

### 集成接口实现
```javascript
// 在ChamberInfo.qml中的集成
EditPatientPopup {
    id: editPatientPopup

    onCancelled: {
        console.log("取消编辑患者信息")
    }

    onConfirmed: function(updatedData) {
        ChamberDataManager.updatePatientData(chamberId, updatedData)
    }
}

// 编辑按钮点击事件
onClicked: {
    editPatientPopup.show(chamberInfoPage.chamberId)
}
```

### 数据流程实现
1. **触发编辑**：点击编辑患者按钮
2. **加载数据**：`loadPatientData()` 从ChamberDataManager获取
3. **显示弹窗**：`show(chamberId)` 显示弹窗并填充数据
4. **用户编辑**：7个输入字段支持编辑
5. **保存数据**：`confirmed(updatedData)` 信号传递修改后的数据
6. **更新后端**：`ChamberDataManager.updatePatientData()` 更新数据

### 虚拟键盘集成
- **触发方式**：输入框获得焦点或双击弹窗空白区域
- **集成方法**：通过父组件的`showKeyboard()`方法
- **键盘类型**：FixedDesignKeyboard组件
- **输入支持**：中英文输入，实时更新

### 验证规则实现
```javascript
function hasValidData() {
    return firstName.length > 0 && lastName.length > 0
}
```

### 状态管理实现
- **isVisible**：控制弹窗显示/隐藏
- **patientData**：存储原始患者数据
- **表单属性**：firstName, lastName, id1, id2, dateOfBirth, eggAge, cycleType
- **动画效果**：300ms缓动动画，Easing.OutCubic

## 🎯 使用说明

### 开发者使用
1. **导入组件**：在需要的QML文件中导入EditPatientPopup
2. **添加实例**：创建EditPatientPopup实例
3. **连接信号**：处理cancelled和confirmed信号
4. **触发显示**：调用`show(chamberId)`方法

### 用户操作流程
1. **进入舱室详情**：选择已分配患者的舱室
2. **点击编辑按钮**：点击蓝色编辑患者按钮
3. **编辑信息**：在弹出的表单中修改患者信息
4. **保存修改**：点击OK按钮保存修改
5. **取消编辑**：点击Cancel按钮取消修改

### 注意事项
- **数据验证**：姓名字段为必填项
- **键盘支持**：支持虚拟键盘输入
- **ESC键**：支持ESC键关闭弹窗
- **数据同步**：修改后自动同步到后端数据
