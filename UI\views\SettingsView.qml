import QtQuick 2.15
import QtQuick.Controls 2.15

Page {
    id: settingsPage
    
    Rectangle {
        anchors.fill: parent
        color: "#1E2233"
        
        Text {
            anchors.centerIn: parent
            text: "系统设置页面"
            font.family: "Helvetica"
            font.pixelSize: 32
            font.weight: Font.Bold
            color: "#FFFFFF"
        }
        
        Text {
            anchors.centerIn: parent
            anchors.verticalCenterOffset: 50
            text: "Settings Content - 等待具体UI需求"
            font.family: "Helvetica"
            font.pixelSize: 18
            color: "#CCCCCC"
        }
    }
} 