# 患者界面需求文档

本文档详细描述了患者管理界面的功能需求。

## 1. 患者主界面 (`PatientsView.qml`)

**目标**: 提供患者管理功能的统一入口，允许用户查看不同类别的患者并添加新患者。

### 1.1. 界面布局与样式

#### 1.1.1. 根容器
- **尺寸**: 1920x720 像素
- **背景色**: `#1E2233`

#### 1.1.2. 添加患者按钮 (`addPatientButton`)
- **位置**: x=1630, y=11
- **尺寸**: 245.04x78 像素
- **背景**: 透明
- **布局**: 水平布局 (RowLayout)，间距为0
- **文本样式**:
    - **内容**: "Add Patient"
    - **字体**: Helvetica
    - **字号**: 24px
    - **颜色**: `#FFFFFF` (白色)
    - **首选宽度**: 125px
- **图标样式**:
    - **尺寸**: 108x78 像素
    - **资源**: `../../Resource/Image/Add_Patient.png`

#### 1.1.3. 主内容区 (`mainContent`)
- **位置**: x=35, y=91.5
- **尺寸**: 1860x620 像素
- **布局**: 垂直布局 (Column)，间距为1px

#### 1.1.4. 分割线
- **尺寸**: 1860x1 像素
- **颜色**: `#3D404D`

#### 1.1.5. 患者分类列表
- 列表包含三个分类项："Active Patients", "Unallocated Patients", "Historical Patients"。
- 每个分类项由 `CategoryItem.qml` 组件实现，具体样式见该组件的定义。

### 1.2. 交互功能
- **页面切换**: 点击任意主菜单按钮（如Dashboard, Patients, Alarms, Settings）切换到其他页面时，界面应立即切换，无滑动动画效果。
- **添加患者**: 点击“Add Patient”按钮，系统应立即弹出新建患者界面 (`AddPatientView`)。

## 2. 新建患者界面 (`AddPatientView.qml`)

**目标**: 提供一个表单，用于快速、准确地录入新患者的信息。

### 2.1. 界面布局与样式

#### 2.1.1. 根容器 (`addPatientView`)
- **位置**: x=20, y=0
- **尺寸**: 1890x620 像素
- **背景色**: `#1E2233`
- **圆角**: 40px (仅顶部)

#### 2.1.2. 标题栏 (`titleBar`)
- **尺寸**: 1890x78 像素
- **背景**: 透明

##### 标题图标 (`titleIcon`)
- **位置**: x=856, y=8
- **尺寸**: 90.08x41.98 像素
- **资源**: `../../Resource/Image/newPatient_icon.png`

##### 标题文字
- **位置**: 图标右侧，左边距21px
- **尺寸**: 300x29 像素
- **文本**: "New Patient"
- **字体**: Helvetica, 24px
- **颜色**: `#FFFFFF`

##### Cancel按钮 (`cancelButton`)
- **位置**: 左上角，左边距20px
- **尺寸**: 153x78 像素
- **背景**: 透明
- **图标**: 78x78 像素, 旋转90度, 资源: `../../Resource/Image/Cancel_Icon.png`
- **文本**: "Cancel", Helvetica 24px, `#FFFFFF`

##### OK按钮 (`confirmButton`)
- **位置**: 右上角，右边距20px
- **尺寸**: 150x78 像素
- **背景**: 透明
- **文本**: "OK", Helvetica 24px, `#FFFFFF`
- **图标**: 60x60 像素, 资源: `../../Resource/Image/OK_Icon_2.png`

#### 2.1.3. 表单区域 (`formArea`)
- **位置**: 标题栏下方
- **尺寸**: 1890x542 像素
- **背景**: 透明

#### 2.1.4. 表单项通用样式
- **标签 (Label)**:
    - **尺寸**: 200x68 像素
    - **字体**: Helvetica, 24px
    - **颜色**: `#81828B`
- **输入框 (TextInput)**:
    - **通用尺寸**: 620x68 像素
    - **背景色**: `#FFFFFF`
    - **圆角**: 34px
    - **内边距**: 左右各30px
    - **字体**: Helvetica-Bold, 24px, 加粗
    - **字体颜色**: `#000000`
    - **光标颜色**: `#008FB9`
- **出生日期输入框**:
    - **尺寸**: 171x68 像素

## 3. 分类项组件 (`CategoryItem.qml`)

**目标**: 为患者主界面提供一个可重用的、样式统一的列表项。

### 3.1. 界面布局与样式

#### 3.1.1. 根容器 (`itemRoot`)
- **尺寸**: 1860x78 像素
- **背景**: 透明

#### 3.1.2. 左侧图标 (`leftIcon`)
- **位置**: x=0, y=7
- **尺寸**: 79.82x63.96 像素

#### 3.1.3. 文本标签 (`label`)
- **位置**: x=95
- **尺寸**: 221x29 像素
- **字体**: Helvetica, 24px
- **颜色**: `#FFFFFF`

#### 3.1.4. 右侧导航图标 (`rightIcon`)
- **位置**: x=1782, y=2.5
- **尺寸**: 78x78 像素
- **资源**: `../../Resource/Image/Right_Navigation_Icon.png`

### 2.2. 交互功能
- **自动聚焦**: 进入该界面时，光标应自动聚焦在“First name”输入框内，并自动弹出虚拟键盘，以便用户立即输入。
- **确认与取消**:
    - 点击“OK”或“Cancel”按钮后，该界面应立即消失，无滑动动画效果。
    - 键盘应与界面一同消失。
- **数据提交**: 点击“OK”按钮后，系统应收集表单中的所有数据，并发送确认信号。
- **取消操作**: 点击“Cancel”按钮后，系统应发送取消信号，不做任何保存。

### 2.3. 周期类型选项
下拉选择框应包含以下选项：
- 卵胞浆内单精子显微注射 (ICSI)
- 温热/解冻卵母细胞
- 体外人工授精(IVF)
- 温热/解冻第2天的胚胎
- 温热/解冻第3天的胚胎
- 温热/解冻第4天的胚胎
- 温热/解冻第5天的胚胎
- 温热/解冻第6天的胚胎
- ICSI补救

### 2.4. 数据格式要求
- **日期格式**: 生日应格式化为"13th Dec 1981"的形式（日期+序数词+月份缩写+年份）。
- **数据验证**: 系统应确保所有必填字段都已填写。

## 3. 虚拟键盘集成

**目标**: 为患者信息录入提供便捷的输入方式。

### 3.1. 键盘行为
- **自动显示**: 当用户点击任何文本输入框时，虚拟键盘应自动显示。
- **自动隐藏**:
    - 当用户点击主菜单按钮切换页面时，键盘应立即隐藏。
    - 当用户点击"OK"或"Cancel"按钮时，键盘应与界面一同消失。
- **输入支持**: 键盘应支持中英文输入，包括候选词选择功能。

### 3.2. 用户体验
- **无动画**: 所有界面切换和键盘显示/隐藏都应立即完成，不使用滑动或淡入淡出动画。
- **焦点管理**: 系统应正确管理输入焦点，确保用户能够顺畅地在不同输入框之间切换。

## 4. 技术实现要求

### 4.1. 性能要求
- 界面切换应在100ms内完成。
- 键盘响应时间应在50ms内。

### 4.2. 兼容性要求
- 支持3840x720分辨率显示。
- 与现有的Home界面导航系统完全兼容。
