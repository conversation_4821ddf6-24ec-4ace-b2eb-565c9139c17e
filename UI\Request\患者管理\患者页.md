

![Active - Details](<Patient - Active - Details.png>) 
![Delete Overlay](<Patient - Delete Overlay.png>) 
![Unallocated - Details - Unlink - Confirm](<Patient - Unallocated - Details - Unlink - Confirm.png>)
![Unallocated - Details - Unlink](<Patient - Unallocated - Details - Unlink.png>) 
![Unallocated - Details](<Patient - Unallocated - Details.png>)
![History - Detail - Edit](<Patients -  History - Detail - Edit.png>) 
![Active - Detail - Edit](<Patients - Active - Detail - Edit.png>)
![Active](<Patients - Active.png>)
![History - Delete](<Patients - History - Delete.png>) 
![History](<Patients - History.png>)
![New Patient - Detail - Edit](<Patients - New Patient - Detail - Edit – 1.png>)
![Unallocated - Delete未选择](<Patients - Unallocated - Delete - 1.png>)
![Unallocated - Delete已选择](<Patients - Unallocated - Delete - 2.png>)
![ Unallocated - Detail - Edit](<Patients - Unallocated - Detail - Edit.png>) 
![Unallocated - Detail - Link未选择](<Patients - Unallocated - Detail - Link - 1.png>)
![Unallocated - Detail - Link已选择](<Patients - Unallocated - Detail - Link - 2.png>) 
![Unallocated - Link](<Patients - Unallocated - Link.png>)
![Unallocated](<Patients - Unallocated.png>) 

一、患者首页
1. [患者首页](Patients-01.png)
支持在Geri上，查看全部患者及患者详细。按照Unallocated、Active、History分类展示患者列表，列表内容包括名、姓、ID1、ID2、DOB、腔室编号（未分配至腔室的患者，默认腔室编号值为“？”）
a. 交互说明
点击“患者”，进入患者首页
点击患者分类，进入患者列表，查看该分类下的全部患者
轻击患者，进入患者详细，展示患者信息（姓、名、ID1、ID2、生日、卵龄、周期类型）
b. 分类定义
Unallocated：表示未分配至腔室（未与腔室绑定）
Active：表示已分配至腔室（与腔室绑定），包括未录制、录制中、迁移中的患者
History：表示已结束录制的患者
c. 列表排序

d. 界面样式
背景颜色：#1E2233
该区域的位置 x:35；y:81.5，height：620，width：1860
纵向排列的三个矩形,分别为Unallocated、Active、History,每个矩形的上下都有一个分割线共4个，与居中间隔1px
分割线：width: 1860px;height: 1px;border: 2px solid #3D404D;
矩形:   包括图标、文字、按钮，width: 1860px;height: 78px;整个矩形均可点击
图标1：width: 79.82px;height: 63.96px;x:0;y:7;
文字：width: 221px;height: 29px;font-family: Helvetica;font-size: 24px;color: #FFFFFF;
图标2：width: 78px;height: 78px;x:1782;y:2.5

以下是三个矩形的图片资源和文字：
Active_Patients.png、“Active Patients”、Right_Navigation_Icon
Unallocated_patients.Unallocated Patients、Right_Navigation_Icon
Historical_patients.png、“Historical Patients”、Right_Navigation_Icon

Add Patient按钮(文字+图标)
x:1630
y:11
width: 245.04px;
height: 78px;

文字：居左，上下居中
width: 125px;
height: 29px;
font-family: Helvetica;
font-size: 24px;
color: #FFFFFF;
text-align: right;
text:"Add Patient"
图标：居右
width: 108px;
height: 78px;
Add_Patient.png


2. 添加患者
点击Add Patient按钮，跳转添加患者界面
该界面（背景颜色：#1E2233）与编辑患者界面几乎一致,所有患者相关信息输入框和下拉框、cancel按钮、OK按钮、默认光标、默认弹出键盘均采用编辑患者界面的功能样式，除了以下内容：
1）背景颜色：#1E2233
2）标题文字不需要跟随输入的Firstname和lastName变化
3）标题图标：newPatient_icon.png


----------------------------------------------------------------

二、Unallocated Patients

![舱室详情页-未分配患者列表](image.png)

点击Active_Patients矩形区域，跳转只未分配舱室列表界面![Unallocated](<Patients - Unallocated.png>)  

该界面的所有功能与舱室详情界面中的患者列表弹窗大体一致，患者列表数据来源一致，不同点如下
1.cancel按钮改为返回患者首页按钮,
  width: 240px;
  height: 78px;
  包括图标（Left_Navigation_Icon.png）和文字（Patients）

2.OK按钮改为edit按钮
  width: 150px;
  height: 78px;
   包括图标（Edit_Icon.png）和文字（Edit）

3.显示位置为x:20,y:0,width: 1890px;height: 620px;
  背景颜色：#1E2233
  标题图标：Unallocated_patients.png

4.关联患者显示!![Unallocated - Link](<Patients - Unallocated - Link.png>)
  如果两个患者时关联关系，需要显示主从患者图标![主从](image-2.png)，在firstname和lastname中间添加一个关联图标列，不是关联的患者项不显示，关联字段的数据会跟随列表数据一起获取

  图标：width: 59.9px;height: 48.15px;
  主患者：Patient_link_1.png
  从患者：Patient_link_2.png

  我要将患者列表模块封装，只有功能按钮和标题是可替换项，其他的部分都是一致的


---------------------------------------------------------------------
x:20
y:1
width: 1890px;
height: 600px;
a. 交互说明
点击“患者”，进入患者首页
点击“添加”按钮，进入添加患者页面
输入患者信息（姓、名、ID1、ID2、生日、卵龄、周期类型）点击“OK”按钮，通过合规性验证，保存患者信息，返回患者首页
b. 输入规则
First names、Last names、ID1、ID2 四个字段至少填写一个
错误提示“缺少患者名称或ID 请提供姓名或ID，以识别时差成像录像” “Missing Patient Name or ID，Please provide either a Name or ID to identify the recorded time lapse images for this time lapse incubation”
项目	文本类型	值	备注
First names	/	不超过100字符，超出无法输入	支持输入多种语言：英语、日语、中文
Last names	/	不超过100字符，超出无法输入	
ID1	/	不超过100字符，超出无法输入	
ID2	/	不超过100字符，超出无法输入	
Date of birth	数字	可输入范围：日1～31、月1～12、年1～9999	
Egg age(Years)	数字	可输入范围：1～999
默认卵龄为患者的年龄，系统自动计算（系统日期-出生日期），支持修改卵龄（但无需反向修改出生日期）	只要修改过卵龄，则出生日期和卵龄2个字段立即解耦，互不干涉
Cycle type	选项	来源于“设置-周期类型”配置的周期类型参数	不设置默认项，提示“选择周期类型”
1. 编辑
支持在Geri上，编辑已录入的患者信息（任意状态下均可编辑）
a. 交互说明
点击“患者”，进入患者首页——点击患者分类，进入患者列表——轻击患者，进入患者详情——点击“编辑”按钮，调出键盘，进入编辑模式
b. 编辑规则
点击“OK”按钮，验证合规性通过，进入二次确认页面
 点击“OK”，确认修改，退出编辑模式
⚠️注意
编辑规则同“添加患者”的输入要求（Active/History Patients，卵龄不可编辑）
修改患者信息成功，需同步GCA
若GCA和Geri出现信息完全相同的患者，则两条数据均保留
1. 删除
支持删除Unallocated/History患者
交互说明
支持在患者列表页，点击“编辑”按钮，选中患者（单选），点击“删除”按钮，删除患者。
支持轻击患者，进入患者信息页，点击“删除”按钮，删除患者。
删除规则
点击“删除”按钮，判断图像数据上传完成，进入二次确认页面。
点击“确认”删除患者，返回患者列表页
⚠️注意
Networked Mode需进行图像数据上传状态检测，Standalone Mode无需检测
图像数据上传未完成需进行提示，允许用户选择继续删除或取消操作
删除患者成功，需同步GCA
1. 链接/取消链接
允许将两个患者链接，用于页面展示同一患者两个皿和导出报告时合并生成一份完整报告。同时，支持解除链接关系。
⚠️注意
仅Unallocated患者支持“链接/取消链接”操作
链接/取消链接，需同步更新患者列表显示和排序（隐藏/显示患者链接标记、插入/取消插入排序）
链接/取消链接，需同步GCA
5.1. 链接
a. 交互说明
点击“患者”按钮，进入患者首页——点击Unallocated患者分类，进入Unallocated患者列表——轻击患者A，进入患者A详细——点击“链接”按钮，显示Unallocated患者列表——选择患者B，点击“OK”按钮，进入二次确认——点击“OK”按钮，确认链接患者B，返回患者A详细页（更新患者A详细页，显示患者1标记、解除链接按钮）
b. 编号逻辑
编号顺序由操作选择决定，主动链接方始终优先获得1号培养皿编号。该规则仅影响系统显示，不涉及临床优先级。
主动链接方的培养皿微孔编号为 Well 1～Well 16
被动链接方的培养皿微孔编号为 Well 17～Well 32
例如：选择患者A链接患者B，则 患者A → Well 1～Well 16，患者B → Well 17～Well 32
⚠️注意
仅支持2位患者链接
5.2. 取消链接
a. 交互说明
点击“患者”按钮，进入患者首页——点击Unallocated患者分类，进入Unallocated患者列表——轻击主或从患者，进入主/从患者详细——点击“取消链接”按钮，弹窗二次确认——点击“OK”按钮，确认取消链接，返回主/从患者详细（更新主/从患者详细页，隐藏患者1/2标记，解除按钮变为链接按钮）
b. 取消规则
支持主/从患者双向解除（即可通过主或从患者触发解除）
解除链接，从患者的chamber页well编号需更新为well1～well16
1. 导出
支持在Geri上，导出患者报告和图像。导出需进行一些必要性验证并反馈结果：
验证USB插入状态、存储容量
展示导出进度
反馈导出结果（成功/失败）
6.1. 导出报告（PDF）
支持在Geri上，导出患者报告。
a. 交互说明
路径1（适配Active/History Patients）：点击“患者”，进入患者首页——点击Active/History Patients，进入患者列表——轻击患者，进入患者详细——点击Video按钮，进入Chamber页——轻击患者模块“导出”按钮，导出患者报告
路径2（适配Active Patients）：点击dashboard录制中腔室模块，进入Chamber页——轻击患者模块“导出”按钮，导出患者报告
b. 导出要求
导出需判断是否存在链接患者（将两个链接患者报告进行合并，生成一份完整的报告）
导出格式PDF
c. 导出内容（同Geri1.0）
项目	描述	备注
		
		
		
6.2. 导出图像（MP4）
支持在Geri上，导出图像。
交互说明
路径1（适配Active/History Patients）：点击“患者”，进入患者首页——点击Active/History Patients，进入患者列表——轻击患者，进入患者详细——点击Video按钮，进入Chamber页——轻击任意微孔，展开播放模块——点击播放模块“导出”按钮，导出视频
路径2（适配Active Patients）：点击dashboard录制中腔室模块，进入Chamber页——轻击任意微孔，展开播放模块——点击播放模块“导出”按钮，导出视频
导出内容
导出微孔图像存储路径下的图像数据