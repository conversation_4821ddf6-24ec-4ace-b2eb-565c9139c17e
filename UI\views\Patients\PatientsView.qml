import QtQuick 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    width: 1920
    height: 720
    color: "#1E2233"

    signal addPatientClicked()


    // Add Patient Button
    Rectangle {
        id: addPatientButton
        x: 1630
        y: 11
        width: 245.04
        height: 78
        color: "transparent"

        RowLayout {
            anchors.fill: parent
            spacing: 0

            Text {
                text: "Add Patient"
                font.family: "Helvetica"
                font.pixelSize: 24
                color: "#FFFFFF"
                verticalAlignment: Text.AlignVCenter
                horizontalAlignment: Text.AlignRight
                Layout.preferredWidth: 125
                Layout.fillHeight: true
            }

            Image {
                width: 108;
                height: 78;
                source: "../../Resource/Image/Add_Patient.png"
                fillMode: Image.PreserveAspectFit
            }
        }

        MouseArea {
            anchors.fill: parent
            cursorShape: Qt.PointingHandCursor
            onClicked: {
                root.addPatientClicked()
            }
        }
    }

    // Main Content Area
    Column {
        id: mainContent
        x: 35
        y: 91.5
        width: 1860
        height: 620
        spacing: 1

        // Separator
        Rectangle { width: 1860; height: 1; color: "#3D404D" }

        // Active Patients
        CategoryItem {
            icon: "../../Resource/Image/Active_Patients.png"
            text: "Active Patients"
            onClicked: { /* TODO */ }
        }

        // Separator
        Rectangle { width: 1860; height: 1; color: "#3D404D" }

        // Unallocated Patients
        CategoryItem {
            icon: "../../Resource/Image/Unallocated_patients.png"
            text: "Unallocated Patients"
            onClicked: {
                console.log("点击未分配患者")
                // 跳转到未分配患者界面
                if (typeof navigateToSubPage !== 'undefined') {
                    navigateToSubPage("Patients/UnallocatedPatientsView.qml")
                } else {
                    console.log("navigateToSubPage 函数不可用")
                }
            }
        }

        // Separator
        Rectangle { width: 1860; height: 1; color: "#3D404D" }

        // Historical Patients
        CategoryItem {
            icon: "../../Resource/Image/Historical_patients.png"
            text: "Historical Patients"
            onClicked: { /* TODO */ }
        }

        // Separator
        Rectangle { width: 1860; height: 1; color: "#3D404D" }
    }
}


