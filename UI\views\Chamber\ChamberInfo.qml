import QtQuick 2.15
import QtQuick.Controls 2.15
import ChamberBackend 1.0
import "../components"

Page {
    id: chamberInfoPage

    // 传入的参数
    property string chamberId: "A01"
    property string initialState: "unassigned"
    property var patientData: null

    // 内部状态管理
    property string currentState: initialState
    property bool showBackButton: true

    // 图像控制状态
    property bool imagePreviewCaptured: false  // 是否已捕获图像预览
    property int currentStep: 1  // 当前引导步骤 (1 或 2)

    // 图像捕获流程状态
    property bool isCapturingImages: false     // 是否正在捕获图像
    property bool isDetectingEmptyWells: false // 是否正在检测空孔
    property bool emptyWellsDetected: false    // 空孔检测是否完成
    property bool emptyWellsConfirmed: false   // 空孔标记是否确认完成
    property bool isAdjustingImage: false      // 是否正在调整图像

    // 标记选择状态
    property int selectedHoleIndex: -1         // 当前选中的孔位索引
    property bool showTagSelector: false       // 是否显示标记选择器
    property int selectedAdjustmentHole: 0     // 图像调整模式下选中的孔位（默认1号孔，索引0）

    // 孔位图像类型存储
    property var holeImageTypes: []            // 存储每个孔位的图像类型
    property var emptyWellIndexes: []          // 存储空孔的下标
    property var targetInputField: null        // 存储要激活的输入框

    Timer {
        id: keyboardTimer
        interval: 310 // 略大于弹窗动画时间
        repeat: false
        onTriggered: {
            if (targetInputField && targetInputField.visible) {
                targetInputField.forceActiveFocus()
                chamberKeyboard.showKeyboard(targetInputField)
            }
        }
    }

    // 查找包含导航函数的父级组件
    function findHomeInterface() {
        var stackView = chamberInfoPage.StackView.view
        if (stackView && stackView.parent) {
            return stackView.parent
        }
        return null
    }

    // 返回上级界面
    function navigateBack() {
        var homeInterface = findHomeInterface()
        if (homeInterface) {
            homeInterface.navigateBack()
        }
    }

    // 使用Home界面背景
    Rectangle {
        id: background
        anchors.fill: parent
        color: "#1E2233"  // Home界面的背景色

        
        // 底层
        Rectangle {
            id: bottomBackground
            x: 20
            y: 0
            width: 1890
            height: 590
            color: "transparent"

            // 添加宽度动画
            Behavior on width {
                NumberAnimation {
                    duration: 800  // 800毫秒的宽度变化动画
                    easing.type: Easing.InOutCubic
                }
            }

            // 使用Canvas实现精确的圆角效果
            Canvas {
                id: backgroundCanvas
                anchors.fill: parent

                // 动画属性：右侧圆角半径
                property real rightRadius: 295  // 初始右侧圆角295px

                // 强制重置函数
                function forceReset() {
                    // console.log("🔧 开始强制重置背景Canvas - 当前rightRadius:", rightRadius)
                    rightRadius = 295
                    requestPaint()
                    // console.log("🔧 完成强制重置背景Canvas - 新rightRadius:", rightRadius)
                }

                // 添加右侧圆角动画
                Behavior on rightRadius {
                    NumberAnimation {
                        duration: 800  // 800毫秒的圆角变化动画
                        easing.type: Easing.InOutCubic
                    }
                }

                onPaint: {
                    var ctx = getContext("2d");
                    ctx.reset();
                    ctx.fillStyle = "#313542";

                    // console.log("🎨 背景Canvas绘制 - rightRadius:", rightRadius, "width:", width, "height:", height)

                    // 测试：先绘制一个简单的矩形来确认Canvas工作
                    if (width <= 0 || height <= 0) {
                        console.log("❌ Canvas尺寸无效:", width, "x", height)
                        return
                    }

                    // 创建特殊圆角路径: 40px rightRadius rightRadius 40px
                    ctx.beginPath();

                    // 从左上角开始，顺时针绘制
                    // 左上角 40px 圆角
                    ctx.moveTo(40, 0);
                    ctx.lineTo(width - rightRadius, 0); // 上边到右上角圆角开始

                    // 右上角圆角（动态半径）
                    ctx.arcTo(width, 0, width, rightRadius, rightRadius);

                    // 右边到右下角圆角开始
                    ctx.lineTo(width, height - rightRadius);

                    // 右下角圆角（动态半径）
                    ctx.arcTo(width, height, width - rightRadius, height, rightRadius);

                    // 下边到左下角圆角开始
                    ctx.lineTo(40, height);

                    // 左下角 40px 圆角
                    ctx.arcTo(0, height, 0, height - 40, 40);

                    // 左边到左上角圆角开始
                    ctx.lineTo(0, 40);

                    // 左上角 40px 圆角
                    ctx.arcTo(0, 0, 40, 0, 40);

                    ctx.closePath();
                    ctx.fill();
                }

                // 当rightRadius变化时重新绘制
                onRightRadiusChanged: {
                    // console.log("📐 rightRadius变化:", rightRadius, "触发重新绘制")
                    requestPaint()
                }

                // Canvas初始化完成
                Component.onCompleted: {
                    // console.log("🎨 背景Canvas初始化完成 - rightRadius:", rightRadius, "width:", width, "height:", height)
                    requestPaint()
                }

                // 当Canvas尺寸变化时重新绘制
                onWidthChanged: {
                    if (width > 0) {
                        // console.log("📏 Canvas宽度变化:", width, "触发重新绘制")
                        requestPaint()
                    }
                }

                onHeightChanged: {
                    if (height > 0) {
                        // console.log("📏 Canvas高度变化:", height, "触发重新绘制")
                        requestPaint()
                    }
                }
            }

            // 右侧16孔显示区域 - 正方形背景，上下居中，距离底层背景右侧8px
            Rectangle {
                id: rightHoleArea
                // 正方形尺寸：574x574
                width: 574
                height: 574
                // 位置：距离底层背景右侧8px，上下居中
                x: 1305  // = 1328 (底层背景x + 底层背景宽度 - 正方形宽度 - 8px边距)
                y: 8    // 上下居中：(底层背景高度 - 正方形高度) / 2 = 8px

                // 与整体背景一致的颜色
                color: "#313542"

                // 添加右侧圆角以匹配背景Canvas
                radius: 0  // 默认无圆角

                // 动态圆角属性
                property real rightRadius: backgroundCanvas.rightRadius

                // 监听背景Canvas的圆角变化
                onRightRadiusChanged: {
                    // 始终应用圆角，减去8px边距以匹配位置偏移
                    radius = Math.max(0, rightRadius - 8)
                    // console.log("🔄 rightHoleArea圆角更新:", radius, "来源rightRadius:", rightRadius)
                }

                // 初始化时设置正确的圆角
                Component.onCompleted: {
                    // console.log("🔧 rightHoleArea初始化 - backgroundCanvas.rightRadius:", backgroundCanvas.rightRadius)
                    rightRadius = backgroundCanvas.rightRadius
                    radius = Math.max(0, rightRadius - 8)
                    // console.log("🔄 rightHoleArea初始圆角设置:", radius)
                }

                // 开始录制按钮 - 在16孔区域中心显示
                Rectangle {
                    id: startRecordingArea
                    // 居中定位：在16孔区域的中心
                    anchors.centerIn: parent
                    width: 176
                    height: 73
                    color: "transparent"

                    // 控制显示状态
                    property bool isRecording: false
                    property bool showStartRecording: emptyWellsConfirmed  // 当空孔标记确认完成后显示
                    visible: showStartRecording && ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId) && !isAdjustingImage  // 图像调整时隐藏

                    // 开始录制图标背景
                    Image {
                        id: startRecordingIcon
                        anchors.fill: parent
                        source: "../../Resource/Image/Start_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    // 开始/停止文字（显示在图标之上）
                    Text {
                        x:70
                        y:22
                        width: 60
                        height: 29
                        text: startRecordingArea.isRecording ? "Stop" : "Start"
                        font.family: "Helvetica"
                        font.weight: Font.Bold
                        font.pixelSize: 24
                        color: "#DA0000"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }

                    // 鼠标交互
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            startRecordingArea.isRecording = !startRecordingArea.isRecording
                            console.log("点击开始录制按钮，当前状态:", startRecordingArea.isRecording ? "录制中" : "已停止")
                        }
                    }
                }

                // 16个孔位容器 - 添加裁剪以确保圆角效果
                Rectangle {
                    id: holesContainer
                    anchors.fill: parent
                    color: "transparent"
                    clip: false  // 禁用裁剪以允许标记完整显示
                    radius: parent.radius  // 继承父容器的圆角

                    // 16个孔位，使用看板界面的精确算法
                    Repeater {
                        id: holeRepeater
                        model: 16

                    delegate: Rectangle {
                        id: holeItem
                        width: 83
                        height: 83

                        // 从Python后端获取孔位数据 - 使用函数调用确保数据更新
                        function updateHoleData() {
                            var holes = ChamberDataManager.getHoleData(chamberInfoPage.chamberId)
                            var data = holes && holes.length > index ? holes[index] : null
                            if (data) {
                                hasEmbryo = data.hasEmbryo || false
                                embryoStage = data.embryoStage || ""
                                quality = data.quality || ""
                                isSelected = data.isSelected || false
                                hasTag = data.hasTag || false
                                tagType = data.tagType || ""
                                isSelectedForTagging = data.isSelectedForTagging || false
                                // console.log("🔄 孔位", index + 1, "数据更新 - hasTag:", hasTag, "tagType:", tagType)
                            }
                        }

                        property bool hasEmbryo: false
                        property string embryoStage: ""
                        property string quality: ""
                        property bool isSelected: false

                        // 微孔标记相关属性 - 只在空孔标识阶段显示
                        property bool hasTag: false
                        property string tagType: ""
                        property bool isSelectedForTagging: false

                        // 调试：监听hasTag变化
                        onHasTagChanged: {
                            // console.log("🏷️ 孔位", index + 1, "hasTag变化:", hasTag, "tagType:", tagType)
                        }

                        // 初始化数据
                        Component.onCompleted: {
                            updateHoleData()
                        }

                        // 监听舱室数据变化
                        Connections {
                            target: ChamberDataManager
                            function onDataChanged(chamberIndex, deviceId) {
                                if (deviceId === chamberInfoPage.chamberId) {
                                    updateHoleData()
                                }
                            }
                        }

                        // 恢复孔位底层背景 - 使用设计规范颜色
                        color: "#3D404D"  // 设计规范的孔位背景色

                        // 调整图像阶段的灰化效果
                        opacity: {
                            if (isAdjustingImage) {
                                // 调整图像阶段：只有1、5、9、13号孔（索引0、4、8、12）保持100%不透明度
                                var adjustableHoles = [0, 4, 8, 12]  // 对应1、5、9、13号孔
                                return adjustableHoles.indexOf(index) !== -1 ? 1.0 : 0.1  // 可选中孔100%，其他孔10%（非常明显的灰化）
                            }
                            return 1.0  // 其他阶段正常显示
                        }

                        // 透明度变化动画
                        Behavior on opacity {
                            NumberAnimation {
                                duration: 400  // 400毫秒的透明度变化动画
                                easing.type: Easing.InOutQuad
                            }
                        }

                        radius: width / 2  // 圆形孔位 (确保完美圆形)

                        // 在574x574正方形区域内排列16个圆
                        // 基于四个关键孔位的精确位置计算椭圆参数

                        // 关键孔位的圆心坐标计算：
                        // 1号（上）：距离上边界20px，圆心Y坐标 = 20 + 83/2 = 61.5
                        // 5号（右）：距离右边界20px，圆心X坐标 = 574 - 20 - 83/2 = 512.5
                        // 9号（下）：距离下边界19px，圆心Y坐标 = 574 - 19 - 83/2 = 513.5
                        // 13号（左）：距离左边界19px，圆心X坐标 = 19 + 83/2 = 60.5

                        // 椭圆中心位置：X = (512.5 + 60.5) / 2 = 286.5，Y = (61.5 + 513.5) / 2 = 287.5
                        property real ellipseCenterX: (512.5 + 60.5) / 2  // = 286.5
                        property real ellipseCenterY: (61.5 + 513.5) / 2   // = 287.5

                        // 椭圆半径：radiusX = (512.5 - 60.5) / 2 = 226，radiusY = (513.5 - 61.5) / 2 = 226
                        property real radiusX: (512.5 - 60.5) / 2  // = 226
                        property real radiusY: (513.5 - 61.5) / 2   // = 226

                        // 角度计算：1号在正上方（-90度），顺时针排列
                        property real angle: ((index * 360 / 16) - 90) * Math.PI / 180

                        x: ellipseCenterX + radiusX * Math.cos(angle) - width / 2
                        y: ellipseCenterY + radiusY * Math.sin(angle) - height / 2

                        // 孔位样本图像显示 - 使用Canvas绘制圆形图片
                        Item {
                            id: sampleImageContainer
                            objectName: "sampleImageContainer"  // 用于查找
                            anchors.centerIn: parent
                            width: parent.width - 4  // 比孔位稍小，确保在圆形内
                            height: parent.height - 4
                            visible: sampleImage.source !== ""
                            z: 0.5  // 在孔位背景之上，但在序号和标记之下

                            // 隐藏的Image用于加载图片
                            Image {
                                id: sampleImage
                                objectName: "sampleImage"  // 用于查找
                                visible: false
                                source: {
                                    // 只有在图像捕获后才显示图像
                                    if (!chamberInfoPage.imagePreviewCaptured) {
                                        return ""
                                    }
                                    var holes = ChamberDataManager.getHoleData(chamberInfoPage.chamberId)
                                    var data = holes && holes.length > index ? holes[index] : null
                                    return data && data.imagePath ? data.imagePath : ""
                                }
                                onStatusChanged: {
                                    if (status === Image.Ready) {
                                        canvas.requestPaint()
                                    }
                                }
                            }

                            // Canvas绘制圆形图片
                            Canvas {
                                id: canvas
                                anchors.fill: parent
                                onPaint: {
                                    if (sampleImage.status !== Image.Ready) return

                                    var ctx = getContext("2d")
                                    ctx.clearRect(0, 0, width, height)

                                    // 保存当前状态
                                    ctx.save()

                                    // 创建圆形裁剪路径
                                    ctx.beginPath()
                                    ctx.arc(width/2, height/2, width/2, 0, 2 * Math.PI)
                                    ctx.clip()

                                    // 绘制图片，保持比例并居中
                                    var imgWidth = sampleImage.implicitWidth
                                    var imgHeight = sampleImage.implicitHeight
                                    var scale = Math.max(width / imgWidth, height / imgHeight)
                                    var scaledWidth = imgWidth * scale
                                    var scaledHeight = imgHeight * scale
                                    var x = (width - scaledWidth) / 2
                                    var y = (height - scaledHeight) / 2

                                    ctx.drawImage(sampleImage, x, y, scaledWidth, scaledHeight)

                                    // 恢复状态
                                    ctx.restore()
                                }
                            }
                        }

                        // 微孔标记 - 根据样本图片名称自动显示空孔标识
                        Image {
                            id: embryoTag
                            width: 39
                            height: 39
                            z: 10  // 确保标记显示在选中边框上面

                            // 记录当前显示的标记类型，用于淡出动画
                            property string displayedTagType: ""

                            // 检查是否为空孔的函数
                            function checkIsEmptyWell() {
                                // 获取当前孔位的图片路径
                                var holes = ChamberDataManager.getHoleData(chamberInfoPage.chamberId)
                                var data = holes && holes.length > index ? holes[index] : null
                                var imagePath = data && data.imagePath ? data.imagePath : ""
                                // console.log("🔍 检查空孔 - 孔位:", index + 1, "图片路径:", imagePath)

                                // 检查是否为空孔：只有以"Empty_"开头的才是空孔，"noEmpty_"不是空孔
                                var isEmpty = imagePath.includes("Empty_") && !imagePath.includes("noEmpty_")
                                // console.log("🔍 是否空孔:", isEmpty)
                                return isEmpty
                            }

                            property bool isEmptyWell: false

                            // 不在初始化时检查空孔，只有在捕获图像后才检查
                            Component.onCompleted: {
                                // console.log("🚀 标记组件初始化 - 孔位:", index + 1, "不进行空孔检查")
                                // 初始化时不检查空孔，isEmptyWell保持false
                                // console.log("标记组件创建 - 孔位:", index + 1, "visible:", visible, "source:", source)
                            }

                            // 监听数据变化，重新计算空孔状态
                            Connections {
                                target: ChamberDataManager
                                function onDataChanged(chamberIndex, deviceId) {
                                    if (deviceId === chamberInfoPage.chamberId) {
                                        // console.log("📡 数据变化，重新检查空孔 - 孔位:", index + 1)
                                        embryoTag.isEmptyWell = embryoTag.checkIsEmptyWell()
                                    }
                                }
                            }

                            // 根据标记类型决定是否显示
                            visible: holeItem.hasTag && holeItem.tagType !== ""
                            opacity: (holeItem.hasTag && holeItem.tagType !== "") ? 1.0 : 0.0

                            // 计算标记图片源的函数
                            function calculateTagSource() {
                                // console.log("标记源计算 - 孔位:", index + 1, "isEmptyWell:", isEmptyWell, "hasTag:", holeItem.hasTag, "tagType:", holeItem.tagType)

                                // 优先显示手动标记
                                if (holeItem.hasTag) {
                                    switch(holeItem.tagType) {
                                        case "empty": return "../../Resource/Image/No_Embryo_Well.png"
                                        case "accepted": return "../../Resource/Image/Accepted_Embryo.png"
                                        case "cancel": return "../../Resource/Image/Cancel_Embryo.png"
                                        case "freeze": return "../../Resource/Image/Freeze_Embryo.png"
                                        case "remove": return "../../Resource/Image/Remove_Tag.png"
                                        default: return ""
                                    }
                                }
                                // 如果没有手动标记但是空孔，显示空孔标识
                                else if (isEmptyWell) {
                                    return "../../Resource/Image/No_Embryo_Well.png"
                                }

                                return ""
                            }

                            source: calculateTagSource()
                            fillMode: Image.PreserveAspectFit

                            // 标记显示/隐藏的淡入淡出动画效果
                            Behavior on opacity {
                                NumberAnimation {
                                    duration: holeItem.hasTag ? 200 : 400  // 显示200毫秒，消失400毫秒
                                    easing.type: Easing.InOutQuad  // 使用更平滑的缓动

                                    // 动画完成后清理
                                    onFinished: {
                                        if (!holeItem.hasTag && embryoTag.opacity === 0.0) {
                                            embryoTag.displayedTagType = ""  // 清空显示的标记类型
                                        }
                                    }
                                }
                            }

                            // 计算标记位置：40%面积与孔重合
                            property real holeRadius: parent.width / 2  // 孔半径 = 41.5px
                            property real tagRadius: width / 2  // 标记半径 = 19.5px
                            // 40%重合意味着标记中心距离孔中心 = 孔半径 + 标记半径 * 0.2
                            property real tagDistance: holeRadius + tagRadius * 0.2  // 约45.4px

                            // 计算从椭圆中心到孔中心的方向向量
                            property real holeX: parent.x + parent.width/2
                            property real holeY: parent.y + parent.height/2
                            property real directionX: (holeX - ellipseCenterX)
                            property real directionY: (holeY - ellipseCenterY)
                            property real directionLength: Math.sqrt(directionX * directionX + directionY * directionY)
                            property real normalizedX: directionX / directionLength
                            property real normalizedY: directionY / directionLength

                            // 标记位置：孔中心 + 向外偏移（确保1/4重合）
                            x: parent.width/2 - width/2 + normalizedX * tagDistance
                            y: parent.height/2 - height/2 + normalizedY * tagDistance


                        }

                        // 选中边框 - 培养阶段选中孔位时显示
                        Rectangle {
                            id: selectionBorder
                            width: 83
                            height: 83
                            anchors.centerIn: parent
                            color: "transparent"
                            border.width: 4
                            border.color: "#00FFFF"
                            radius: width / 2  // 确保完美圆形
                            visible: holeItem.isSelectedForTagging || (isAdjustingImage && index === selectedAdjustmentHole)
                            z: 5  // 确保选中边框在标记下面
                        }

                        // 序号文字 - 显示在16个孔的内侧区域，但在每个圆的外面，距离圆边缘1px
                        Text {
                            width: 34
                            height: 36
                            text: index + 1
                            font.family: "Helvetica"
                            font.pixelSize: 30
                            color: {
                                if (isAdjustingImage) {
                                    // 图像调整模式下的颜色逻辑
                                    if (index === selectedAdjustmentHole) {
                                        return "#00FFFF"  // 选中的孔：青色
                                    } else if (index === 0 || index === 4 || index === 8 || index === 12) {
                                        return "#FFFFFF"  // 可点击的孔（1、5、9、13）：白色
                                    } else {
                                        return "#81828B"  // 其他孔：默认灰色
                                    }
                                } else {
                                    return "#81828B"  // 非调整模式：默认灰色
                                }
                            }
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter

                            // 计算文字位置：在16个孔的内侧区域，但在每个圆的外面，距离圆边缘1px
                            property real holeRadius: parent.width / 2  // 孔的半径 = 41.5px
                            property real offsetFromEdge: 1  // 距离圆边缘1px
                            property real textRadius: 18  // 文字区域的半径
                            property real totalOffset: holeRadius + offsetFromEdge + textRadius  // 向椭圆中心方向偏移距离

                            // 计算从椭圆中心到孔中心的方向向量
                            property real holeX: parent.x + parent.width/2
                            property real holeY: parent.y + parent.height/2
                            property real directionX: (holeX - ellipseCenterX)
                            property real directionY: (holeY - ellipseCenterY)
                            property real directionLength: Math.sqrt(directionX * directionX + directionY * directionY)
                            property real normalizedX: directionX / directionLength
                            property real normalizedY: directionY / directionLength

                            // 文字位置：孔的中心 - 向椭圆中心方向偏移（在16个孔的内侧区域，但在圆外）
                            x: parent.width/2 - width/2 - normalizedX * totalOffset
                            y: parent.height/2 - height/2 - normalizedY * totalOffset
                        }

                        // 鼠标交互 - 支持不同阶段的标记功能，设置高z-index确保在最上层
                        MouseArea {
                            anchors.fill: parent
                            z: 100  // 确保在所有其他元素之上
                            enabled: true  // 明确启用
                            hoverEnabled: true  // 启用悬停

                            onClicked: {
                                console.log("🔥 点击了孔位", index + 1, "- 点击事件触发成功!")
                                console.log("状态检查 - emptyWellsDetected:", emptyWellsDetected, "emptyWellsConfirmed:", emptyWellsConfirmed)

                                if (emptyWellsDetected && !emptyWellsConfirmed) {
                                    // 空孔标记阶段：切换空孔标记
                                    console.log("执行空孔标记切换")
                                    toggleEmptyWellTag(index)
                                } else if (emptyWellsConfirmed && !isAdjustingImage) {
                                    // 培养阶段：选中孔位进行标记选择
                                    console.log("执行培养阶段标记选择")
                                    selectHoleForTagging(index)
                                } else if (isAdjustingImage && (index === 0 || index === 4 || index === 8 || index === 12)) {
                                    // 图像调整阶段：只有1、5、9、13号孔可点击
                                    console.log("执行图像调整阶段选择")
                                    selectHoleForAdjustment(index)
                                } else {
                                    console.log("没有匹配的点击处理条件")
                                }
                            }

                            onEntered: {
                                // console.log("🖱️ 鼠标进入孔位", index + 1)
                            }

                            onExited: {
                                // console.log("🖱️ 鼠标离开孔位", index + 1)
                            }
                        }
                    }
                }
                }  // holesContainer 结束
            }

            // 底层引导文字区域
            Rectangle {
                id: guideTextArea
                x: 950
                y: 0
                width: 300
                height: 590
                color: "transparent"
                property int visibilityRefresh: 0
                visible: {
                    visibilityRefresh; // 强制依赖刷新计数器
                    var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                    // console.log("引导文字区域可见性计算:", hasPatient, "刷新计数:", visibilityRefresh)
                    return hasPatient  // 只在已分配患者时显示
                }

                // Step 1 引导区域
                Rectangle {
                    id: step1Guide
                    x: 0
                    y: 60
                    width: 300
                    height: 200
                    color: "transparent"
                    visible: currentStep === 1 && !isCapturingImages && !isDetectingEmptyWells && !emptyWellsDetected

                    // 三角形图标
                    Image {
                        id: step1Triangle
                        x: 2  // x:952 - x:950 = 2
                        y: 101  // y:161 - y:60 = 101
                        width: 15
                        height: 38
                        source: "../../Resource/Image/triangle.png"
                        fillMode: Image.PreserveAspectFit
                        //rotation: -90
                    }

                    // Step 1 文字
                    Text {
                        x: 59  
                        y: 105
                        width: 250
                        height: 116
                        text: "Step 1 of 2\n\nPress 'Image preview'\nto get a preview of\nall the wells and detect\nany empty wells."
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        wrapMode: Text.WordWrap
                    }
                }

                // Step 2 引导区域
                Rectangle {
                    id: step2Guide
                    x: 0
                    y: 310
                    width: 300
                    height: 200
                    color: "transparent"
                    visible: currentStep === 2 && emptyWellsConfirmed && !isAdjustingImage  // 调整图像时隐藏Step 2

                    // 三角形图标
                    Image {
                        id: step2Triangle
                        x: 2  // x:952 - x:950 = 2
                        y: 67  // y:375 - y:310 = 65
                        width: 15
                        height: 38
                        source: "../../Resource/Image/triangle.png"
                        //fillMode: Image.PreserveAspectFit
                        //rotation: -90
                    }

                    // Step 2 文字
                    Text {
                        x: 59 
                        y: 70 
                        width: 250
                        height: 116
                        text: "Step 2 of 2\n\nPress 'Adjust image' to adjust the camera and image settings."
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        wrapMode: Text.WordWrap
                    }
                }

                // 图像捕获过程引导区域
                Rectangle {
                    id: capturingGuide
                    x: 0
                    y: 207
                    width: 300
                    height: 200
                    color: "transparent"
                    visible: isCapturingImages

                    // 使用Column布局避免重叠
                    Column {
                        anchors.centerIn: parent
                        spacing: 20

                        // 捕获文字
                        Text {
                            width: 250
                            height: 87
                            text: "Images being\ncaptured.\nPlease wait..."
                            font.family: "Helvetica"
                            font.pixelSize: 24
                            color: "#FFFFFF"
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                            wrapMode: Text.WordWrap
                        }

                        // 捕获等待图标（定时旋转）
                        Image {
                            id: capturingWaitIcon
                            width: 59.5
                            height: 58.95
                            source: "../../Resource/Image/wait.png"
                            fillMode: Image.PreserveAspectFit
                            anchors.horizontalCenter: parent.horizontalCenter

                            // 旋转动画
                            RotationAnimation {
                                target: capturingWaitIcon
                                property: "rotation"
                                from: 0
                                to: 360
                                duration: 2000
                                loops: Animation.Infinite
                                running: isCapturingImages
                            }
                        }
                    }
                }

                // 空孔检测过程引导区域
                Rectangle {
                    id: detectingGuide
                    x: 0
                    y: 311
                    width: 300
                    height: 200
                    color: "transparent"
                    visible: isDetectingEmptyWells

                    // 空孔识别文字
                    Text {
                        x: 59  // x:1009 - x:950 = 59
                        y: 0   // y:311 - y:311 = 0
                        width: 250
                        height: 87
                        text: "Empty wells\nbeing detected"
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                        wrapMode: Text.WordWrap
                    }

                    // 空孔识别图标（定时旋转）- 按需求文档精确定位
                    Image {
                        id: detectingWaitIcon
                        x: 154  // x:1104 - x:950 = 154
                        y: -89  // y:222 - y:311 = -89
                        width: 59.5
                        height: 58.95
                        source: "../../Resource/Image/wait.png"
                        fillMode: Image.PreserveAspectFit

                        // 旋转动画
                        RotationAnimation {
                            target: detectingWaitIcon
                            property: "rotation"
                            from: 0
                            to: 360
                            duration: 2000
                            loops: Animation.Infinite
                            running: isDetectingEmptyWells
                        }
                    }
                }

                // 空孔标注确认区域
                Rectangle {
                    id: emptyWellsConfirmArea
                    x: 0
                    y: 0
                    width: 300
                    height: 590
                    color: "transparent"
                    visible: emptyWellsDetected && !emptyWellsConfirmed

                    // 空孔图标 - 按设计稿位置调整
                    Image {
                        id: emptyWellIcon
                        x: 165 // x:1115 - x:950 = 165
                        y: 152   // 向上移动，与文字分开
                        width: 39
                        height: 39
                        source: "../../Resource/Image/No_Embryo_Well.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    // 空孔标注文字 - 按设计稿位置调整
                    Text {
                        x: 59  // x:1009 - x:950 = 59
                        y: 221  // 向下移动，与图标分开
                        width: 250
                        height: 150
                        text: "Empty wells detected.\n\nTap on the well to\nadd or remove\nthe tag to confirm\nthe selection."
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignTop
                        wrapMode: Text.WordWrap
                    }

                    // 确认按钮 - 复用UnallocatedPatientsList的样式
                    Rectangle {
                        id: confirmButton
                        x: 109 // x:1059 - x:950 = 109
                        y: 492 // y:400 - y:152 = 248
                        width: 150
                        height: 78
                        color: "transparent"

                        Row {
                            anchors.centerIn: parent
                            spacing: 24

                            Text {
                                anchors.verticalCenter: parent.verticalCenter
                                width: 35
                                height: 29
                                font.family: "Helvetica"
                                font.pixelSize: 24
                                color: "#FFFFFF"
                                text: "OK"
                                horizontalAlignment: Text.AlignRight
                                verticalAlignment: Text.AlignVCenter
                            }

                            Image {
                                anchors.verticalCenter: parent.verticalCenter
                                width: 60
                                height: 60
                                source: "../../Resource/Image/OK_Icon_2.png"
                                fillMode: Image.PreserveAspectFit
                            }
                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                console.log("点击空孔标注确认按钮")
                                emptyWellsConfirmed = true
                                currentStep = 2  // 切换到Step 2
                            }
                        }
                    }
                }

                // 调整图像引导文字区域 - 按设计稿红色框位置
                Rectangle {
                    id: adjustImageGuide
                    x: 59
                    y: 252  // 移动到更下方，避免与Step 2重叠
                    width: 250
                    height: 97
                    color: "transparent"
                    visible: isAdjustingImage

                    // 调整图像引导文字
                    Text {
                        anchors.centerIn:parent
                        width: 250
                        height: 87
                        text: "Select wells\n1, 5, 9 or 13\n to adjust image"
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        horizontalAlignment: Text.AlignLeft
                        verticalAlignment: Text.AlignTop
                        wrapMode: Text.WordWrap
                    }
                }



                // 监听数据变化
                Connections {
                    target: ChamberDataManager
                    function onDataChanged(chamberIndex, deviceId) {
                        if (deviceId === chamberInfoPage.chamberId) {
                            // console.log("引导文字区域收到数据变化信号")
                            guideTextArea.visibilityRefresh++
                        }
                    }
                    function onChamberStatusChanged(deviceId, property, value) {
                        if (deviceId === chamberInfoPage.chamberId) {
                            // console.log("引导文字区域收到状态变化信号:", property, "=", value)
                            if (property === "isPatientAssigned") {
                                guideTextArea.visibilityRefresh++
                            }
                        }
                    }
                }
            }
        }

        // 中间层 - 图像控制区域
        Rectangle {
            id: imageControlArea
            x: 20
            y: 0
            width: 920
            height: 590
            color: "#3D404D"
            radius: 40
            property int visibilityRefresh: 0
            visible: {
                visibilityRefresh; // 强制依赖刷新计数器
                var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                // console.log("图像控制区域可见性计算:", hasPatient, "刷新计数:", visibilityRefresh)
                return hasPatient  // 只在已分配患者时显示
            }

            // 只保留左侧圆角，右侧变直角 (border-radius: 40px 0 0 40px)
            Rectangle {
                anchors.right: parent.right
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                width: 40
                color: "#3D404D"
            }
            //阴影效果
            Rectangle {
                id: rightShadowStrip
                x: 920  // 紧贴左侧面板右边缘
                y: 0
                width: 30   // 阴影宽度
                height: 590

                // 水平渐变：从左侧的半透明黑色到右侧完全透明 (再次加深以完美匹配设计图)
                gradient: Gradient {
                    orientation: Gradient.Horizontal
                    GradientStop { position: 0.0; color: "#40000000" }  // 左侧25%透明度 (再次加深)
                    GradientStop { position: 0.3; color: "#28000000" }  // 15.6%透明度 (再次加深)
                    GradientStop { position: 0.6; color: "#15000000" }  // 8.2%透明度 (再次加深)
                    GradientStop { position: 1.0; color: "#00000000" }  // 右侧完全透明
                }
            }
           
            // 捕获图像按钮
            Rectangle {
                id: captureImageButton
                x: 754
                y: 130
                width: 80
                height: 140
                color: "transparent"  // 透明背景

                Image {
                    id: captureImageIcon
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.top: parent.top
                    anchors.topMargin: 10
                    width: 120
                    height: 80
                    source: "../../Resource/Image/Image_Preview.png"
                    fillMode: Image.PreserveAspectFit
                }

                Text {
                    width: 120
                    height: 58
                    anchors.bottom: parent.bottom
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: "Image\r\npreview"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("点击了捕获图像按钮")
                        startImageCaptureProcess()
                    }
                }
            }

            // 调整图像按钮
            Rectangle {
                id: adjustImageButton
                x: 754
                y: 342
                width: 80
                height: 140
                color: "transparent"  // 透明背景

                Image {
                    id: adjustImageIcon
                    anchors.horizontalCenter: parent.horizontalCenter
                    anchors.top: parent.top
                    anchors.topMargin: 10
                    width: 120
                    height: 80
                    source: emptyWellsConfirmed ?
                           "../../Resource/Image/Adjust_Image_1.png" :
                           "../../Resource/Image/Adjust_Image.png"
                    fillMode: Image.PreserveAspectFit
                    opacity: emptyWellsConfirmed ? 1.0 : 0.5  // 只有确认空孔后才完全不透明
                }

                Text {
                    width: 120
                    height: 58
                    anchors.bottom: parent.bottom
                    anchors.horizontalCenter: parent.horizontalCenter
                    text: "Adjust\r\nimage"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: emptyWellsConfirmed ? "#FFFFFF" : "#888888"  // 只有确认空孔后才显示白色文字
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                MouseArea {
                    anchors.fill: parent
                    enabled: emptyWellsConfirmed  // 只有确认空孔标注后才能点击
                    onClicked: {
                        if (emptyWellsConfirmed) {
                            console.log("点击了调整图像按钮")
                            startImageAdjustmentProcess()  // 启动调整图像流程
                        }
                    }
                }
            }

            // 调整图像OK按钮 - 使用标准确认按钮模块
            Rectangle {
                id: adjustImageOkButton
                x: 3650  // 动态计算：背景宽度 - 按钮宽度 - 右边距
                y: 0
                width: 150
                height: 78
                color: "transparent"
                visible: isAdjustingImage && bottomBackground.width >= 3800  // 背景展开后才显示

                Row {
                    anchors.centerIn: parent
                    spacing: 24

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 35
                        height: 29
                        text: "OK"
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        horizontalAlignment: Text.AlignRight
                        verticalAlignment: Text.AlignVCenter
                    }

                    Image {
                        anchors.verticalCenter: parent.verticalCenter
                        width: 60
                        height: 60
                        source: "../../Resource/Image/OK_Icon_2.png"
                        fillMode: Image.PreserveAspectFit
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("点击调整图像OK按钮")
                        completeImageAdjustment()
                    }
                }
            }

            // 监听数据变化
            Connections {
                target: ChamberDataManager
                function onDataChanged(chamberIndex, deviceId) {
                    if (deviceId === chamberInfoPage.chamberId) {
                        // console.log("图像控制区域收到数据变化信号")
                        imageControlArea.visibilityRefresh++
                    }
                }
                function onChamberStatusChanged(deviceId, property, value) {
                    if (deviceId === chamberInfoPage.chamberId) {
                        // console.log("图像控制区域收到状态变化信号:", property, "=", value)
                        if (property === "isPatientAssigned") {
                            imageControlArea.visibilityRefresh++
                        }
                    }
                }
            }
        }

        // 顶层
        Rectangle {
            id: topBackground
            x: 20
            y: 0
            width: 708  // 只覆盖左侧数据区域
            height: 590
            color: "#313542"
            radius: 40
            // 右侧阴影条 - 使用渐变实现柔和效果，稍微加深以匹配设计图
            Rectangle {
                id: rightShadowStripMid
                x: 708  // 紧贴左侧面板右边缘
                y: 0
                width: 30   // 阴影宽度
                height: 590

                // 水平渐变：从左侧的半透明黑色到右侧完全透明 (再次加深以完美匹配设计图)
                gradient: Gradient {
                    orientation: Gradient.Horizontal
                    GradientStop { position: 0.0; color: "#40000000" }  // 左侧25%透明度 (再次加深)
                    GradientStop { position: 0.3; color: "#28000000" }  // 15.6%透明度 (再次加深)
                    GradientStop { position: 0.6; color: "#15000000" }  // 8.2%透明度 (再次加深)
                    GradientStop { position: 1.0; color: "#00000000" }  // 右侧完全透明
                }
            }

            //患者数据
            Rectangle {
                id: leftDataPanel
                x: 0
                y: 0
                width: 708
                height: 590
                color: "#3D404D"
                radius: 40

                // 只保留左侧圆角，右侧变直角 (border-radius: 40px 0 0 40px)
                Rectangle {
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    width: 40
                    color: "#3D404D"
                }

                // 舱室序号
                Text {
                    id: chamberNumber
                    x: 19
                    y: -5
                    width: 57
                    height: 138
                    text: {
                        // 从Python后端获取舱室号码
                        var chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId);
                        if (chamberData && chamberData.chamberNumber) {
                            return chamberData.chamberNumber.toString();
                        }
                        // 备用方案：从设备ID解析
                        var deviceId = chamberInfoPage.chamberId;
                        if (deviceId && deviceId.length >= 3) {
                            var numberPart = deviceId.substring(1);
                            return parseInt(numberPart, 10).toString();
                        }
                        return "1";
                    }
                    font.family: "Helvetica"
                    font.pixelSize: 115
                    color: "#ffffff"
                    font.letterSpacing: -7.5
                    horizontalAlignment: Text.AlignLeft
                    verticalAlignment: Text.AlignTop
                }

                // 姓名区域 - 按照精确位置规格实现
                Item {
                    id: nameArea
                    property int visibilityRefresh: 0
                    visible: {
                        visibilityRefresh; // 强制依赖刷新计数器
                        var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                        // console.log("姓名区域可见性计算:", hasPatient, "刷新计数:", visibilityRefresh)
                        return hasPatient
                    }

                    property var chamberData: ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)
                    property bool isChineseMode: chamberData ? (chamberData.chineseName && chamberData.chineseName.length > 0) : false

                    // 调试信息
                    Component.onCompleted: {
                        // console.log("姓名区域初始化 - 舱室ID:", chamberInfoPage.chamberId)
                        // console.log("hasPatientAssigned:", ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId))
                        // console.log("chamberData:", JSON.stringify(chamberData))
                    }

                    onVisibleChanged: {
                        // console.log("姓名区域可见性变化:", visible)
                    }

                    // 监听数据变化
                    Connections {
                        target: ChamberDataManager
                        function onDataChanged(chamberIndex, deviceId) {
                            if (deviceId === chamberInfoPage.chamberId) {
                                // console.log("姓名区域收到数据变化信号")
                                nameArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)
                                nameArea.visibilityRefresh++
                            }
                        }
                        function onChamberStatusChanged(deviceId, property, value) {
                            if (deviceId === chamberInfoPage.chamberId) {
                                // console.log("姓名区域收到状态变化信号:", property, "=", value)
                                nameArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)
                                if (property === "isPatientAssigned") {
                                    nameArea.visibilityRefresh++
                                }
                            }
                        }
                    }

                    // 英文模式：名 (x:143, y:16)
                    Text {
                        visible: !nameArea.isChineseMode
                        x: 143
                        y: 16
                        width: 400
                        height: 50
                        text: nameArea.chamberData ? nameArea.chamberData.patientFirstName : ""
                        font.family: "Helvetica"
                        font.pixelSize: 42
                        color: "#FFFFFF"
                    }

                    // 英文模式：姓 (x:143, y:66)
                    Text {
                        visible: !nameArea.isChineseMode
                        x: 143
                        y: 66
                        width: 400
                        height: 50
                        text: nameArea.chamberData ? nameArea.chamberData.patientLastName : ""
                        font.family: "Helvetica"
                        font.pixelSize: 42
                        color: "#FFFFFF"
                    }

                    // 中文模式：姓+名 (x:143, y:41)
                    Text {
                        visible: nameArea.isChineseMode
                        x: 143
                        y: 41
                        width: 400
                        height: 50
                        text: nameArea.chamberData ? nameArea.chamberData.chineseName : ""
                        font.family: "Helvetica"
                        font.pixelSize: 42
                        color: "#FFFFFF"
                    }
                }

                // 详细信息区域
                Rectangle {
                    id: patientInfoArea
                    x: 24
                    y: 142
                    width: 660
                    height: 280
                    color: "transparent"
                    property int visibilityRefresh: 0
                    visible: {
                        visibilityRefresh; // 强制依赖刷新计数器
                        var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                        // console.log("患者详细信息区域可见性计算:", hasPatient, "刷新计数:", visibilityRefresh)
                        return hasPatient
                    }

                    property var chamberData: ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)

                    // 监听数据变化
                    Connections {
                        target: ChamberDataManager
                        function onDataChanged(chamberIndex, deviceId) {
                            if (deviceId === chamberInfoPage.chamberId) {
                                // console.log("患者详细信息区域收到数据变化信号")
                                patientInfoArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)
                                patientInfoArea.visibilityRefresh++
                            }
                        }
                        function onChamberStatusChanged(deviceId, property, value) {
                            if (deviceId === chamberInfoPage.chamberId) {
                                // console.log("患者详细信息区域收到状态变化信号:", property, "=", value)
                                patientInfoArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)
                                if (property === "isPatientAssigned") {
                                    patientInfoArea.visibilityRefresh++
                                }
                            }
                        }
                    }

                    // ID信息
                    Text {
                        x:0
                        y:0
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        lineHeight: 29
                        lineHeightMode: Text.FixedHeight
                        textFormat: Text.RichText
                        text: "<span style='color: #81828B'>ID1: </span><span style='color: #FFFFFF'>" +
                                (patientInfoArea.chamberData ? patientInfoArea.chamberData.patientId1 : "") + "</span>"
                    }

                    Text {
                        x:0
                        y:31
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        lineHeight: 29
                        lineHeightMode: Text.FixedHeight
                        textFormat: Text.RichText
                        text: "<span style='color: #81828B'>ID2: </span><span style='color: #FFFFFF'>" +
                                (patientInfoArea.chamberData ? patientInfoArea.chamberData.patientId2 : "") + "</span>"
                    }
                    

                    // 分隔线1
                    Rectangle {
                        x:0
                        y:69
                        width: 660
                        height: 1
                        color: "#81828B"
                        opacity: 0.3
                    }

                    // 第三部分：详细信息
                    Column {
                        x:0
                        y:82
                        width: parent.width
                        height: 158
                        spacing: 2

                        Text {
                            textFormat: Text.RichText
                            text: "<span style='color: #81828B'>Date of birth: </span><span style='color: #FFFFFF'>" +
                                    (patientInfoArea.chamberData ? patientInfoArea.chamberData.birthDate : "") + "</span>"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            lineHeight: 24
                            lineHeightMode: Text.FixedHeight
                        }

                        Text {
                            textFormat: Text.RichText
                            text: "<span style='color: #81828B'>Egg age (Years): </span><span style='color: #FFFFFF'>" +
                                    (patientInfoArea.chamberData ? patientInfoArea.chamberData.eggAge : "") + "</span>"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            lineHeight: 24
                            lineHeightMode: Text.FixedHeight
                        }

                        Text {
                            textFormat: Text.RichText
                            text: "<span style='color: #81828B'>Development hours: </span><span style='color: #FFFFFF'>" +
                                    (patientInfoArea.chamberData ? patientInfoArea.chamberData.developmentHours : "") + "</span>"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            lineHeight: 24
                            lineHeightMode: Text.FixedHeight
                        }

                        Text {
                            textFormat: Text.RichText
                            text: "<span style='color: #81828B'>Estimated insemination time: </span><span style='color: #FFFFFF'>" +
                                    (patientInfoArea.chamberData ? patientInfoArea.chamberData.estimatedFertilization : "") + "</span>"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            lineHeight: 24
                            lineHeightMode: Text.FixedHeight
                        }

                        Text {
                            textFormat: Text.RichText
                            text: "<span style='color: #81828B'>Cycle type: </span><span style='color: #FFFFFF'>" +
                                    (patientInfoArea.chamberData ? patientInfoArea.chamberData.cycleType : "") + "</span>"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            lineHeight: 24
                            lineHeightMode: Text.FixedHeight
                        }

                        Text {
                            textFormat: Text.RichText
                            text: "<span style='color: #81828B'>Z-Stack number: </span><span style='color: #FFFFFF'>11</span>"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            lineHeight: 24
                            lineHeightMode: Text.FixedHeight
                        }
                    }
                    // 分隔线2
                    Rectangle {
                        x:0
                        y:252
                        width: 660
                        height: 1
                        color: "#81828B"
                        opacity: 0.3
                    }
                    
                }

                // 环境信息区域 - 使用Python后端数据
                Rectangle {
                    id: envInfoArea
                    x: 20  
                    y: 416  
                    width: 289
                    height: 155
                    color: "transparent"

                    // 从Python后台获取当前舱室的数据
                    property var chamberData: ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)

                    // 环境数据属性 - 使用正确的QML属性绑定
                    readonly property real temperature: chamberData ? chamberData.temperature : 0
                    readonly property string tempStatus: chamberData ? chamberData.tempStatus : "initial"
                    readonly property string flowStatus: chamberData ? chamberData.flowStatus : "initial"
                    readonly property int flowValue: chamberData ? chamberData.flowValue : 0
                    readonly property string humidityStatus: chamberData ? chamberData.humidityStatus : "off"
                    readonly property bool humidityEnabled: chamberData ? chamberData.humidityEnabled : false

                    // 报警状态 - 使用正确的QML属性绑定
                    readonly property bool lidOpenAlarm: chamberData ? chamberData.lidOpenAlarm : false
                    readonly property bool thermalCutoffAlarm: chamberData ? chamberData.thermalCutoffAlarm : false
                    readonly property bool serviceAlarm: chamberData ? chamberData.serviceAlarm : false
                    readonly property bool illuminationAlarm: chamberData ? chamberData.illuminationAlarm : false

                    // 警告状态 - 使用正确的QML属性绑定
                    readonly property bool cameraOffline: chamberData ? chamberData.cameraOffline : false
                    readonly property bool controllerOffline: chamberData ? chamberData.controllerOffline : false

                    // 监听Python后台数据变化 - 使用正确的绑定更新方式
                    Connections {
                        target: ChamberDataManager
                        function onDataChanged(chamberIndex, deviceId) {
                            if (deviceId === chamberInfoPage.chamberId) {
                                // 触发属性重新计算
                                envInfoArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId);
                            }
                        }
                        function onAllDataChanged() {
                            // 触发属性重新计算
                            envInfoArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId);
                        }
                        function onChamberStatusChanged(deviceId, property, value) {
                            if (deviceId === chamberInfoPage.chamberId) {
                                // 触发属性重新计算
                                envInfoArea.chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId);
                            }
                        }
                    }

                    // 1. 温度区域
                    Rectangle {
                        id: temperatureArea
                        x: 0
                        y: 0
                        width: 191
                        height: 49
                        radius: 24
                        color: "transparent"

                        // 只保留左上圆角，其他角变直角
                        Rectangle {
                            anchors.right: parent.right
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            width: 24
                            color: parent.color
                        }
                        Rectangle {
                            anchors.left: parent.left
                            anchors.right: parent.right
                            anchors.bottom: parent.bottom
                            height: 24
                            color: parent.color
                        }

                        // 温度背景渐变 - 使用Canvas绘制-83度渐变
                        Canvas {
                            anchors.fill: parent
                            visible: envInfoArea.tempStatus !== "initial"

                            // 监听状态变化以触发重绘
                            onVisibleChanged: requestPaint()
                            Component.onCompleted: requestPaint()

                            onPaint: {
                                var ctx = getContext("2d");
                                ctx.reset();

                                // 定义角度和起始位置
                                var angle = -83;
                                var startPos = 0.04;

                                // 颜色根据状态
                                var firstColor = envInfoArea.tempStatus === "normal" ? "#00A605" : "#DA0000";
                                var secondColor = envInfoArea.tempStatus === "normal" ? "#00C89B" : "#FA007D";

                                // 计算渐变起点和终点
                                var rad = angle * Math.PI / 180;
                                var dx = Math.sin(rad);
                                var dy = -Math.cos(rad);

                                var cx = width / 2;
                                var cy = height / 2;

                                // 对于终点 (positive direction)
                                var tPos = [];
                                if (dx !== 0) {
                                    var t = dx > 0 ? (width - cx) / dx : (0 - cx) / dx;
                                    tPos.push(t);
                                }
                                if (dy !== 0) {
                                    var t = dy > 0 ? (height - cy) / dy : (0 - cy) / dy;
                                    tPos.push(t);
                                }
                                var t = Math.min.apply(null, tPos);
                                var endX = cx + t * dx;
                                var endY = cy + t * dy;

                                // 对于起点 (negative direction)
                                var sx = -dx;
                                var sy = -dy;
                                var sTPos = [];
                                if (sx !== 0) {
                                    var st = sx > 0 ? (width - cx) / sx : (0 - cx) / sx;
                                    sTPos.push(st);
                                }
                                if (sy !== 0) {
                                    var st = sy > 0 ? (height - cy) / sy : (0 - cy) / sy;
                                    sTPos.push(st);
                                }
                                var st = Math.min.apply(null, sTPos);
                                var startX = cx + st * sx;
                                var startY = cy + st * sy;

                                // 调整起点以匹配 startPos (例如 4%)
                                var vx = endX - startX;
                                var vy = endY - startY;
                                var newStartX = startX + startPos * vx;
                                var newStartY = startY + startPos * vy;

                                // 创建渐变
                                var gradient = ctx.createLinearGradient(newStartX, newStartY, endX, endY);
                                gradient.addColorStop(0, firstColor);
                                gradient.addColorStop(1, secondColor);

                                ctx.fillStyle = gradient;

                                // 绘制圆角矩形 - 只有左上角有圆角
                                ctx.beginPath();
                                ctx.moveTo(24, 0); // 从左上圆角结束位置开始
                                ctx.lineTo(width, 0); // 上边到右上角
                                ctx.lineTo(width, height); // 右边到右下角
                                ctx.lineTo(0, height); // 下边到左下角
                                ctx.lineTo(0, 24); // 左边到左上角圆角开始
                                ctx.arcTo(0, 0, 24, 0, 24); // 左上角圆角
                                ctx.closePath();
                                ctx.fill();
                            }
                        }

                        // 温度图标
                        Image {
                            x: 19
                            y: (parent.height - height) / 2
                            width: 20.33
                            height: 38.99
                            source: "../../Resource/Image/Temperature_Icon.png"
                            fillMode: Image.PreserveAspectFit
                        }

                        // 实时温度文字
                        Text {
                            x: 62
                            y: (parent.height - height) / 2
                            width: 47
                            height: 29
                            text: envInfoArea.temperature.toFixed(1)
                            font.family: "Helvetica"
                            font.pixelSize: 24
                            color: "#FFFFFF"
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignVCenter
                        }

                        // 摄氏度固定文字
                        Text {
                            x: 105
                            y: 12
                            width: 18
                            height: 19
                            text: "°C"
                            font.family: "Helvetica"
                            font.pixelSize: 16
                            color: "#FFFFFF"
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignVCenter
                        }

                        // 状态图标
                        Image {
                            x: 147
                            y: (parent.height - height) / 2
                            width: 39
                            height: 39
                            source: envInfoArea.tempStatus === "normal" ? "../../Resource/Image/OK_icon.png" : "../../Resource/Image/Critical_Icon.png"
                            fillMode: Image.PreserveAspectFit
                        }
                    }

                    // 2. 流量区域
                    Rectangle {
                        id: flowArea
                        x: 0
                        y: 53
                        width: 191
                        height: 49
                        radius: 0
                        color: envInfoArea.flowStatus === "initial" ? "#313542" : "transparent"

                        // 流量背景渐变 - 异常/正常(-83度)，吹扫(102度)
                        Canvas {
                            anchors.fill: parent
                            visible: envInfoArea.flowStatus !== "initial"

                            // 监听状态变化以触发重绘
                            onVisibleChanged: requestPaint()
                            Component.onCompleted: requestPaint()

                            onPaint: {
                                var ctx = getContext("2d");
                                ctx.reset();

                                // 根据状态选择角度和起始位置
                                var angle = (envInfoArea.flowStatus === "purge" ? 102 : -83);
                                var startPos = (envInfoArea.flowStatus === "purge" ? 0.0 : 0.04);

                                // 颜色根据状态
                                var firstColor = envInfoArea.flowStatus === "normal" ? "#00A605" :
                                                envInfoArea.flowStatus === "purge" ? "#00D9FB" : "#DA0000";
                                var secondColor = envInfoArea.flowStatus === "normal" ? "#00C89B" :
                                                envInfoArea.flowStatus === "purge" ? "#0041EE" : "#FA007D";

                                // 计算渐变起点和终点 (同上)
                                var rad = angle * Math.PI / 180;
                                var dx = Math.sin(rad);
                                var dy = -Math.cos(rad);

                                var cx = width / 2;
                                var cy = height / 2;

                                var tPos = [];
                                if (dx !== 0) {
                                    var t = dx > 0 ? (width - cx) / dx : (0 - cx) / dx;
                                    tPos.push(t);
                                }
                                if (dy !== 0) {
                                    var t = dy > 0 ? (height - cy) / dy : (0 - cy) / dy;
                                    tPos.push(t);
                                }
                                var t = Math.min.apply(null, tPos);
                                var endX = cx + t * dx;
                                var endY = cy + t * dy;

                                var sx = -dx;
                                var sy = -dy;
                                var sTPos = [];
                                if (sx !== 0) {
                                    var st = sx > 0 ? (width - cx) / sx : (0 - cx) / sx;
                                    sTPos.push(st);
                                }
                                if (sy !== 0) {
                                    var st = sy > 0 ? (height - cy) / sy : (0 - cy) / sy;
                                    sTPos.push(st);
                                }
                                var st = Math.min.apply(null, sTPos);
                                var startX = cx + st * sx;
                                var startY = cy + st * sy;

                                // 调整起点以匹配 startPos
                                var vx = endX - startX;
                                var vy = endY - startY;
                                var newStartX = startX + startPos * vx;
                                var newStartY = startY + startPos * vy;

                                // 创建渐变
                                var gradient = ctx.createLinearGradient(newStartX, newStartY, endX, endY);
                                gradient.addColorStop(0, firstColor);
                                gradient.addColorStop(1, secondColor);

                                ctx.fillStyle = gradient;
                                ctx.fillRect(0, 0, width, height);
                            }
                        }

                        // 流量图标
                        Image {
                            x: 13
                            y: 11
                            width: 42.03
                            height: 34.95
                            source: "../../Resource/Image/Flow_Icon.png"
                            fillMode: Image.PreserveAspectFit
                        }

                        // 流量文字 - 正常和异常状态
                        Text {
                            x: 62
                            y: (parent.height - height) / 2
                            width: 27
                            height: 29
                            text: envInfoArea.flowValue.toString()
                            font.family: "Helvetica"
                            font.pixelSize: 24
                            color: "#FFFFFF"
                            visible: envInfoArea.flowStatus !== "purge"
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignVCenter
                        }

                        // 流量单位
                        Text {
                            x: 89
                            y: 18
                            width: 48
                            height: 19
                            text: "ml/min"
                            font.family: "Helvetica"
                            font.pixelSize: 16
                            color: "#FFFFFF"
                            visible: envInfoArea.flowStatus !== "purge"
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignVCenter
                        }

                        // 吹扫文字
                        Text {
                            x: 62
                            y: (parent.height - height) / 2
                            width: 65
                            height: 29
                            text: "Purge"
                            font.family: "Helvetica"
                            font.pixelSize: 24
                            color: "#FFFFFF"
                            visible: envInfoArea.flowStatus === "purge"
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignVCenter
                        }

                        // 状态图标
                        Image {
                            x: 147
                            y: (parent.height - height) / 2
                            width: 39
                            height: 39
                            source: envInfoArea.flowStatus === "normal" ? "../../Resource/Image/OK_icon.png" :
                                    envInfoArea.flowStatus === "purge" ? "../../Resource/Image/Purge_Icon.png" : "../../Resource/Image/Critical_Icon.png"
                            fillMode: Image.PreserveAspectFit
                        }
                    }

                    // 3. 湿度区域
                    Rectangle {
                        id: humidityArea
                        x: 0
                        y: 106
                        width: 191
                        height: 49
                        radius: 24
                        color: envInfoArea.humidityStatus === "off" ? "#313542" : "transparent"

                        // 只保留左下圆角，其他角变直角
                        Rectangle {
                            anchors.right: parent.right
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            width: 24
                            color: parent.color
                        }
                        Rectangle {
                            anchors.left: parent.left
                            anchors.right: parent.right
                            anchors.top: parent.top
                            height: 24
                            color: parent.color
                        }

                        // 湿度背景渐变 - 使用Canvas绘制-83度渐变
                        Canvas {
                            anchors.fill: parent
                            visible: envInfoArea.humidityStatus === "normal" || envInfoArea.humidityStatus === "abnormal"

                            // 监听状态变化以触发重绘
                            onVisibleChanged: requestPaint()
                            Component.onCompleted: requestPaint()

                            onPaint: {
                                var ctx = getContext("2d");
                                ctx.reset();

                                // 定义角度和起始位置
                                var angle = -83;
                                var startPos = 0.04;

                                // 颜色根据状态
                                var firstColor = envInfoArea.humidityStatus === "normal" ? "#00A605" : "#DA0000";
                                var secondColor = envInfoArea.humidityStatus === "normal" ? "#00C89B" : "#FA007D";

                                // 计算渐变起点和终点
                                var rad = angle * Math.PI / 180;
                                var dx = Math.sin(rad);
                                var dy = -Math.cos(rad);

                                var cx = width / 2;
                                var cy = height / 2;

                                var tPos = [];
                                if (dx !== 0) {
                                    var t = dx > 0 ? (width - cx) / dx : (0 - cx) / dx;
                                    tPos.push(t);
                                }
                                if (dy !== 0) {
                                    var t = dy > 0 ? (height - cy) / dy : (0 - cy) / dy;
                                    tPos.push(t);
                                }
                                var t = Math.min.apply(null, tPos);
                                var endX = cx + t * dx;
                                var endY = cy + t * dy;

                                var sx = -dx;
                                var sy = -dy;
                                var sTPos = [];
                                if (sx !== 0) {
                                    var st = sx > 0 ? (width - cx) / sx : (0 - cx) / sx;
                                    sTPos.push(st);
                                }
                                if (sy !== 0) {
                                    var st = sy > 0 ? (height - cy) / sy : (0 - cy) / sy;
                                    sTPos.push(st);
                                }
                                var st = Math.min.apply(null, sTPos);
                                var startX = cx + st * sx;
                                var startY = cy + st * sy;

                                // 调整起点以匹配 startPos
                                var vx = endX - startX;
                                var vy = endY - startY;
                                var newStartX = startX + startPos * vx;
                                var newStartY = startY + startPos * vy;

                                // 创建渐变
                                var gradient = ctx.createLinearGradient(newStartX, newStartY, endX, endY);
                                gradient.addColorStop(0, firstColor);
                                gradient.addColorStop(1, secondColor);

                                ctx.fillStyle = gradient;

                                // 绘制圆角矩形 - 只有左下角有圆角
                                ctx.beginPath();
                                ctx.moveTo(0, 0); // 从左上角开始
                                ctx.lineTo(width, 0); // 上边到右上角
                                ctx.lineTo(width, height); // 右边到右下角
                                ctx.lineTo(24, height); // 下边到左下角圆角开始
                                ctx.arcTo(0, height, 0, height - 24, 24); // 左下角圆角
                                ctx.lineTo(0, 0); // 左边到左上角
                                ctx.closePath();
                                ctx.fill();
                            }
                        }

                        // 湿度图标
                        Image {
                            x: 13
                            y: 5
                            width: 48.06
                            height: 37.57
                            source: "../../Resource/Image/Humidity_Icon.png"
                            fillMode: Image.PreserveAspectFit
                        }

                        // 湿度状态文字
                        Text {
                            x: 62
                            y: (parent.height - height) / 2
                            width: 48
                            height: 29
                            text: envInfoArea.humidityEnabled ? "ON" : "OFF"
                            font.family: "Helvetica"
                            font.pixelSize: 24
                            color: "#FFFFFF"
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignVCenter
                        }

                        // 状态图标
                        Image {
                            x: 147
                            y: (parent.height - height) / 2
                            width: 39
                            height: 39
                            source: envInfoArea.humidityStatus === "normal" ? "../../Resource/Image/OK_icon.png" : "../../Resource/Image/Critical_Icon.png"
                            fillMode: Image.PreserveAspectFit
                        }
                    }

                    // 4. 报警区域
                    Rectangle {
                        id: alarmArea
                        x: 195
                        y: 0
                        width: 94
                        height: 102
                        color: (envInfoArea.lidOpenAlarm || envInfoArea.thermalCutoffAlarm ||
                               envInfoArea.serviceAlarm || envInfoArea.illuminationAlarm) ? "#DA0000" : "#313542"
                        radius: 24

                        // 只保留右上圆角，其他角变直角
                        Rectangle {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            width: 24
                            color: parent.color
                        }
                        Rectangle {
                            anchors.left: parent.left
                            anchors.right: parent.right
                            anchors.bottom: parent.bottom
                            height: 24
                            color: parent.color
                        }

                        // 开盖超时报警图标
                        Image {
                            x: 5
                            y: 5
                            width: 39.95
                            height: 37
                            source: "../../Resource/Image/Lid_open_alarm_icon.png"
                            fillMode: Image.PreserveAspectFit
                            visible: envInfoArea.lidOpenAlarm
                        }

                        // 热切断报警图标
                        Image {
                            x: 48.98
                            y: 5
                            width: 39.95
                            height: 37
                            source: "../../Resource/Image/Thermal_cutoff_alarm_icon.png"
                            fillMode: Image.PreserveAspectFit
                            visible: envInfoArea.thermalCutoffAlarm
                        }

                        // 舱室检修报警图标
                        Image {
                            x: 5
                            y: 58
                            width: 39.95
                            height: 37
                            source: "../../Resource/Image/Service_alarm_icon.png"
                            fillMode: Image.PreserveAspectFit
                            visible: envInfoArea.serviceAlarm
                        }

                        // 照明异常报警图标
                        Image {
                            x: 48.98
                            y: 58
                            width: 39.95
                            height: 37
                            source: "../../Resource/Image/Illumination_alarm_icon.png"
                            fillMode: Image.PreserveAspectFit
                            visible: envInfoArea.illuminationAlarm
                        }
                    }

                    // 5. 警告区域
                    Rectangle {
                        id: warningArea
                        x: 195
                        y: 106
                        width: 94
                        height: 49
                        color: (envInfoArea.cameraOffline || envInfoArea.controllerOffline) ? "#FF9500" : "#313542"
                        radius: 24

                        // 只保留右下圆角，其他角变直角
                        Rectangle {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.right: parent.right
                            height: 24
                            color: parent.color
                        }
                        Rectangle {
                            anchors.left: parent.left
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            width: 24
                            color: parent.color
                        }

                        // 相机故障警告图标
                        Image {
                            x: 5
                            y: 3.5
                            width: 39.95
                            height: 37
                            source: "../../Resource/Image/camera_offline_icon.png"
                            fillMode: Image.PreserveAspectFit
                            visible: envInfoArea.cameraOffline
                        }

                        // 舱室断联警告图标
                        Image {
                            x: 48.98
                            y: 3.5
                            width: 39.95
                            height: 37
                            source: "../../Resource/Image/controller_offline_icon.png"
                            fillMode: Image.PreserveAspectFit
                            visible: envInfoArea.controllerOffline
                        }
                    }
                }
            }
            // 患者管理按钮区域
            Rectangle {
                id: patientManagementArea
                x: 590
                y: 27
                width: 108
                height: 175  // 足够容纳所有按钮
                color: "#3D404D"
                // 分配患者按钮 - 当没有患者时显示
                Rectangle {
                    id: allocatePatientButton
                    x: 0
                    y: 0
                    width: 108
                    height: 78
                    color: "transparent"
                    property int visibilityRefresh: 0
                    visible: {
                        visibilityRefresh;
                        var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                        // console.log("分配患者按钮可见性:", !hasPatient)
                        return !hasPatient
                    }

                    // 分配患者图标 - 无背景
                    Image {
                        anchors.centerIn: parent
                        width: 108
                        height: 78
                        source: "../../Resource/Image/Allocate_Patient_2.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    // 鼠标交互
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            console.log("点击分配患者按钮，舱室:", chamberInfoPage.chamberId)
                            // 显示未分配患者列表
                            unallocatedPatientsList.show(chamberInfoPage.chamberId)
                        }
                    }
                }

                // 分配患者按钮文字 - 按照设计规格调整位置
                Text {
                    id: allocatePatientText
                    x: 0
                    y: 83  // y:110 - y:27 = 83 (相对于按钮区域)
                    width: 108
                    height: 58
                    text: "Allocate\nPatient"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    property int visibilityRefresh: 0
                    visible: {
                        visibilityRefresh;
                        var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                        return !hasPatient
                    }
                }

                // 移除患者按钮 - 当有患者时显示
                Rectangle {
                    id: removePatientButton
                    x: 0
                    y: -9  // y:18 - y:27 = -9 (相对于按钮区域)
                    width: 108
                    height: 78
                    color: "transparent"
                    property int visibilityRefresh: 0
                    visible: {
                        visibilityRefresh;
                        var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                        // console.log("移除患者按钮可见性:", hasPatient)
                        return hasPatient
                    }

                    // 删除患者图标 - 无背景
                    Image {
                        anchors.centerIn: parent
                        width: 108
                        height: 78
                        source: "../../Resource/Image/Delete_Patient_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    // 鼠标交互
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            console.log("点击移除患者按钮，舱室:", chamberInfoPage.chamberId)
                            // TODO: 确认移除患者
                        }
                    }
                }

                // 编辑患者按钮 - 当有患者时显示
                Rectangle {
                    id: editPatientButton
                    x: 0
                    y: 97  // y:124 - y:27 = 97 (相对于按钮区域)
                    width: 108
                    height: 78
                    color: "transparent"
                    property int visibilityRefresh: 0
                    visible: {
                        visibilityRefresh;
                        var hasPatient = ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId)
                        // console.log("编辑患者按钮可见性:", hasPatient)
                        return hasPatient
                    }

                    // 编辑患者图标 - 无背景
                    Image {
                        anchors.centerIn: parent
                        width: 108
                        height: 78
                        source: "../../Resource/Image/Edit_Patient_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    // 鼠标交互
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            console.log("点击编辑患者按钮，舱室:", chamberInfoPage.chamberId)
                            // 显示编辑患者信息弹窗
                            editPatientPopup.show(chamberInfoPage.chamberId)
                        }
                    }
                }
            }
        }
    }

    // 未分配患者列表组件
    UnallocatedPatientsList {
        id: unallocatedPatientsList

        onPatientSelected: function(patientData) {
            console.log("患者已选择:", JSON.stringify(patientData))
        }

        onCancelled: {
            console.log("取消分配患者")
        }

        onConfirmed: {
            console.log("确认分配患者到舱室:", chamberInfoPage.chamberId)
        }

        // 新增：处理患者分配
        onPatientAssigned: function(patientData, targetChamberId) {
            console.log("分配患者到舱室:", targetChamberId, "患者信息:", JSON.stringify(patientData))

            // 更新左侧患者信息显示
            updatePatientInfo(patientData)

            // 更新舱室状态为已分配
            if (chamberInfoPage.chamberId === targetChamberId) {
                chamberInfoPage.currentState = "assigned"
                console.log("舱室状态已更新为: assigned")

                // 刷新按钮可见性
                allocatePatientButton.visibilityRefresh++
                allocatePatientText.visibilityRefresh++
                removePatientButton.visibilityRefresh++
                editPatientButton.visibilityRefresh++
            }
        }
    }

    // 开始图像捕获流程
    function startImageCaptureProcess() {
        // console.log("开始图像捕获流程")

        // 第一阶段：图像捕获
        isCapturingImages = true
        isDetectingEmptyWells = false
        emptyWellsDetected = false
        emptyWellsConfirmed = false

        // 延迟一点时间确保Repeater完全创建
        Qt.callLater(function() {
            // console.log("=== 🚀 开始图像捕获流程 ===")
            // print("=== 🚀 开始图像捕获流程 ===")

            // 开始逐个填充孔位图像（从1号孔开始）
            fillHoleImagesSequentially()
        })

        // 模拟图像捕获过程（3秒）
        captureTimer.start()
    }

    // 设置单个孔位图像
    function setHoleImage(holeIndex, imagePath) {
        // console.log("🎯 设置孔位", (holeIndex + 1), "图像:", imagePath)

        if (holeRepeater && holeRepeater.itemAt) {
            var holeItem = holeRepeater.itemAt(holeIndex)
            if (holeItem) {
                // console.log("✅ 找到孔位", (holeIndex + 1))
                // 查找sampleImageContainer
                var found = false
                for (var j = 0; j < holeItem.children.length; j++) {
                    var child = holeItem.children[j]
                    if (child.objectName === "sampleImageContainer") {
                        // 在容器内查找sampleImage
                        for (var k = 0; k < child.children.length; k++) {
                            if (child.children[k].objectName === "sampleImage") {
                                child.children[k].source = imagePath
                                // console.log("🎯 成功设置孔位", (holeIndex + 1), "图像:", imagePath)
                                found = true
                                break
                            }
                        }
                        break
                    }
                }
                if (!found) {
                    console.log("❌ 未找到sampleImageContainer在孔位", (holeIndex + 1))
                }
            } else {
                // console.log("❌ 无法找到孔位", (holeIndex + 1))
            }
        } else {
            console.log("❌ holeRepeater不可用")
        }
    }



    // 逐个填充孔位图像
    function fillHoleImagesSequentially() {
        // console.log("=== 🖼️ 开始逐个填充孔位图像 ===")
        // console.log("🔍 检查holeRepeater可用性:", holeRepeater ? "可用" : "不可用")
        if (holeRepeater) {
            console.log("📊 holeRepeater.count:", holeRepeater.count)
        }

        // 可用的图像路径
        var imagePaths = [
            "../../Resource/SamplePic/Empty.png",      // 空孔
            "../../Resource/SamplePic/noEmpty.png"     // 有样本
        ]

        // 存储每个孔位的图像选择和空孔状态
        var holeImageTypes = []
        var emptyWellIndexes = []  // 记录空孔的下标

        // 为所有16个孔位填充图像
        for (var i = 0; i < 16; i++) {
            var randomIndex = Math.floor(Math.random() * imagePaths.length)
            var selectedImage = imagePaths[randomIndex]
            holeImageTypes[i] = selectedImage

            // 检查是否是空孔图片（精确匹配文件名）
            var isEmptyImage = selectedImage.includes("/Empty.png") && !selectedImage.includes("noEmpty.png")
            if (isEmptyImage) {
                emptyWellIndexes.push(i)  // 记录空孔下标
            }

            // console.log("📋 孔位", (i + 1), "填充图像:", selectedImage, "是否空孔:", isEmptyImage)

            // 立即填充所有图像，不使用延时
            setHoleImage(i, selectedImage)
        }

        // 存储数据供检测使用
        chamberInfoPage.holeImageTypes = holeImageTypes
        chamberInfoPage.emptyWellIndexes = emptyWellIndexes

        // console.log("🎯 空孔位置记录:", emptyWellIndexes)
    }

    // 开始空孔检测流程
    function startEmptyWellDetection() {
        // console.log("开始空孔检测流程")

        // 第二阶段：空孔检测
        isCapturingImages = false
        isDetectingEmptyWells = true

        // 开始按序检测空孔（从1号孔开始）
        detectEmptyWellsSequentially()

        // 模拟空孔检测过程（2秒）
        detectionTimer.start()
    }

    // 按序检测空孔并显示标记
    function detectEmptyWellsSequentially() {
        // console.log("开始按序检测空孔")

        // 遍历所有孔位，检测哪些是空孔
        for (var i = 0; i < 16; i++) {
            (function(holeIndex) {
                var timer = Qt.createQmlObject(
                    "import QtQuick 2.15; Timer { interval: " + (holeIndex * 150) + "; repeat: false; running: true }",
                    chamberInfoPage
                )
                timer.triggered.connect(function() {
                    // 直接根据记录的空孔下标进行检测
                    var isEmpty = false
                    if (chamberInfoPage.emptyWellIndexes) {
                        isEmpty = chamberInfoPage.emptyWellIndexes.indexOf(holeIndex) !== -1
                    }

                    var filledImagePath = chamberInfoPage.holeImageTypes ? chamberInfoPage.holeImageTypes[holeIndex] : "未知"

                    // console.log("🔍 检测孔位", (holeIndex + 1), ":")
                    // console.log("  - 填充图片:", filledImagePath)
                    // console.log("  - 在空孔列表中:", isEmpty)
                    // console.log("  - 空孔列表:", chamberInfoPage.emptyWellIndexes)

                    // 如果是空孔，设置空孔标记
                    if (isEmpty) {
                        // console.log("🏷️ 设置孔位", (holeIndex + 1), "为空孔标记")
                        ChamberDataManager.toggleEmptyWellTag(chamberInfoPage.chamberId, holeIndex)
                    } else {
                        // console.log("❌ 孔位", (holeIndex + 1), "不是空孔，跳过标记")
                    }

                    timer.destroy()
                })
            })(i)
        }
    }

    // 完成空孔检测，显示确认界面
    function showEmptyWellConfirmation() {
        // console.log("显示空孔确认界面")

        // 第三阶段：空孔标注确认
        isDetectingEmptyWells = false
        emptyWellsDetected = true
        imagePreviewCaptured = true  // 标记已捕获图像

        // console.log("🏷️ 空孔检测和标记完成")
    }

    // 开始调整图像流程
    function startImageAdjustmentProcess() {
        console.log("开始调整图像流程")

        // 切换到Step 2并显示调整图像引导区域
        currentStep = 2
        isAdjustingImage = true
        selectedAdjustmentHole = 0  // 默认选中1号孔（索引0）

        // 启动背景动画：宽度从1890px变为3800px，右侧圆角从295px变为40px
        console.log("启动背景动画：宽度 1890px → 3800px，右侧圆角 295px → 40px")
        bottomBackground.width = 3800
        backgroundCanvas.rightRadius = 40

        // 背景保持拉长状态，等待用户点击OK按钮
    }

    // 完成调整图像流程
    function completeImageAdjustment() {
        console.log("完成调整图像流程")

        // 调整图像完成，恢复到正常状态
        isAdjustingImage = false

        // 恢复背景到原始状态
        resetBackgroundToOriginal()

        // 这里可以添加后续的操作逻辑，比如等待用户进行下一步操作
        console.log("调整图像流程完成，背景已恢复正常")
    }

    // 恢复背景到原始状态（如果需要的话）
    function resetBackgroundToOriginal() {
        console.log("恢复背景到原始状态")
        bottomBackground.width = 1890

        // 使用强制重置函数
        backgroundCanvas.forceReset()
        // console.log("背景Canvas已重新绘制，rightRadius =", backgroundCanvas.rightRadius)
    }

    // 图像捕获定时器
    Timer {
        id: captureTimer
        interval: 3000  // 3秒
        repeat: false
        onTriggered: {
            startEmptyWellDetection()
        }
    }

    // 空孔检测定时器
    Timer {
        id: detectionTimer
        interval: 2000  // 2秒
        repeat: false
        onTriggered: {
            showEmptyWellConfirmation()
        }
    }



    // 更新患者信息显示
    function updatePatientInfo(patientData) {
        // console.log("开始更新患者信息显示:", JSON.stringify(patientData))

        // 映射患者数据字段到舱室数据字段
        var chamberData = {
            patientFirstName: patientData.firstName || "",
            patientLastName: patientData.lastName || "",
            patientId1: patientData.id1 || "",
            patientId2: patientData.id2 || "",
            birthDate: patientData.dateOfBirth || "",
            eggAge: patientData.eggAge || "N/A",
            developmentHours: patientData.developmentHours || "N/A",
            estimatedFertilization: patientData.estimatedFertilization || "N/A",
            cycleType: patientData.cycleType || "N/A",
            chineseName: patientData.chineseName || ""
        }

        // 通过ChamberDataManager更新患者数据 - 使用updateChamberData方法
        if (typeof ChamberDataManager !== 'undefined') {
            // 逐个更新患者数据字段
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "isPatientAssigned", true)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "patientFirstName", chamberData.patientFirstName)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "patientLastName", chamberData.patientLastName)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "patientId1", chamberData.patientId1)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "patientId2", chamberData.patientId2)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "birthDate", chamberData.birthDate)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "eggAge", chamberData.eggAge)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "developmentHours", chamberData.developmentHours)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "estimatedFertilization", chamberData.estimatedFertilization)
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "cycleType", chamberData.cycleType)

            // 更新显示名称
            var displayName = chamberData.patientFirstName + " " + chamberData.patientLastName
            ChamberDataManager.updateChamberData(chamberInfoPage.chamberId, "displayName", displayName)

            // console.log("患者数据已通过ChamberDataManager.updateChamberData更新")

            // 验证数据是否正确更新
            var updatedChamberData = ChamberDataManager.getChamberDataByDeviceId(chamberInfoPage.chamberId)
            // console.log("更新后的舱室数据:", JSON.stringify(updatedChamberData))
            // console.log("hasPatientAssigned:", ChamberDataManager.hasPatientAssigned(chamberInfoPage.chamberId))
        } else {
            // console.log("ChamberDataManager不可用，无法更新患者数据")
        }
    }

    // 为EditPatientPopup提供数据访问函数
    function getChamberData(targetChamberId) {
        if (ChamberDataManager) {
            return ChamberDataManager.getChamberDataByDeviceId(targetChamberId)
        }
        return null
    }

    // 编辑患者信息弹窗组件
    EditPatientPopup {
        id: editPatientPopup
        keyboard: chamberKeyboard // 直接传递键盘实例

        onCancelled: {
            console.log("取消编辑患者信息")
        }

        onConfirmed: function(updatedData) {
            console.log("确认保存患者信息:", JSON.stringify(updatedData))
            // 更新患者数据到后端
            if (ChamberDataManager) {
                ChamberDataManager.updatePatientDetails(chamberInfoPage.chamberId, updatedData)
                console.log("患者信息已更新到后端")
            }
        }

        onPopupOpened: function(inputField) {
            targetInputField = inputField
            keyboardTimer.start()
        }

        onDataChanged: {
            console.log("患者数据已修改")
        }
    }

    // 键盘事件处理
    Keys.onEscapePressed: {
        // 如果编辑患者弹窗打开，先关闭弹窗
        if (editPatientPopup.isVisible) {
            editPatientPopup.hide()
        }
        // 如果患者列表打开，先关闭列表
        else if (unallocatedPatientsList.isVisible) {
            unallocatedPatientsList.hide()
        } else {
            navigateBack()
        }
    }

    // 标记选择下拉框
    Rectangle {
        id: tagSelectorContainer
        width: 200
        height: 50
        x: 1000  // 位置可根据需要调整
        y: 400
        color: "#2A2A2A"
        border.color: "#555555"
        border.width: 1
        radius: 5
        visible: showTagSelector

        ComboBox {
            id: tagSelector
            anchors.fill: parent
            anchors.margins: 2

            model: [
                {text: "移植", value: "accepted"},
                {text: "废弃", value: "cancel"},
                {text: "冷冻", value: "freeze"},
                {text: "移除", value: "remove"}
            ]

            textRole: "text"
            valueRole: "value"

            onActivated: {
                if (selectedHoleIndex >= 0) {
                    applyTagToHole(selectedHoleIndex, currentValue)
                }
            }

            background: Rectangle {
                color: "#3A3A3A"
                border.color: "#555555"
                border.width: 1
                radius: 3
            }

            contentItem: Text {
                text: tagSelector.displayText
                color: "#FFFFFF"
                font.pixelSize: 14
                verticalAlignment: Text.AlignVCenter
                leftPadding: 10
            }
        }
    }

    // JavaScript函数
    function toggleEmptyWellTag(holeIndex) {
        console.log("切换空孔标记，孔位:", holeIndex + 1)
        ChamberDataManager.toggleEmptyWellTag(chamberId, holeIndex)
    }



    function selectHoleForTagging(holeIndex) {
        console.log("选中孔位进行标记:", holeIndex + 1)

        // 清除之前的选中状态
        if (selectedHoleIndex >= 0) {
            ChamberDataManager.setHoleSelectedForTagging(chamberId, selectedHoleIndex, false)
        }

        // 设置新的选中状态
        selectedHoleIndex = holeIndex
        showTagSelector = true
        ChamberDataManager.setHoleSelectedForTagging(chamberId, holeIndex, true)
    }

    function selectHoleForAdjustment(holeIndex) {
        console.log("选中孔位进行图像调整:", holeIndex + 1)
        // 更新选中的调整孔位
        selectedAdjustmentHole = holeIndex
        // 图像调整逻辑
        ChamberDataManager.selectHoleForAdjustment(chamberId, holeIndex)
    }

    function applyTagToHole(holeIndex, tagType) {
        console.log("应用标记到孔位:", holeIndex + 1, "标记类型:", tagType)

        // 应用标记
        ChamberDataManager.setHoleTag(chamberId, holeIndex, tagType)

        // 清除选中状态
        ChamberDataManager.setHoleSelectedForTagging(chamberId, holeIndex, false)
        selectedHoleIndex = -1
        showTagSelector = false
    }

    // 背景状态检查定时器
    Timer {
        id: backgroundCheckTimer
        interval: 1000  // 每秒检查一次
        running: true
        repeat: true
        onTriggered: {
            // 如果不在图像调整状态，但背景不是正确状态，则修复
            if (!isAdjustingImage && (backgroundCanvas.rightRadius !== 295 || bottomBackground.width !== 1890)) {
                console.log("🔧 检测到背景状态异常，自动修复 - rightRadius:", backgroundCanvas.rightRadius, "width:", bottomBackground.width)
                resetBackgroundToOriginal()
            }
        }
    }

    // 组件加载完成后的初始化
    Component.onCompleted: {
        console.log("舱室详情界面加载完成，舱室ID:", chamberId, "初始状态:", initialState)

        // 强制设置正确的初始状态
        isAdjustingImage = false
        bottomBackground.width = 1890

        // 使用强制重置函数
        backgroundCanvas.forceReset()

        console.log("背景已强制重置为初始状态 - rightRadius:", backgroundCanvas.rightRadius)
    }

    // 右侧弹出键盘 - 使用和患者列表相同的弹出方式
    FixedDesignKeyboard {
        id: chamberKeyboard
        anchors.fill: parent
        z: keyboardVisible ? 2000 : -1  // 只有键盘可见时才在最上层
        enabled: keyboardVisible  // 只有键盘可见时才响应事件
    }

    // 键盘触发区域 - 双击右下角弹出键盘
    Rectangle {
        id: keyboardTriggerArea
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        width: 100
        height: 100
        color: "transparent"
        z: 1500

        // 提示文字
        Text {
            anchors.centerIn: parent
            text: "双击\n弹出键盘"
            color: "#6B7280"
            font.pixelSize: 12
            font.family: "Microsoft YaHei"
            horizontalAlignment: Text.AlignHCenter
            opacity: keyboardTriggerMouseArea.containsMouse ? 0.8 : 0.3
        }

        MouseArea {
            id: keyboardTriggerMouseArea
            anchors.fill: parent
            hoverEnabled: true
            onDoubleClicked: {
                // console.log("🎹 ChamberInfo - 双击触发键盘弹出")
                chamberKeyboard.showKeyboard(null)
            }
        }
    }


}