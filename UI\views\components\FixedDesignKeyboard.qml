import QtQuick 2.15
import QtQuick.Controls 2.15

// 修复版设计图键盘 - 保持原来的设计图样式，解决技术问题
Item {
    id: keyboardManager

    property var currentInputField: null
    property bool keyboardVisible: false

    // 自己的候选词管理
    property string pinyinBuffer: ""
    property var candidateWords: []
    property bool showCandidates: false

    // 候选词选择信号
    signal candidateSelected(string text)

    // 处理候选词选择
    onCandidateSelected: function(text) {
        console.log("选择候选词:", text)
        insertText(text)
        multiLangKeyboard.clearPinyinBuffer()
        clearPinyinBuffer()
    }

    // 专业中文输入法引擎
    ProfessionalChineseEngine {
        id: professionalEngine
    }
    
    // 键盘容器
    Rectangle {
        id: keyboard
        
        // 位置与患者列表一致 - 右侧弹出
        width: 1920 // 固定为屏幕一半宽度
        height: 500  // 调整键盘高度为500px
        anchors.right: parent.right // 锚点到父容器右侧
        anchors.bottom: parent.bottom
        anchors.bottomMargin: keyboardVisible ? 10 : -parent.height
        
        // 深色背景
        color: "#1E2233"
        radius: 12
        
        // 弹出动画
        Behavior on y {
            PropertyAnimation {
                duration: 300
                easing.type: Easing.OutCubic
            }
        }
        

        
        // 使用原来的设计图键盘，但修复问题
        VirtualKeyboardPerfect {
            id: multiLangKeyboard
            anchors.fill: parent
            anchors.margins: 0
            
            // 隐藏VirtualKeyboardPerfect的背景，使用外层背景
            color: "transparent"
            border.width: 0
            
            Component.onCompleted: {
                currentLanguage = "CN"  // 默认中文
                // console.log("🌐 修复版设计图键盘初始化完成")

                // 强制隐藏内部候选词，使用外部候选词
                hideInternalCandidates = true

                // 调试信息
                // console.log("📝 初始候选词数量:", candidateWords.length)
                // console.log("📝 初始拼音缓冲区:", pinyinBuffer)

                // 调整边距，确保按键在容器内，避免与候选词重叠
                topMargin = 20  // 减少顶部边距，因为候选词已经在外部显示
                bottomMargin = 15
                leftMargin = 85
                rightMargin = 85
            }

            // 监听候选词变化，同步到外层
            onCandidateWordsChanged: {
                // console.log("🔽 VirtualKeyboardPerfect候选词变化:", candidateWords.length, "个候选词")
                // console.log("🔽 候选词内容:", JSON.stringify(candidateWords))

                // 限制候选词数量为8个（两排，每排4个）
                var limitedCandidates = candidateWords.length > 8 ? candidateWords.slice(0, 8) : candidateWords

                // 同时更新VirtualKeyboardPerfect内部的候选词
                if (candidateWords.length !== limitedCandidates.length) {
                    candidateWords = limitedCandidates
                    // console.log("📝 VirtualKeyboardPerfect内部候选词已限制为:", limitedCandidates.length, "个")
                }

                // 同步候选词数据到外层
                keyboardManager.candidateWords = limitedCandidates
                keyboardManager.showCandidates = limitedCandidates.length > 0

                // console.log("📝 同步到外层的候选词数量:", limitedCandidates.length)
                // console.log("📝 外层候选词内容:", JSON.stringify(limitedCandidates))


            }

            onPinyinBufferChanged: {
                // console.log("📝 拼音缓冲区变化:", pinyinBuffer, "长度:", pinyinBuffer.length)

                // 限制拼音长度为20个字符，不允许继续输入
                if (pinyinBuffer.length > 20) {
                    // console.log("📝 拼音长度超过20字符限制，阻止继续输入")
                    // 恢复到20个字符
                    pinyinBuffer = pinyinBuffer.substring(0, 20)
                    // console.log("📝 拼音缓冲区已截断为:", pinyinBuffer)
                    return
                }

                // 同步拼音缓冲区到外层
                keyboardManager.pinyinBuffer = pinyinBuffer
            }

            // 拦截按键事件，添加我们自己的中文输入处理
            onKeyPressed: function(key) {
                console.log("📝 按键事件:", key, "当前语言:", currentLanguage)

                // 中文模式下的字母输入
                if (currentLanguage === "CN" && /^[a-zA-Z]$/.test(key)) {
                    // 限制拼音长度，避免无限累加
                    if (keyboardManager.pinyinBuffer.length >= 25) {
                        // console.log("📝 拼音长度达到限制，自动提交第一个候选词")
                        // 自动选择第一个候选词
                        if (keyboardManager.candidateWords.length > 0) {
                            keyboardManager.insertText(keyboardManager.candidateWords[0])
                            keyboardManager.clearPinyinBuffer()
                        }
                        // 然后添加新的字母
                        keyboardManager.pinyinBuffer = key.toLowerCase()
                    } else {
                        keyboardManager.pinyinBuffer += key.toLowerCase()
                    }
                    keyboardManager.updateCandidates()
                    return  // 不传递给默认处理
                }

                // 中文模式下的数字选择候选词
                if (currentLanguage === "CN" && /^[1-9]$/.test(key) && keyboardManager.candidateWords.length > 0) {
                    var index = parseInt(key) - 1
                    if (index < keyboardManager.candidateWords.length) {
                        keyboardManager.insertText(keyboardManager.candidateWords[index])
                        keyboardManager.clearPinyinBuffer()
                        return
                    }
                }

                // 其他按键正常处理
                keyboardManager.insertText(key)
            }

            onBackspacePressed: {
                console.log("📝 退格键事件")

                // 中文模式下优先删除拼音缓冲区
                if (currentLanguage === "CN" && keyboardManager.pinyinBuffer.length > 0) {
                    keyboardManager.pinyinBuffer = keyboardManager.pinyinBuffer.slice(0, -1)
                    keyboardManager.updateCandidates()
                } else {
                    keyboardManager.deleteText()
                }
            }

            onSpacePressed: {
                console.log("📝 空格键事件")

                // 中文模式下如果有候选词，选择第一个
                if (currentLanguage === "CN" && keyboardManager.candidateWords.length > 0) {
                    keyboardManager.insertText(keyboardManager.candidateWords[0])
                    keyboardManager.clearPinyinBuffer()
                } else {
                    keyboardManager.insertText(" ")
                }
            }

            onEnterPressed: {
                keyboardManager.insertText("\n")
            }

            onChineseTextSelected: function(text) {
                keyboardManager.insertText(text)
            }
            
            onLanguageChanged: function(language) {
                console.log("🌐 语言切换到:", language)
            }
            
            onHideKeyboard: {
                keyboardManager.hideKeyboard()
            }
        }
    }
    
    // 搜狗风格候选词区域 - 在键盘上方，与键盘按键完全对齐
    Rectangle {
        id: separatorLine
        anchors.bottom: keyboard.top
        anchors.bottomMargin: 5
        anchors.horizontalCenter: keyboard.horizontalCenter
        width: keyboard.width - 170
        height: 1
        color: "#4A5568"
        visible: candidateArea.visible
    }

    Rectangle {
        id: candidateArea
        anchors.bottom: separatorLine.top
        anchors.bottomMargin: 0
        anchors.horizontalCenter: keyboard.horizontalCenter
        width: keyboard.width - 170  // 与键盘按键区域宽度一致（减去左右边距85+85=170）
        height: contentColumn.height + 16 // 根据内容动态调整高度，并增加上下边距
        color: "#1E2233"
        radius: 8
        visible: (keyboardManager.candidateWords.length > 0) && keyboardVisible
        z: 1002

        // 调试信息
        onVisibleChanged: {
            // console.log("📝 候选词区域可见性:", visible, "位置:", x, y, "尺寸:", width, height)
            // console.log("📝 键盘位置:", keyboard.x, keyboard.y, "键盘可见:", keyboardVisible)
        }

        // 阴影效果
        Rectangle {
            anchors.fill: parent
            anchors.topMargin: 2
            anchors.leftMargin: 2
            color: "#00000020"
            radius: 8
            z: -1
        }

        Column {
            id: contentColumn
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.top: parent.top
            anchors.leftMargin: 15  // 只保留左边距，与键盘按键对齐
            anchors.rightMargin: 15  // 只保留右边距，与键盘按键对齐
            anchors.topMargin: 12  // 增加顶部边距以显示完整的拼音
            spacing: 4

            // 输入字母显示行
            Rectangle {
                width: parent.width
                height: 20
                color: "transparent"

                Row {
                    anchors.left: parent.left
                    spacing: 8

                    Text {
                        text: pinyinBuffer || ""
                        color: "#FFFFFF"
                        font.pixelSize: 14
                        font.weight: Font.Bold
                        font.family: "Microsoft YaHei"
                    }

                    Rectangle {
                        width: 1
                        height: 16
                        color: "#D1D5DB"
                        visible: pinyinBuffer.length > 0 && keyboardManager.candidateWords.length > 0
                    }

                    Text {
                        text: getLanguageIndicator(multiLangKeyboard.currentLanguage)
                        color: "#6B7280"
                        font.pixelSize: 12
                        font.family: "Microsoft YaHei"
                    }
                }
            }

            // 候选词显示区域 - 恢复两排显示
            Rectangle {
                id: mobileCandidateArea
                width: parent.width
                height: 72  // 恢复两排高度
                color: "transparent"
                visible: keyboardManager.candidateWords.length > 0

                // 调试信息
                onVisibleChanged: {
                    // console.log("� 候选词区域可见性:", visible, "候选词数量:", keyboardManager.candidateWords.length)
                }

                // 候选词流式布局 - 恢复自动换行
                Flow {
                    anchors.fill: parent
                    anchors.margins: 8
                    spacing: 8

                    Repeater {
                        model: Math.min(keyboardManager.candidateWords.length, 8)  // 最多显示8个候选词

                        Rectangle {
                            property string candidateText: keyboardManager.candidateWords[index] || ""

                            width: Math.max(80, candidateContent.width + 20)
                            height: 32
                            color: candidateMouseArea.pressed ? "#3B82F6" :
                                   candidateMouseArea.containsMouse ? "#4A5568" :
                                   (index === 0 ? "#4A5568" : "transparent")
                            radius: 4
                            border.color: "transparent"

                            Row {
                                id: candidateContent
                                anchors.centerIn: parent
                                spacing: 4

                                Text {
                                    text: (index + 1).toString()
                                    color: "#9CA3AF"
                                    font.pixelSize: 12
                                    font.family: "Microsoft YaHei"
                                }

                                Text {
                                    text: parent.parent.candidateText
                                    color: "#FFFFFF"
                                    font.pixelSize: 14
                                    font.weight: index === 0 ? Font.Bold : Font.Normal
                                    font.family: "Microsoft YaHei"
                                }
                            }

                            MouseArea {
                                id: candidateMouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                onClicked: {
                                    // console.log("🖱️ 点击候选词:", parent.candidateText)
                                    keyboardManager.candidateSelected(parent.candidateText)
                                }
                            }
                        }
                    }
                }
            }
        }


    }

    // 候选词管理函数
    function updateCandidates() {
        if (pinyinBuffer.length > 0) {
            candidateWords = professionalEngine.getCandidateWords(pinyinBuffer)
            showCandidates = candidateWords.length > 0
            // console.log("📝 更新候选词:", pinyinBuffer, "→", candidateWords.length, "个候选词")
            // console.log("📝 候选词内容:", JSON.stringify(candidateWords))
        } else {
            candidateWords = []
            showCandidates = false
        }
    }

    function clearPinyinBuffer() {
        pinyinBuffer = ""
        candidateWords = []
        showCandidates = false
        // console.log("📝 清空拼音缓冲区和候选词")
    }

    // 显示键盘
    function showKeyboard(inputField) {
        // console.log("🌐 显示修复版设计图键盘")
        currentInputField = inputField
        keyboardVisible = true

        // 键盘位置已在基本属性中设置，与患者列表一致
        // console.log("🔧 键盘显示 - 位置由y属性控制，与患者列表一致")

        // 调试候选词状态
        // console.log("📝 键盘显示时候选词状态:", multiLangKeyboard.candidateWords.length)
        // console.log("📝 键盘显示时拼音缓冲区:", multiLangKeyboard.pinyinBuffer)
        // console.log("📝 键盘显示时当前语言:", multiLangKeyboard.currentLanguage)

        if (inputField && inputField.forceActiveFocus) {
            inputField.forceActiveFocus()
        }
    }
    
    // 隐藏键盘
    function hideKeyboard() {
        console.log("🌐 隐藏修复版设计图键盘")
        keyboardVisible = false
        keyboard.y = parent.height
        currentInputField = null
        
        // 清空状态
        multiLangKeyboard.pinyinBuffer = ""
        multiLangKeyboard.candidateWords = []
        multiLangKeyboard.showCandidates = false
    }
    
    // 插入文本
    function insertText(text) {
        if (!currentInputField) return
        
        try {
            if (currentInputField.insert) {
                currentInputField.insert(currentInputField.cursorPosition, text)
            } else if (currentInputField.text !== undefined) {
                var cursorPos = currentInputField.cursorPosition || currentInputField.text.length
                var beforeCursor = currentInputField.text.substring(0, cursorPos)
                var afterCursor = currentInputField.text.substring(cursorPos)
                currentInputField.text = beforeCursor + text + afterCursor
                
                if (currentInputField.cursorPosition !== undefined) {
                    currentInputField.cursorPosition = cursorPos + text.length
                }
            }
        } catch (error) {
            console.log("⚠️ 插入文本失败:", error)
        }
    }
    
    // 删除文本
    function deleteText() {
        if (!currentInputField) return
        
        try {
            if (currentInputField.remove) {
                var cursorPos = currentInputField.cursorPosition
                if (cursorPos > 0) {
                    currentInputField.remove(cursorPos - 1, cursorPos)
                }
            } else if (currentInputField.text !== undefined) {
                var cursorPos = currentInputField.cursorPosition || currentInputField.text.length
                if (cursorPos > 0) {
                    var beforeCursor = currentInputField.text.substring(0, cursorPos - 1)
                    var afterCursor = currentInputField.text.substring(cursorPos)
                    currentInputField.text = beforeCursor + afterCursor
                    
                    if (currentInputField.cursorPosition !== undefined) {
                        currentInputField.cursorPosition = cursorPos - 1
                    }
                }
            }
        } catch (error) {
            console.log("⚠️ 删除文本失败:", error)
        }
    }
    
    // 获取语言显示名称
    function getLanguageName(lang) {
        switch(lang) {
            case "CN": return "🇨🇳 中文"
            case "JP": return "🇯🇵 日文"
            case "EN": return "🇺🇸 英文"
            default: return "未知"
        }
    }

    // 获取语言指示器
    function getLanguageIndicator(lang) {
        switch(lang) {
            case "CN": return "中"
            case "JP": return "あ"
            case "EN": return "EN"
            default: return "?"
        }
    }
}
