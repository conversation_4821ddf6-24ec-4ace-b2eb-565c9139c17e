import QtQuick 2.15
import QtQuick.Controls 2.15

Page {
    id: deviceSettingsPage
    
    property string deviceId: ""
    property string deviceName: ""
    
    // 查找包含导航函数的父级组件
    function findHomeInterface() {
        var stackView = deviceSettingsPage.StackView.view
        if (stackView && stackView.parent) {
            return stackView.parent
        }
        return null
    }
    
    Rectangle {
        anchors.fill: parent
        color: "#1E2233"
        
        // 页面标题区域
        Rectangle {
            id: titleArea
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: 60
            color: "transparent"
            
            Button {
                id: backButton
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                width: 120
                height: 40
                text: "← 返回"
                onClicked: {
                    var homeInterface = findHomeInterface()
                    if (homeInterface && homeInterface.navigateBack) {
                        homeInterface.navigateBack()
                    }
                }
                
                background: Rectangle {
                    color: "#2A3441"
                    radius: 8
                    border.color: "#00D7B3"
                    border.width: 1
                }
                
                contentItem: Text {
                    text: backButton.text
                    font.family: "Helvetica"
                    font.pixelSize: 16
                    color: "#FFFFFF"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            
            Text {
                anchors.centerIn: parent
                text: deviceName + " - 参数设置"
                font.family: "Helvetica"
                font.pixelSize: 28
                font.weight: Font.Bold
                color: "#FFFFFF"
            }
        }
        
        // 设置内容
        Text {
            anchors.centerIn: parent
            text: "设备参数设置页面\n(这里可以添加温度、湿度、CO₂等参数的调节控件)"
            font.family: "Helvetica"
            font.pixelSize: 18
            color: "#CCCCCC"
            horizontalAlignment: Text.AlignHCenter
        }
    }
} 