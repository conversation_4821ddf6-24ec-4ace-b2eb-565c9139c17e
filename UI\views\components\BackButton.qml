import QtQuick 2.15

Rectangle {
    id: backButton
    width: 240
    height: 78
    color: "transparent"

    property string text: "Patients"
    property string iconSource: ""
    signal clicked()

    Row {
        anchors.centerIn: parent
        spacing: 10

        Image {
            width: 78
            height: 78
            source: iconSource
            fillMode: Image.PreserveAspectFit
        }

        Text {
            anchors.verticalCenter: parent.verticalCenter
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            text: backButton.text
        }
    }

    MouseArea {
        anchors.fill: parent
        onClicked: backButton.clicked()
    }
}

