import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: keyButton

    // 对外属性
    property string text: ""
    property bool isSpecialKey: false
    property bool isActive: false
    property bool isPressed: false

    // 信号
    signal clicked()

    // 设计稿样式属性 - 不需要点击变色
    color: {
        if (isActive) {
            return "#4299E1"  // 激活状态（如 Caps Lock、Shift）
        } else {
            return "#6B7280"  // 设计稿中的统一灰色
        }
    }

    radius: 6  // 设计稿中的圆角
    border.width: 0  // 设计稿中没有边框
    
    // 按键文字 - 设计稿样式
    Text {
        id: keyText
        anchors.centerIn: parent
        text: keyButton.text
        color: "#FFFFFF"  // 设计稿中统一使用白色文字
        font.family: "SF Pro Display, Helvetica, Arial"
        font.pixelSize: {
            if (text.length > 3) {
                return 14  // 长文本使用小字体
            } else if (isSpecialKey) {
                return 16  // 特殊键使用中等字体
            } else {
                return 20  // 普通按键使用标准字体
            }
        }
        font.weight: Font.Medium  // 设计稿中使用中等字重
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
    }
    
    // 鼠标交互
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        z: 1000  // 确保在最上层
        enabled: true
        acceptedButtons: Qt.LeftButton
        propagateComposedEvents: false  // 不传播事件

        onPressed: {
            keyButton.isPressed = true
            pressAnimation.start()
        }

        onReleased: {
            keyButton.isPressed = false
            releaseAnimation.start()
        }

        onClicked: {
            keyButton.clicked()
        }
        
        onEntered: {
            if (!keyButton.isPressed) {
                hoverAnimation.start()
            }
        }
        
        onExited: {
            if (!keyButton.isPressed) {
                normalAnimation.start()
            }
        }
    }
    
    // 按下动画
    SequentialAnimation {
        id: pressAnimation
        
        NumberAnimation {
            target: keyButton
            property: "scale"
            to: 0.95
            duration: 50
            easing.type: Easing.OutQuad
        }
    }
    
    // 释放动画
    SequentialAnimation {
        id: releaseAnimation
        
        NumberAnimation {
            target: keyButton
            property: "scale"
            to: 1.0
            duration: 100
            easing.type: Easing.OutBack
            easing.overshoot: 1.1
        }
    }
    
    // 悬停动画 - 只保留缩放效果
    PropertyAnimation {
        id: hoverAnimation
        target: keyButton
        property: "scale"
        to: 1.02
        duration: 150
        easing.type: Easing.OutQuad
    }

    // 正常状态动画
    PropertyAnimation {
        id: normalAnimation
        target: keyButton
        property: "scale"
        to: 1.0
        duration: 150
        easing.type: Easing.OutQuad
    }
    
    // 激活状态变化动画
    Behavior on color {
        ColorAnimation {
            duration: 200
            easing.type: Easing.OutQuad
        }
    }
    
    // 设计稿风格的阴影效果
    Rectangle {
        anchors.fill: parent
        anchors.topMargin: 2
        color: "#4A5568"  // 更浅的阴影色
        radius: keyButton.radius
        z: -10  // 确保在最底层
        opacity: 0.8
    }
}
