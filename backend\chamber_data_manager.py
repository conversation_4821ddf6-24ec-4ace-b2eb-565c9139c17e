#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Geri项目 - 舱室数据管理器
提供舱室环境监测数据的统一管理和实时更新功能
"""

import sys
import random
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from PySide6.QtCore import QObject, Signal, Slot, QTimer, Property
from PySide6.QtQml import qmlRegisterSingletonInstance


class ChamberDataManager(QObject):
    """
    舱室数据管理器 - 单例模式
    负责管理所有舱室的环境监测数据，提供实时数据更新和QML集成
    """

    # Qt信号定义 - 用于通知QML界面数据变化
    dataChanged = Signal(int, str)  # chamberIndex, deviceId
    allDataChanged = Signal()
    chamberStatusChanged = Signal(str, str, 'QVariant')  # deviceId, property, value
    timerStateChanged = Signal(bool)  # 定时器状态变化

    def __init__(self, parent=None):
        super().__init__(parent)

        # 初始化舱室数据
        self._chambers_data = self._initialize_chamber_data()

        # 初始化系统全局数据
        self._system_data = self._initialize_system_data()

        # 创建定时器用于模拟数据更新
        self._update_timer = QTimer()
        self._update_timer.timeout.connect(self._simulate_data_update)
        self._update_timer.setInterval(3000)  # 3秒更新一次

        # 定时器状态
        self._timer_enabled = False

        # 患者分配状态跟踪 - 记录哪些患者被分配到哪个舱室
        self._patient_assignments = {}  # {patientKey: chamberId}

        # print("ChamberDataManager 初始化完成")  # 精简日志

    def _initialize_chamber_data(self) -> List[Dict[str, Any]]:
        """初始化舱室数据 - 包含所有界面显示的文字和状态"""
        chambers = []
        device_ids = ["A01", "A02", "A03", "A04", "A05", "A06",
                     "B01", "B02", "B03", "B04", "B05", "B06"]

        # 示例患者数据 - 混合中英文姓名（保留用于测试，但初始化时不使用）
        patient_data = [
            {
                "firstName": "Jane", "lastName": "O'Shaughnessy", "chineseName": "",  # 英文姓名
                "id1": "113456789-1-3", "id2": "893456781-2-2",
                "birthDate": "26th Nov 1978", "eggAge": 37, "developmentHours": 120,
                "estimatedFertilization": "25th Oct 2023 10:35", "cycleType": "Warmed/Thawed Day 2 Embryo"
            },
            {
                "firstName": "Alice", "lastName": "Johnson", "chineseName": "",  # 英文姓名
                "id1": "223456789-2-1", "id2": "793456782-1-3",
                "birthDate": "15th Mar 1985", "eggAge": 35, "developmentHours": 96,
                "estimatedFertilization": "20th Oct 2023 14:20", "cycleType": "Fresh Day 3 Embryo"
            },
            {
                "firstName": "张", "lastName": "三", "chineseName": "张三",  # 中文姓名
                "id1": "333456789-3-2", "id2": "693456783-3-1",
                "birthDate": "08th Jul 1982", "eggAge": 41, "developmentHours": 144,
                "estimatedFertilization": "18th Oct 2023 09:15", "cycleType": "Frozen Day 5 Blastocyst"
            },
            {
                "firstName": "Emma", "lastName": "Brown", "chineseName": "",  # 英文姓名
                "id1": "443456789-4-1", "id2": "593456784-2-2",
                "birthDate": "12th Dec 1990", "eggAge": 33, "developmentHours": 72,
                "estimatedFertilization": "22nd Oct 2023 16:45", "cycleType": "Fresh Day 2 Embryo"
            },
            {
                "firstName": "李", "lastName": "四", "chineseName": "李四",  # 中文姓名
                "id1": "553456789-5-3", "id2": "493456785-1-1",
                "birthDate": "30th Apr 1987", "eggAge": 36, "developmentHours": 108,
                "estimatedFertilization": "19th Oct 2023 11:30", "cycleType": "Warmed Day 4 Morula"
            },
            {
                "firstName": "Sophia", "lastName": "Garcia", "chineseName": "",  # 英文姓名
                "id1": "663456789-6-2", "id2": "393456786-3-3",
                "birthDate": "05th Sep 1983", "eggAge": 40, "developmentHours": 132,
                "estimatedFertilization": "17th Oct 2023 13:10", "cycleType": "Fresh Day 5 Blastocyst"
            },
            {
                "firstName": "王", "lastName": "五", "chineseName": "王五",  # 中文姓名
                "id1": "773456789-7-1", "id2": "293456787-2-1",
                "birthDate": "22nd Jan 1986", "eggAge": 37, "developmentHours": 84,
                "estimatedFertilization": "21st Oct 2023 08:25", "cycleType": "Frozen Day 3 Embryo"
            }
        ]

        for i, device_id in enumerate(device_ids):
            # 所有舱室初始状态：无患者数据，需要手动分配
            has_patient = False  # 初始状态：所有舱室都没有患者
            is_on = i != 7  # 第8个舱室关闭，其他舱室开启
            patient = None  # 初始状态：无患者数据

            chamber_data = {
                # 基本信息
                "deviceId": device_id,
                "chamberNumber": i + 1,
                "isOn": is_on,
                "isPatientAssigned": has_patient,

                # 患者基本信息
                "patientFirstName": patient["firstName"] if patient else "",
                "patientLastName": patient["lastName"] if patient else "",
                "patientName": f"{patient['firstName']} {patient['lastName']}" if patient else "",
                "chineseName": patient["chineseName"] if patient else "",
                "patientId1": patient["id1"] if patient else "",
                "patientId2": patient["id2"] if patient else "",

                # 患者详细信息
                "birthDate": patient["birthDate"] if patient else "",
                "eggAge": patient["eggAge"] if patient else 0,
                "developmentHours": patient["developmentHours"] if patient else 0,
                "estimatedFertilization": patient["estimatedFertilization"] if patient else "",
                "cycleType": patient["cycleType"] if patient else "",

                # 环境数据
                "temperature": round(36.5 + random.uniform(-1.0, 2.0), 1),
                "tempStatus": "normal" if i % 3 != 0 else "abnormal",
                "flowValue": 15 + random.randint(-5, 5),
                "flowStatus": "normal" if i % 4 != 0 else ("abnormal" if i % 4 == 1 else "purge"),
                "humidityEnabled": i != 4,  # 第5个舱室湿度关闭
                "humidityStatus": "normal" if i % 5 != 0 else ("abnormal" if i != 4 else "off"),

                # 报警状态
                "lidOpenAlarm": i == 0,  # 第1个舱室开盖报警
                "thermalCutoffAlarm": i == 3,  # 第4个舱室热切断报警
                "serviceAlarm": i == 5,  # 第6个舱室检修报警
                "illuminationAlarm": i == 9,  # 第10个舱室照明报警

                # 警告状态
                "cameraOffline": i == 1 or i == 8,  # 第2和第9个舱室相机离线
                "controllerOffline": i == 6,  # 第7个舱室控制器离线

                # 16孔位状态
                "holes": self._initialize_hole_data(i),

                # 培养状态
                "isCultivating": has_patient and random.choice([True, False]),
                "cultivationDuration": random.randint(24, 168) if has_patient else 0,  # 1-7天

                # 界面显示文字
                "statusText": self._get_status_text(i, has_patient, is_on),
                "displayName": self._get_display_name(patient, i),
                "chamberTitle": f"Chamber {i + 1}",
                "deviceLabel": device_id
            }
            chambers.append(chamber_data)

        return chambers

    def _initialize_hole_data(self, chamber_index: int) -> List[Dict[str, Any]]:
        """初始化16孔位数据"""
        holes = []
        for hole_index in range(16):
            # 模拟孔位状态 - 恢复随机逻辑
            has_embryo = random.choice([True, False]) if chamber_index < 7 else False
            # 根据是否有胚胎决定图片路径（相对于QML文件位置）
            if has_embryo:
                image_path = "../../Resource/SamplePic/noEmpty.png"
            else:
                image_path = "../../Resource/SamplePic/Empty.png"

            hole_data = {
                "holeNumber": hole_index + 1,
                "hasEmbryo": has_embryo,
                "embryoStage": random.choice(["2-cell", "4-cell", "8-cell", "Morula", "Blastocyst"]) if has_embryo else "",
                "quality": random.choice(["A", "B", "C"]) if has_embryo else "",
                "isSelected": random.choice([True, False]) if has_embryo else False,
                "cultivationTime": random.randint(12, 144) if has_embryo else 0,  # 小时
                "imagePath": image_path,  # 样本图片路径

                # 微孔标记相关属性
                "hasTag": False,  # 是否有标记
                "tagType": "",  # 标记类型: empty, accepted, cancel, freeze, remove
                "isSelectedForTagging": False  # 是否选中用于标记
            }
            holes.append(hole_data)
        return holes

    def _initialize_system_data(self) -> Dict[str, Any]:
        """初始化系统全局数据"""
        return {
            # CO2数据
            "co2Value": round(5.8 + random.uniform(-0.5, 0.5), 1),
            "co2Status": "normal",  # normal, abnormal

            # 硬盘空间数据
            "diskSpaceGB": 1250,  # GB
            "diskTotalGB": 2000,  # GB
            "diskUsagePercent": 62.5,
            "diskStatus": "normal",  # normal, warning, critical

            # 报警和警告统计
            "totalAlarms": 0,
            "totalWarnings": 0,

            # 系统状态
            "systemOnline": True,
            "lastUpdateTime": datetime.now().isoformat()
        }

    def _get_status_text(self, index: int, has_patient: bool, is_on: bool) -> str:
        """获取舱室状态文字"""
        if not is_on:
            return "Chamber Off"
        elif not has_patient:
            return "Unassigned"
        else:
            return "In Treatment"

    def _get_display_name(self, patient: Dict[str, Any], index: int) -> str:
        """获取显示用的患者姓名"""
        if not patient:
            return ""

        # 某些舱室显示中文姓名
        if index in [2, 5]:  # 第3和第6个舱室显示中文
            return patient.get("chineseName", patient.get("firstName", ""))

        return f"{patient.get('firstName', '')} {patient.get('lastName', '')}"

    @Slot(str, result='QVariant')
    def getChamberDataByDeviceId(self, device_id: str) -> Optional[Dict[str, Any]]:
        """根据设备ID获取舱室数据"""
        for chamber in self._chambers_data:
            if chamber["deviceId"] == device_id:
                return chamber
        return None

    @Slot(int, result='QVariant')
    def getChamberDataByIndex(self, index: int) -> Optional[Dict[str, Any]]:
        """根据索引获取舱室数据"""
        if 0 <= index < len(self._chambers_data):
            return self._chambers_data[index]
        return None

    @Slot(str, str, 'QVariant', result=bool)
    def updateChamberData(self, device_id: str, property_name: str, value: Any) -> bool:
        """更新舱室数据"""
        for i, chamber in enumerate(self._chambers_data):
            if chamber["deviceId"] == device_id:
                if property_name in chamber:
                    chamber[property_name] = value
                    # 发射信号通知数据变化
                    self.dataChanged.emit(i, device_id)
                    self.chamberStatusChanged.emit(device_id, property_name, value)
                    # print(f"更新舱室 {device_id} 的 {property_name} = {value}")  # 精简日志
                    return True
        return False


    @Slot(str, 'QVariant', result=bool)
    def updatePatientDetails(self, device_id: str, patient_data: Dict[str, Any]) -> bool:
        """根据设备ID更新整个患者信息块"""
        chamber = self.getChamberDataByDeviceId(device_id)
        if not chamber:
            return False

        # 字段映射：QML属性名 -> Python数据结构中的键名
        field_map = {
            "firstName": "patientFirstName",
            "lastName": "patientLastName",
            "chineseName": "chineseName",
            "id1": "patientId1",
            "id2": "patientId2",
            "birthDate": "birthDate",
            "eggAge": "eggAge",
            "developmentHours": "developmentHours",
            "estimatedFertilization": "estimatedFertilization",
            "cycleType": "cycleType"
        }

        has_updated = False
        for qml_key, value in patient_data.items():
            python_key = field_map.get(qml_key)
            if python_key and python_key in chamber:
                chamber[python_key] = value
                has_updated = True

        if has_updated:
            # 更新组合字段
            chamber["patientName"] = f"{chamber['patientFirstName']} {chamber['patientLastName']}"
            chamber["displayName"] = self._get_display_name(
                {"firstName": chamber["patientFirstName"],
                 "lastName": chamber["patientLastName"],
                 "chineseName": chamber["chineseName"]},
                self._chambers_data.index(chamber)
            )

            chamber_index = self._chambers_data.index(chamber)
            self.dataChanged.emit(chamber_index, device_id)
            self.allDataChanged.emit() # 通知全局更新
            return True

        return False

    @Slot(result='QVariant')
    def getAllChamberData(self) -> List[Dict[str, Any]]:
        """获取所有舱室数据"""
        return self._chambers_data

    @Slot(str, result=str)
    def getChamberStatusText(self, device_id: str) -> str:
        """获取舱室状态文字"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["statusText"] if chamber else "Unknown"

    @Slot(str, result=str)
    def getPatientDisplayName(self, device_id: str) -> str:
        """获取患者显示姓名"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["displayName"] if chamber else ""

    @Slot(str, result=bool)
    def isChamberOnline(self, device_id: str) -> bool:
        """检查舱室是否在线"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["isOn"] if chamber else False

    @Slot(str, result=bool)
    def hasPatientAssigned(self, device_id: str) -> bool:
        """检查是否分配了患者"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["isPatientAssigned"] if chamber else False

    @Slot(result=int)
    def getTotalChamberCount(self) -> int:
        """获取舱室总数"""
        return len(self._chambers_data)

    @Slot(result=int)
    def getOnlineChamberCount(self) -> int:
        """获取在线舱室数量"""
        return sum(1 for chamber in self._chambers_data if chamber["isOn"])

    @Slot(result=int)
    def getAssignedChamberCount(self) -> int:
        """获取已分配患者的舱室数量"""
        return sum(1 for chamber in self._chambers_data if chamber["isPatientAssigned"])

    # 系统全局数据访问方法
    @Slot(result='QVariant')
    def getSystemData(self) -> Dict[str, Any]:
        """获取系统全局数据"""
        return self._system_data

    @Slot(result=float)
    def getCO2Value(self) -> float:
        """获取CO2数值"""
        return self._system_data["co2Value"]

    @Slot(result=str)
    def getCO2Status(self) -> str:
        """获取CO2状态"""
        return self._system_data["co2Status"]

    @Slot(result=int)
    def getDiskSpaceGB(self) -> int:
        """获取硬盘剩余空间(GB)"""
        return self._system_data["diskSpaceGB"]

    @Slot(result=float)
    def getDiskUsagePercent(self) -> float:
        """获取硬盘使用百分比"""
        return self._system_data["diskUsagePercent"]

    @Slot(result=str)
    def getDiskStatus(self) -> str:
        """获取硬盘状态"""
        return self._system_data["diskStatus"]

    @Slot(result=int)
    def getTotalAlarms(self) -> int:
        """获取总报警数量"""
        # 实时计算报警数量
        alarm_count = 0
        for chamber in self._chambers_data:
            if chamber["lidOpenAlarm"]:
                alarm_count += 1
            if chamber["thermalCutoffAlarm"]:
                alarm_count += 1
            if chamber["serviceAlarm"]:
                alarm_count += 1
            if chamber["illuminationAlarm"]:
                alarm_count += 1
        self._system_data["totalAlarms"] = alarm_count
        return alarm_count

    @Slot(result=int)
    def getTotalWarnings(self) -> int:
        """获取总警告数量"""
        # 实时计算警告数量
        warning_count = 0
        for chamber in self._chambers_data:
            if chamber["cameraOffline"]:
                warning_count += 1
            if chamber["controllerOffline"]:
                warning_count += 1
        self._system_data["totalWarnings"] = warning_count
        return warning_count

    # 孔位数据访问方法
    @Slot(str, result='QVariant')
    def getHoleData(self, device_id: str) -> List[Dict[str, Any]]:
        """获取舱室的16孔位数据"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["holes"] if chamber else []

    @Slot(str, int, result='QVariant')
    def getHoleInfo(self, device_id: str, hole_number: int) -> Optional[Dict[str, Any]]:
        """获取特定孔位信息"""
        holes = self.getHoleData(device_id)
        for hole in holes:
            if hole["holeNumber"] == hole_number:
                return hole
        return None

    # 患者详细信息访问方法
    @Slot(str, result=str)
    def getPatientBirthDate(self, device_id: str) -> str:
        """获取患者出生日期"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["birthDate"] if chamber else ""

    @Slot(str, result=int)
    def getPatientEggAge(self, device_id: str) -> int:
        """获取患者卵龄"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["eggAge"] if chamber else 0

    @Slot(str, result=int)
    def getDevelopmentHours(self, device_id: str) -> int:
        """获取发育时长(小时)"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["developmentHours"] if chamber else 0

    @Slot(str, result=str)
    def getEstimatedFertilization(self, device_id: str) -> str:
        """获取预计受精时间"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["estimatedFertilization"] if chamber else ""

    @Slot(str, result=str)
    def getCycleType(self, device_id: str) -> str:
        """获取周期类型"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["cycleType"] if chamber else ""

    @Slot(str, result=bool)
    def isCultivating(self, device_id: str) -> bool:
        """检查是否正在培养"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["isCultivating"] if chamber else False

    @Slot(str, result=int)
    def getCultivationDuration(self, device_id: str) -> int:
        """获取培养时长(小时)"""
        chamber = self.getChamberDataByDeviceId(device_id)
        return chamber["cultivationDuration"] if chamber else 0

    # 定时器控制方法
    @Slot(bool)
    def setTimerEnabled(self, enabled: bool):
        """设置定时器启用状态"""
        if enabled != self._timer_enabled:
            self._timer_enabled = enabled
            if enabled:
                self._update_timer.start()
                # print("数据更新定时器已启动")  # 精简日志
            else:
                self._update_timer.stop()
                # print("数据更新定时器已停止")  # 精简日志
            self.timerStateChanged.emit(enabled)

    @Slot(result=bool)
    def isTimerEnabled(self) -> bool:
        """获取定时器启用状态"""
        return self._timer_enabled

    @Slot(int)
    def setTimerInterval(self, interval_ms: int):
        """设置定时器间隔（毫秒）"""
        self._update_timer.setInterval(interval_ms)
        # print(f"定时器间隔设置为 {interval_ms} 毫秒")  # 精简日志

    # 患者分配管理方法
    @Slot(str, str, str, str, result=bool)
    def assignPatientToChamber(self, chamber_id: str, first_name: str, last_name: str, patient_id1: str) -> bool:
        """分配患者到舱室"""
        try:
            # 创建患者唯一标识
            patient_key = f"{first_name}_{last_name}_{patient_id1}"

            # 记录患者分配
            self._patient_assignments[patient_key] = chamber_id

            # print(f"患者分配记录：{patient_key} → {chamber_id}")  # 精简日志
            # print(f"当前所有分配：{self._patient_assignments}")  # 精简日志

            return True
        except Exception as e:
            print(f"分配患者失败：{e}")
            return False

    @Slot(str, str, str, result=str)
    def getPatientAssignedChamber(self, first_name: str, last_name: str, patient_id1: str) -> str:
        """获取患者分配的舱室"""
        patient_key = f"{first_name}_{last_name}_{patient_id1}"
        return self._patient_assignments.get(patient_key, "?")

    @Slot(result='QVariant')
    def getAllPatientAssignments(self) -> Dict[str, str]:
        """获取所有患者分配状态"""
        return self._patient_assignments.copy()

    # 微孔标记管理方法
    @Slot(str, int)
    def toggleEmptyWellTag(self, device_id: str, hole_index: int):
        """切换空孔标记"""
        chamber = self.getChamberDataByDeviceId(device_id)
        if chamber and 0 <= hole_index < len(chamber["holes"]):
            hole = chamber["holes"][hole_index]
            if hole["hasTag"] and hole["tagType"] == "empty":
                # 移除空孔标记
                hole["hasTag"] = False
                hole["tagType"] = ""
            else:
                # 添加空孔标记
                hole["hasTag"] = True
                hole["tagType"] = "empty"

            # 发射信号通知更新
            chamber_index = self._chambers_data.index(chamber)
            self.dataChanged.emit(chamber_index, device_id)
            # print(f"切换孔位 {hole_index + 1} 空孔标记: {hole['hasTag']}")  # 精简日志

    @Slot(str, int, bool)
    def setHoleSelectedForTagging(self, device_id: str, hole_index: int, selected: bool):
        """设置孔位选中状态用于标记"""
        chamber = self.getChamberDataByDeviceId(device_id)
        if chamber and 0 <= hole_index < len(chamber["holes"]):
            hole = chamber["holes"][hole_index]
            hole["isSelectedForTagging"] = selected

            # 发射信号通知更新
            chamber_index = self._chambers_data.index(chamber)
            self.dataChanged.emit(chamber_index, device_id)
            # print(f"设置孔位 {hole_index + 1} 选中状态: {selected}")  # 精简日志

    @Slot(str, int, str)
    def setHoleTag(self, device_id: str, hole_index: int, tag_type: str):
        """设置孔位标记"""
        chamber = self.getChamberDataByDeviceId(device_id)
        if chamber and 0 <= hole_index < len(chamber["holes"]):
            hole = chamber["holes"][hole_index]
            hole["hasTag"] = True
            hole["tagType"] = tag_type

            # 发射信号通知更新
            chamber_index = self._chambers_data.index(chamber)
            self.dataChanged.emit(chamber_index, device_id)
            # print(f"设置孔位 {hole_index + 1} 标记类型: {tag_type}")  # 精简日志

    @Slot(str, int)
    def selectHoleForAdjustment(self, device_id: str, hole_index: int):
        """选择孔位进行图像调整"""
        chamber = self.getChamberDataByDeviceId(device_id)
        if chamber:
            # 清除所有孔位的选中状态
            for hole in chamber["holes"]:
                hole["isSelected"] = False

            # 设置指定孔位为选中状态
            if 0 <= hole_index < len(chamber["holes"]):
                chamber["holes"][hole_index]["isSelected"] = True

                # 发射信号通知更新
                chamber_index = self._chambers_data.index(chamber)
                self.dataChanged.emit(chamber_index, device_id)
                # print(f"选择孔位 {hole_index + 1} 进行图像调整")  # 精简日志

    def clearAllHoleTags(self, device_id: str):
        """清除所有孔位的标记（内部方法）"""
        chamber = self.getChamberDataByDeviceId(device_id)
        if chamber:
            for hole in chamber["holes"]:
                hole["hasTag"] = False
                hole["tagType"] = ""
                hole["isSelectedForTagging"] = False

            # 发射信号通知更新
            chamber_index = self._chambers_data.index(chamber)
            self.dataChanged.emit(chamber_index, device_id)
            # print(f"清除舱室 {device_id} 所有孔位标记")  # 精简日志

    def _simulate_data_update(self):
        """模拟数据更新 - 定时器回调（仅更新环境监控数据，不更新患者数据）"""
        # 随机选择几个舱室更新数据
        update_count = random.randint(1, 3)
        updated_chambers = random.sample([c for c in self._chambers_data if c["isOn"]],
                                       min(update_count, len([c for c in self._chambers_data if c["isOn"]])))

        for chamber in updated_chambers:
            # 更新环境数据
            new_temp = 36.5 + random.uniform(-1.0, 2.0)
            chamber["temperature"] = round(new_temp, 1)

            # 更新流量数据
            chamber["flowValue"] = 15 + random.randint(-3, 3)

            # 随机更新状态
            if random.random() < 0.2:  # 20%概率更新状态
                chamber["tempStatus"] = "normal" if random.random() < 0.8 else "abnormal"
                chamber["flowStatus"] = random.choice(["normal", "abnormal", "purge"])
                chamber["humidityStatus"] = random.choice(["normal", "abnormal"]) if chamber["humidityEnabled"] else "off"

            # 更新培养时长
            if chamber["isCultivating"]:
                chamber["cultivationDuration"] += 1  # 每次更新增加1小时

            # 发射信号
            device_id = chamber["deviceId"]
            chamber_index = self._chambers_data.index(chamber)
            self.dataChanged.emit(chamber_index, device_id)

        # 更新系统数据
        if random.random() < 0.3:  # 30%概率更新系统数据
            self._system_data["co2Value"] = round(5.8 + random.uniform(-0.3, 0.3), 1)
            self._system_data["co2Status"] = "normal" if random.random() < 0.9 else "abnormal"

            # 模拟硬盘空间变化
            self._system_data["diskSpaceGB"] = max(100, self._system_data["diskSpaceGB"] - random.randint(0, 5))
            self._system_data["diskUsagePercent"] = round((self._system_data["diskTotalGB"] - self._system_data["diskSpaceGB"]) / self._system_data["diskTotalGB"] * 100, 1)

            # 更新硬盘状态
            if self._system_data["diskUsagePercent"] > 90:
                self._system_data["diskStatus"] = "critical"
            elif self._system_data["diskUsagePercent"] > 80:
                self._system_data["diskStatus"] = "warning"
            else:
                self._system_data["diskStatus"] = "normal"

            self._system_data["lastUpdateTime"] = datetime.now().isoformat()
            self.allDataChanged.emit()

        # print(f"模拟数据更新: 更新了 {len(updated_chambers)} 个舱室的数据")  # 精简日志


# 全局单例实例
_chamber_data_manager_instance = None


def get_chamber_data_manager() -> ChamberDataManager:
    """获取舱室数据管理器单例实例"""
    global _chamber_data_manager_instance
    if _chamber_data_manager_instance is None:
        _chamber_data_manager_instance = ChamberDataManager()
    return _chamber_data_manager_instance


def register_qml_types():
    """注册QML类型"""
    # 注册单例实例到QML
    chamber_manager = get_chamber_data_manager()
    qmlRegisterSingletonInstance(
        ChamberDataManager,
        "ChamberBackend",
        1, 0,
        "ChamberDataManager",
        chamber_manager
    )
    # print("ChamberDataManager 已注册到QML")  # 精简日志


if __name__ == "__main__":
    # 测试代码
    from PySide6.QtGui import QGuiApplication

    app = QGuiApplication(sys.argv)

    # 创建数据管理器
    manager = get_chamber_data_manager()

    # 测试数据访问
    data = manager.getChamberDataByDeviceId("A01")
    print("A01舱室数据:", data)

    # 测试数据更新
    manager.updateChamberData("A01", "temperature", 38.5)

    # 启动定时器
    manager.setTimerEnabled(True)

    print("ChamberDataManager 测试完成")
