import QtQuick 2.15
import QtQuick.Controls 2.15
import Qt5Compat.GraphicalEffects

Popup {
    id: customPopup
    modal: true
    focus: true
    closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
    width: 3840
    height: 720
    x: 0
    y: 0
    padding: 0
    background: Item {}

    // 完美毛玻璃背景效果 - 使用 FastBlur 技术
    Rectangle {
        id: backgroundContainer
        anchors.fill: parent
        color: "transparent"

        // 背景截图层 - 单层高质量模糊
        Image {
            id: backgroundImage
            anchors.fill: parent
            fillMode: Image.PreserveAspectCrop
            visible: source != ""

            // 应用高质量模糊效果
            layer.enabled: true
            layer.effect: FastBlur {
                radius: 120  // 增强模糊效果，完全模糊文字
                transparentBorder: true
                cached: true
            }
        }

        // 毛玻璃效果层 - 精细调整
        Rectangle {
            anchors.fill: parent
            color: "transparent"

            // 第一层：增强的深色遮罩，提高对比度
            Rectangle {
                anchors.fill: parent
                color: Qt.rgba(0.02, 0.03, 0.06, 0.65)  // 65% 近黑深蓝色遮罩
            }

            // 第二层：增强的白色毛玻璃效果
            Rectangle {
                anchors.fill: parent
                color: Qt.rgba(1, 1, 1, 0.12)  // 12% 白色叠加，雾状效果
            }

            // 第三层：细腻的渐变效果
            Rectangle {
                anchors.fill: parent
                gradient: Gradient {
                    orientation: Gradient.Vertical
                    GradientStop {
                        position: 0.0
                        color: Qt.rgba(1, 1, 1, 0.08)  // 顶部轻微增亮
                    }
                    GradientStop {
                        position: 0.3
                        color: Qt.rgba(1, 1, 1, 0.06)  // 上部雾状
                    }
                    GradientStop {
                        position: 0.7
                        color: Qt.rgba(1, 1, 1, 0.04)  // 下部雾状
                    }
                    GradientStop {
                        position: 1.0
                        color: Qt.rgba(0.01, 0.015, 0.03, 0.25)  // 底部近黑深蓝色暗化
                    }
                }
            }

            // 额外的模糊增强层
            Rectangle {
                anchors.fill: parent
                color: "transparent"

                // 使用FastBlur再次处理，确保完全模糊
                layer.enabled: true
                layer.effect: FastBlur {
                    radius: 40
                    transparentBorder: true
                    cached: true
                }
            }

            // 第四层：细微的噪点纹理模拟
            Rectangle {
                anchors.fill: parent
                color: "transparent"

                // 使用多个小点模拟细腻纹理
                Repeater {
                    model: 120
                    Rectangle {
                        x: Math.random() * parent.width
                        y: Math.random() * parent.height
                        width: Math.random() * 2 + 0.5
                        height: Math.random() * 2 + 0.5
                        color: Qt.rgba(1, 1, 1, Math.random() * 0.07 + 0.02)
                        radius: width / 2
                        opacity: Math.random() * 0.2 + 0.05
                    }
                }
            }
        }
    }

    // 内容区域
    Rectangle {
        id: contentArea
        width: 1920
        height: 380
        anchors.centerIn: parent
        color: "transparent"

        Text {
            id: mainTitle
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.top
            anchors.topMargin: 60
            text: "Was the dish removed?"
            font.family: "SF Pro Display, Helvetica, Arial"
            font.bold: true
            font.weight: Font.Bold
            font.pixelSize: 72
            color: "white"  // 白色文字
            horizontalAlignment: Text.AlignHCenter

            // 文字阴影效果 - 纯 QML 实现
            Text {
                anchors.centerIn: parent
                anchors.verticalCenterOffset: 2
                anchors.horizontalCenterOffset: 1
                text: mainTitle.text
                font: mainTitle.font
                color: "#40000000"
                horizontalAlignment: Text.AlignHCenter
                z: -1
            }
        }

        Text {
            id: subTitle
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: mainTitle.bottom
            anchors.topMargin: 24
            text: "If the dish was removed, it is recommended that\na new image capture and image adjustment is done"
            font.family: "SF Pro Display, Helvetica, Arial"
            font.pixelSize: 28
            color: "white"  // 白色文字
            horizontalAlignment: Text.AlignHCenter
            wrapMode: Text.WordWrap
            lineHeight: 1.2

            // 文字阴影效果 - 纯 QML 实现
            Text {
                anchors.centerIn: parent
                anchors.verticalCenterOffset: 1
                text: subTitle.text
                font: subTitle.font
                color: "#30000000"
                horizontalAlignment: Text.AlignHCenter
                wrapMode: Text.WordWrap
                lineHeight: 1.2
                z: -1
            }
        }



        // 按钮容器
        Row {
            anchors.top: subTitle.bottom
            anchors.topMargin: 60
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: 80

            // 取消按钮 - 毛玻璃风格
            Rectangle {
                id: cancelButton
                width: 340
                height: 88
                color: "#E5FFFFFF"  // 半透明白色背景
                radius: 44
                border.color: "#30000000"
                border.width: 1

                // 按钮阴影 - 纯 QML 实现
                Rectangle {
                    anchors.fill: parent
                    anchors.topMargin: 4
                    anchors.leftMargin: 2
                    color: "#20000000"
                    radius: 44
                    z: -1
                }

                Rectangle {
                    anchors.fill: parent
                    anchors.topMargin: 6
                    anchors.leftMargin: 3
                    color: "#10000000"
                    radius: 44
                    z: -2
                }

                Text {
                    anchors.centerIn: parent
                    text: "No. Not removed"
                    font.pixelSize: 28
                    font.family: "SF Pro Display, Helvetica, Arial"
                    font.weight: Font.Medium
                    color: "#2C2C2E"
                }

                MouseArea {
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: customPopup.close()
                    onEntered: parent.color = "#F0FFFFFF"
                    onExited: parent.color = "#E5FFFFFF"
                    onPressed: parent.color = "#DAFFFFFF"
                    onReleased: parent.color = "#F0FFFFFF"
                }
            }

            // 确认按钮 - 红色渐变
            Rectangle {
                id: confirmButton
                width: 340
                height: 88
                radius: 44

                gradient: Gradient {
                    GradientStop { position: 0.0; color: "#FF3B30" }
                    GradientStop { position: 1.0; color: "#FF2D92" }
                }

                // 按钮阴影 - 纯 QML 实现
                Rectangle {
                    anchors.fill: parent
                    anchors.topMargin: 4
                    anchors.leftMargin: 2
                    color: "#40FF0000"
                    radius: 44
                    z: -1
                }

                Rectangle {
                    anchors.fill: parent
                    anchors.topMargin: 6
                    anchors.leftMargin: 3
                    color: "#20FF0000"
                    radius: 44
                    z: -2
                }

                Text {
                    anchors.centerIn: parent
                    text: "Yes, adjust image"
                    font.pixelSize: 28
                    font.family: "SF Pro Display, Helvetica, Arial"
                    font.weight: Font.Medium
                    color: "#FFFFFF"
                }

                MouseArea {
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: customPopup.close()
                    onPressed: parent.opacity = 0.8
                    onReleased: parent.opacity = 1.0
                }
            }
        } // Row 容器结束


    } // Rectangle 容器结束

    // 优化的进入动画 - 模拟 iOS 风格
    enter: Transition {
        ParallelAnimation {
            NumberAnimation {
                property: "opacity"
                from: 0
                to: 1
                duration: 300
                easing.type: Easing.OutCubic
            }
            NumberAnimation {
                target: contentArea
                property: "scale"
                from: 0.8
                to: 1.0
                duration: 300
                easing.type: Easing.OutBack
                easing.overshoot: 1.2
            }
        }
    }

    // 优化的退出动画
    exit: Transition {
        ParallelAnimation {
            NumberAnimation {
                property: "opacity"
                from: 1
                to: 0
                duration: 200
                easing.type: Easing.InCubic
            }
            NumberAnimation {
                target: contentArea
                property: "scale"
                from: 1.0
                to: 0.9
                duration: 200
                easing.type: Easing.InCubic
            }
        }
    }

    // 当弹窗打开时捕获背景
    onAboutToShow: {
        captureBackground()
    }

    // 捕获背景函数
    function captureBackground() {
        Qt.callLater(function() {
            if (customPopup.parent) {
                customPopup.parent.grabToImage(function(result) {
                    if (result && result.url) {
                        backgroundImage.source = result.url
                    }
                })
            }
        })
    }
}