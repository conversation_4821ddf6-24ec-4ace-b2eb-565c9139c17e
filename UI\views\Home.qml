import QtQuick 2.15
import QtQuick.Controls 2.15
import Qt5Compat.GraphicalEffects  // 更改为Qt6兼容模块
import ChamberBackend 1.0
import "./components"

Item {
    id: homeInterface
    
    // 全局属性
    property real scaleRatio: 1.0
    property bool showMenuBar: true
    property bool debugMode: false
    property bool isLockScreenVisible: false  // 锁屏显示状态
    
    // 导航状态
    property int currentMenuIndex: 0  // 0: Dashboard, 1: Patients, 2: Alarms, 3: Settings
    property var navigationHistory: []
    
    // 系统状态属性 - 从Python后端获取
    property bool lockScreenEnabled: true
    property bool co2Normal: ChamberDataManager ? ChamberDataManager.getCO2Status() === "normal" : true
    property string co2Range: ChamberDataManager ? getCO2RangeText() : "5-10%"
    property int alarmCount: ChamberDataManager ? ChamberDataManager.getTotalAlarms() : 1

    // 生成CO2范围文本的函数
    function getCO2RangeText() {
        if (!ChamberDataManager) return "5-10%"

        var co2Value = ChamberDataManager.getCO2Value()
        var status = ChamberDataManager.getCO2Status()

        // 根据CO2状态和数值确定范围
        if (status === "normal") {
            // 正常状态：5-10%
            return "5-10%"
        } else {
            // 异常状态：根据实际值确定范围
            if (co2Value < 5) {
                return "0-5%"
            } else if (co2Value > 10) {
                return "10-15%"
            } else {
                return "5-10%"
            }
        }
    }
    property int warningCount: ChamberDataManager ? ChamberDataManager.getTotalWarnings() : 6
    property bool usbConnected: true
    property int gcaStatus: 1  // 0: disabled, 1: normal, 2: error
    property int storageStatus: {
        if (!ChamberDataManager) return 0
        var usage = ChamberDataManager.getDiskUsagePercent()
        if (usage > 90) return 2  // alarm
        if (usage > 75) return 1  // warning
        return 0  // normal
    }
    property string storageText: ChamberDataManager ? ChamberDataManager.getDiskSpaceGB() + "GB" : "100% Space"
    property string currentTime: "30 September 2024   23:59"

    // 监听Python后端数据变化
    Connections {
        target: ChamberDataManager
        function onAllDataChanged() {
            // 更新系统状态属性
            co2Normal = ChamberDataManager.getCO2Status() === "normal"
            co2Range = getCO2RangeText()
            alarmCount = ChamberDataManager.getTotalAlarms()
            warningCount = ChamberDataManager.getTotalWarnings()

            var usage = ChamberDataManager.getDiskUsagePercent()
            if (usage > 90) storageStatus = 2
            else if (usage > 75) storageStatus = 1
            else storageStatus = 0

            storageText = ChamberDataManager.getDiskSpaceGB() + "GB"
        }
    }
    
    // 全局函数 - 导航管理
    function navigateToMain(menuIndex) {
        globalKeyboard.hideKeyboard()  // 切换主菜单时隐藏键盘
        currentMenuIndex = menuIndex

        // 安全地返回到根视图
        while (stackView.depth > 1) {
            stackView.pop(null, StackView.Immediate)
        }
        
        switch(menuIndex) {
            case 0:
                stackView.push("DashboardView.qml")
                break
            case 1:
                var page = stackView.push("Patients/PatientsView.qml", {}, StackView.Immediate)
                page.addPatientClicked.connect(function() {
                    var addPage = stackView.push("Patients/AddPatientView.qml", { keyboard: globalKeyboard }, StackView.Immediate)

                    // Set focus after the page is loaded
                    if (addPage && addPage.requestInitialFocus) {
                        addPage.requestInitialFocus()
                    }

                    addPage.cancelled.connect(function() {
                        globalKeyboard.hideKeyboard()
                        stackView.pop({immediate: true})
                    })
                    addPage.confirmed.connect(function(newPatientData) {
                        console.log("New Patient to be added:", JSON.stringify(newPatientData, null, 2))
                        // TODO: Call backend patient manager to add the patient
                        globalKeyboard.hideKeyboard()
                        stackView.pop({immediate: true})
                    })
                })
                break
            case 2:
                stackView.push("AlarmsView.qml")
                break
            case 3:
                stackView.push("SettingsView.qml")
                break
        }
    }
    
    function navigateToSubPage(pagePath, properties) {
        if (properties) {
            stackView.push(pagePath, properties)
        } else {
            stackView.push(pagePath)
        }
    }
    
    function navigateBack() {
        if (stackView.depth > 1) {
            stackView.pop()
        }
    }

    // 跳转到舱室详情界面
    function navigateToChamberInfo(chamberId) {
        console.log("导航到舱室详情界面:", chamberId)
        stackView.push("Chamber/ChamberInfo.qml", {
            "chamberId": chamberId,
            "initialState": "assigned"  // 从看板跳转的舱室通常是已分配状态
        })
    }
    
    function showDialog(dialogPath, properties) {
        var component = Qt.createComponent(dialogPath)
        if (component.status === Component.Ready) {
            var dialog = component.createObject(homeInterface, properties || {})
            dialog.open()
            return dialog
        }
    }
    
    // 全局函数 - 锁屏管理
    function showLockScreen() {
        isLockScreenVisible = true
    }
    
    function hideLockScreen() {
        isLockScreenVisible = false
    }
    
    // 全局信号
    signal deviceSelected(string deviceId)
    signal patientSelected(string patientId)
    signal alarmClicked(string alarmId)
    signal settingChanged(string settingKey, var value)
    signal lockRequested()  // 请求锁屏信号
    
    // 顶部菜单栏 - 固定显示
    Rectangle {
        id: menuBar
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.margins: 20
        height: 80
        color: "#1E2233"
        z: 100  // 确保菜单栏在最上层
        
        // 设备名称
        Text {
            x: 0
            y: 0
            width: 608
            height: 74
            text: "Geri - A01"
            font.family: "Helvetica"
            font.pixelSize: 62
            font.weight: Font.Bold
            color: "#FFFFFF"
            verticalAlignment: Text.AlignVCenter
        }
        
        // Dashboard按钮
        Rectangle {
            x: 1306
            y: 0
            width: 282
            height: 70
            color: "#1E2233"
            radius: 35
            
            Row {
                anchors.centerIn: parent
                spacing: 10
                
                Image {
                    width: 56.84
                    height: 35
                    source: currentMenuIndex === 0 ? "../Resource/Image/Dashboard_Menu_Icon_2.png" : "../Resource/Image/Dashboard_Menu_Icon_1.png"
                    fillMode: Image.PreserveAspectFit
                }
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: "Dashboard"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: currentMenuIndex === 0 ? "#FFFFFF" : "#81828B"
                }
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: navigateToMain(0)
            }
        }
        
        // Patients按钮
        Rectangle {
            x: 1608
            y: 0
            width: 282
            height: 70
            color: "#1E2233"
            radius: 35
            
            Row {
                anchors.centerIn: parent
                spacing: 10
                
                Image {
                    width: 44.14
                    height: 50
                    source: currentMenuIndex === 1 ? "../Resource/Image/Patients_Menu_Icon_2.png" : "../Resource/Image/Patients_Menu_Icon_1.png"
                    fillMode: Image.PreserveAspectFit
                }
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: "Patients"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: currentMenuIndex === 1 ? "#FFFFFF" : "#81828B"
                }
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: navigateToMain(1)
            }
        }
        
        // Alarms按钮
        Rectangle {
            x: 1910
            y: 0
            width: 282
            height: 70
            color: "#1E2233"
            radius: 35
            
            Row {
                anchors.left: parent.left
                anchors.leftMargin: 32.49
                anchors.verticalCenter: parent.verticalCenter
                spacing: 10
                
                Image {
                    width: 48.59
                    height: 45
                    source: currentMenuIndex === 2 ? "../Resource/Image/ALarms_Menu_Icon_2.png" : "../Resource/Image/ALarms_Menu_Icon_1.png"
                    fillMode: Image.PreserveAspectFit
                }
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: "Alarms"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: currentMenuIndex === 2 ? "#FFFFFF" : "#81828B"
                }
            }
            
            // 警报计数器
            Row {
                anchors.right: parent.right
                anchors.rightMargin: 15
                anchors.verticalCenter: parent.verticalCenter
                spacing: 4
                
                Rectangle {
                    width: 40
                    height: 40
                    radius: 20
                    gradient: Gradient {
                        GradientStop { position: 0.02; color: "#DA0000" }
                        GradientStop { position: 0.95; color: "#FA007D" }
                    }
                    
                    Text {
                        anchors.centerIn: parent
                        text: alarmCount.toString()
                        font.family: "Helvetica"
                        font.weight: Font.Bold
                        font.pixelSize: 24
                        color: "#FFFFFF"
                    }
                }
                
                Rectangle {
                    width: 40
                    height: 40
                    radius: 20
                    gradient: Gradient {
                        GradientStop { position: 0.03; color: "#FF6E00" }
                        GradientStop { position: 1.0; color: "#FFBA00" }
                    }
                    
                    Text {
                        anchors.centerIn: parent
                        text: warningCount.toString()
                        font.family: "Helvetica"
                        font.weight: Font.Bold
                        font.pixelSize: 24
                        color: "#FFFFFF"
                    }
                }
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: navigateToMain(2)
            }
        }
        
        // Settings按钮
        Rectangle {
            x: 2212
            y: 0
            width: 282
            height: 70
            color: "#1E2233"
            radius: 35
            
            Row {
                anchors.centerIn: parent
                spacing: 10
                
                Image {
                    width: 50
                    height: 50
                    source: currentMenuIndex === 3 ? "../Resource/Image/Setting_Menu_Icon_2.png" : "../Resource/Image/Setting_Menu_Icon_1.png"
                    fillMode: Image.PreserveAspectFit
                }
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    text: "Settings"
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: currentMenuIndex === 3 ? "#FFFFFF" : "#81828B"
                }
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: navigateToMain(3)
            }
        }
        
        // 锁屏按钮
        Rectangle {
            x: 2760
            y: 11
            width: 49
            height: 49
            radius: 24.5
            visible: lockScreenEnabled
            gradient: Gradient {
                GradientStop { position: 0.0; color: "#008FB9" }
                GradientStop { position: 1.0; color: "#00D7B3" }
            }
            rotation: -45
            
            Image {
                anchors.centerIn: parent
                width: 30
                height: 30
                source: "../Resource/Image/Lock_Button.png"
                fillMode: Image.PreserveAspectFit
                rotation: 45
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    console.log("锁屏按钮被点击")
                    showLockScreen()  // 直接显示锁屏界面
                }
            }
        }
        
        // CO2状态指示器
        Rectangle {
            x: 2859
            y: 11
            width: 191
            height: 49
            radius: 24.5
            gradient: Gradient {
                GradientStop { 
                    position: 0.0
                    color: co2Normal ? "#00A605" : "#DA0000"
                }
                GradientStop { 
                    position: 1.0
                    color: co2Normal ? "#00C89B" : "#FA007D"
                }
            }
            
            Image {
                x: 7
                y: 7.5
                width: 53.16
                height: 28.22
                source: "../Resource/Image/CO2_Icon.png"
                fillMode: Image.PreserveAspectFit
            }
            
            Text {
                x: 70
                anchors.verticalCenter: parent.verticalCenter
                width: 70
                height: 29
                text: co2Range
                font.family: "Helvetica"
                font.pixelSize: 24
                color: "#FFFFFF"
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            Rectangle {
                x: 147
                y: 5
                width: 40
                height: 40
                radius: 20
                color: "#FFFFFF"
                
                Image {
                    anchors.centerIn: parent
                    width: 24
                    height: 24
                    source: co2Normal ? "../Resource/Image/OK_icon.png" : "../Resource/Image/Critical_Icon.png"
                    fillMode: Image.PreserveAspectFit
                }
            }
        }
        
        // USB外设状态
        Image {
            x: 3070
            y: 21
            width: 56
            height: 29
            source: "../Resource/Image/USB_Icon.png"
            fillMode: Image.PreserveAspectFit
            visible: usbConnected
        }
        
        // GCA服务器连接状态
        Image {
            x: 3146
            y: 17
            width: 40
            height: 40
            source: "../Resource/Image/GCA_connected_1.png"
            fillMode: Image.PreserveAspectFit
            visible: gcaStatus > 0
        }
        
        // 存储容量
        Row {
            x: 3222
            y: 19
            spacing: 2
            
            Image {
                width: 56
                height: 32
                source: {
                    switch(storageStatus) {
                        case 0: return "../Resource/Image/Space_Normal.png"
                        case 1: return "../Resource/Image/Space_Warning.png"
                        case 2: return "../Resource/Image/Space_Alarms.png"
                        default: return "../Resource/Image/Space_Normal.png"
                    }
                }
                fillMode: Image.PreserveAspectFit
            }
            
            Text {
                anchors.verticalCenter: parent.verticalCenter
                width: 137
                height: 29
                text: storageText
                font.family: "Helvetica"
                font.pixelSize: 24
                color: "#FFFFFF"
                verticalAlignment: Text.AlignVCenter
            }
        }
        
        // 系统时间
        Text {
            id: dateText
            anchors.right: timeText.left
            anchors.rightMargin: 37
            y: 21
            width: 200  // 调整宽度以适应日期
            height: 29
            text: Qt.formatDate(new Date(), "dd MMMM yyyy")
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            verticalAlignment: Text.AlignVCenter
            horizontalAlignment: Text.AlignRight
        }

        Text {
            id: timeText
            anchors.right: parent.right
            anchors.rightMargin: 28
            y: 21
            width: 100  // 调整宽度以适应时间
            height: 29
            text: Qt.formatDateTime(new Date(), "hh:mm")
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            verticalAlignment: Text.AlignVCenter
            horizontalAlignment: Text.AlignLeft
        }
    }
    
    // 主内容区域
    Item {
        id: mainContent
        anchors.top: menuBar.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom

        // 使用StackView管理多级界面
        StackView {
            id: stackView
            anchors.fill: parent
            initialItem: "DashboardView.qml"

            // 禁用所有过渡动画
            pushEnter: null
            pushExit: null
            popEnter: null
            popExit: null
            replaceEnter: null
            replaceExit: null
        }

        // 全局键盘
        FixedDesignKeyboard {
            id: globalKeyboard
            anchors.fill: parent
            z: 1500 // 确保键盘在最上层
        }
    }

    // 功能测试按钮区域
    Row {
        anchors.bottom: parent.bottom
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottomMargin: 20
        spacing: 20

        // 设计图风格弹窗按钮
        Rectangle {
            id: customPopupButton
            width: 120
            height: 40
            color: customPopupMouseArea.pressed ? "#3B82F6" : "#4299E1"
            radius: 8

            Text {
                anchors.centerIn: parent
                text: "设计图风格弹窗"
                color: "#FFFFFF"
                font.pixelSize: 16
            }

            MouseArea {
                id: customPopupMouseArea
                anchors.fill: parent
                onClicked: {
                    var component = Qt.createComponent("CustomPopup.qml");
                    if (component.status === Component.Ready) {
                        var popup = component.createObject(homeInterface);
                        if (popup) {
                            popup.open();
                        }
                    }
                }
            }
        }


    }

    // 全局遮罩层 - 用于显示弹窗
    Rectangle {
        id: overlayLayer
        anchors.fill: parent
        color: "transparent"
        z: 200
        visible: false
        
        MouseArea {
            anchors.fill: parent
            onClicked: {
                // 点击遮罩层关闭弹窗
                overlayLayer.visible = false
            }
        }
    }
    
    // 定时器更新时间
    Timer {
        interval: 1000  // 每秒更新
        running: true
        repeat: true
        onTriggered: {
            var date = new Date()
            currentTime = Qt.formatDateTime(date, "dd MMMM yyyy    hh:mm")
        }
    }
    
    // 锁屏界面层
    Loader {
        id: lockScreenLoader
        anchors.fill: parent
        source: isLockScreenVisible ? "LockScreen.qml" : ""
        z: 1000  // 确保锁屏界面在最上层
        
        onLoaded: {
            if (item && item.hasOwnProperty("unlockCompleted")) {
                item.unlockCompleted.connect(hideLockScreen)
            }
        }
    }
    
    // 初始化
    Component.onCompleted: {
        navigateToMain(0)  // 默认显示Dashboard
    }






}
