import QtQuick 2.15
import QtQuick.Controls 2.15
import ChamberBackend 1.0

Page {
    id: dashboardPage

    property bool showBackButton: false

    // 监听Python后端数据变化
    Connections {
        target: ChamberDataManager
        function onDataChanged(chamberIndex, deviceId) {
            // 数据变化时，Repeater会自动更新对应的舱室
            // console.log("舱室数据更新:", deviceId)
        }
        function onAllDataChanged() {
            // console.log("所有舱室数据更新")
        }
    }
    
    // 查找包含导航函数的父级组件
    function findHomeInterface() {
        var obj = dashboardPage.parent
        while (obj) {
            // Use a more specific property to identify Home.qml, like 'currentMenuIndex'
            if (obj.hasOwnProperty('currentMenuIndex') && obj.navigateToChamberInfo) {
                return obj
            }
            obj = obj.parent
        }
        return null
    }

    // 跳转到舱室详情界面
    function navigateToChamberInfo(chamberId) {
        var homeInterface = findHomeInterface()
        if (homeInterface && homeInterface.navigateToChamberInfo) {
            homeInterface.navigateToChamberInfo(chamberId)
        } else {
            console.log("无法找到导航接口，尝试直接跳转到舱室详情:", chamberId)
        }
    }
    
    Rectangle {
        anchors.fill: parent
        color: "#1E2233"
        
        // 主显示区域
        Rectangle {
            x: 20
            y: 20
            width: 3800
            height: 600
            color: "#1E2233"
            
            // 12个舱室模块 - 6x2布局
            Grid {
                anchors.fill: parent
                columns: 6
                rows: 2
                columnSpacing: 30
                rowSpacing: 18
                
                Repeater {
                    model: 12
                    
                    // 舱室模块组件
                    Item {
                        width: 608
                        height: 280
                        
                        // 从Python后端获取舱室数据
                        property var chamberData: ChamberDataManager.getChamberDataByIndex(index)
                        property bool isChamberOn: chamberData ? chamberData.isOn : false
                        property bool isPatientAssigned: chamberData ? chamberData.isPatientAssigned : false

                        // 底层背景 - 自定义形状
                        Canvas {
                            id: bottomLayer
                            anchors.fill: parent
                            
                            onPaint: {
                                var ctx = getContext("2d");
                                ctx.reset();
                                ctx.fillStyle = "#313542";
                                
                                // 创建自定义圆角矩形路径
                                ctx.beginPath();
                                
                                // 从左上角开始，顺时针绘制
                                ctx.moveTo(40, 0); // 移动到左上角圆角结束位置
                                ctx.lineTo(width - 140, 0); // 上边到右上角圆角开始
                                ctx.arcTo(width, 0, width, 140, 140); // 右上角圆角
                                ctx.lineTo(width, height - 140); // 右边到右下角圆角开始
                                ctx.arcTo(width, height, width - 140, height, 140); // 右下角圆角
                                ctx.lineTo(40, height); // 下边到左下角圆角开始
                                ctx.arcTo(0, height, 0, height - 40, 40); // 左下角圆角
                                ctx.lineTo(0, 40); // 左边到左上角圆角开始
                                ctx.arcTo(0, 0, 40, 0, 40); // 左上角圆角
                                
                                ctx.closePath();
                                ctx.fill();
                            }
                        }
                        
                        // 中层 - 左侧
                        Rectangle {
                            id: leftLayer
                            x: 0
                            y: 0
                            width: 323
                            height: 280
                            color: "#3D404D"
                            radius: 40
                            
                            // 只保留左侧圆角
                            Rectangle {
                                anchors.right: parent.right
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                width: 40
                                color: "#3D404D"
                            }
                        }
                        
                        // 中层 - 右侧（16个小圆组成的大圆）
                        Item {
                            x: 323
                            y: 0
                            width: 285
                            height: 280
                            visible: parent.isChamberOn
                            
                            // 16个小圆，排列成大圆形
                            Repeater {
                                model: 16
                                
                                Rectangle {
                                    width: 39  // 直径39px
                                    height: 39
                                    radius: 19.5  // 半径 = 直径/2
                                    color: "#3D404D"
                                    
                                    // 精确计算16个小圆的位置
                                    // 1号小圆在正上方，index=0对应1号
                                    property real centerX: 285 / 2  // 右侧区域中心X = 142.5
                                    property real centerY: 280 / 2  // 右侧区域中心Y = 140
                                    
                                    // 精确计算椭圆参数以满足边界距离要求
                                    // 1号（上）：距离上边界10px，圆心Y坐标 = 10 + 19.5 = 29.5
                                    // 5号（右）：距离右边界10px，圆心X坐标 = 285 - 10 - 19.5 = 255.5  
                                    // 9号（下）：距离下边界10px，圆心Y坐标 = 280 - 10 - 19.5 = 250.5
                                    // 13号（左）：距离左边界16px，圆心X坐标 = 16 + 19.5 = 35.5
                                    
                                    // 椭圆中心位置：X = (255.5 + 35.5) / 2 = 145.5，Y = (29.5 + 250.5) / 2 = 140
                                    property real ellipseCenterX: (255.5 + 35.5) / 2  // = 145.5
                                    property real ellipseCenterY: (29.5 + 250.5) / 2   // = 140
                                    
                                    // 椭圆半径：radiusX = (255.5 - 35.5) / 2 = 110，radiusY = (250.5 - 29.5) / 2 = 110.5
                                    property real radiusX: (255.5 - 35.5) / 2  // = 110
                                    property real radiusY: (250.5 - 29.5) / 2  // = 110.5
                                    
                                    // 角度计算：1号在正上方（-90度），顺时针排列
                                    property real angle: ((index * 360 / 16) - 90) * Math.PI / 180
                                    
                                    x: ellipseCenterX + radiusX * Math.cos(angle) - width / 2
                                    y: ellipseCenterY + radiusY * Math.sin(angle) - height / 2
                                    
                                    // 可以在这里添加状态图片
                                    Image {
                                        anchors.centerIn: parent
                                        width: 60
                                        height: 60
                                        // source: "" // 根据状态显示不同图片
                                        fillMode: Image.PreserveAspectFit
                                        visible: false // 暂时隐藏，等添加图片资源
                                    }
                                }
                            }
                        }
                        
                        // 舱室编号
                        Text {
                            x: 16
                            y: 2
                            width: 91
                            height: 114
                            text: (index + 1).toString()
                            font.family: "Helvetica"
                            font.pixelSize: 95
                            color: "#FFFFFF"
                            font.letterSpacing: -7.5
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignTop
                        }

                        // “OFF”状态显示
                        Text {
                            x: 122
                            y: 2
                            width: 168
                            height: 114
                            text: "OFF"
                            font.family: "Helvetica"
                            font.pixelSize: 95
                            color: "#FFFFFF"
                            font.letterSpacing: -7.5
                            horizontalAlignment: Text.AlignLeft
                            verticalAlignment: Text.AlignTop
                            visible: !parent.isChamberOn
                        }

                        // “未分配患者”状态显示
                        Image {
                            x: 134
                            y: 23
                            width: 91.95
                            height: 66.86
                            source: "../Resource/Image/Allocate_Patients_1.png"
                            visible: parent.isChamberOn && !parent.isPatientAssigned
                        }
                        
                        // 患者信息区域
                        Item {
                            id: patientInfoArea
                            x: 126
                            y: 17
                            width: 169
                            height: 87
                            visible: parent.isChamberOn && parent.isPatientAssigned
                            
                            // 从Python后端获取患者信息 - 使用安全的属性绑定
                            readonly property string patientName: parent.chamberData && parent.chamberData.patientName ? String(parent.chamberData.patientName) : ""
                            readonly property string patientId: parent.chamberData && parent.chamberData.patientId ? String(parent.chamberData.patientId) : ""
                            readonly property string displayName: parent.chamberData && parent.chamberData.displayName ? String(parent.chamberData.displayName) : ""
                            readonly property string statusText: parent.chamberData && parent.chamberData.statusText ? String(parent.chamberData.statusText) : ""

                            // 患者姓名信息 - 从Python后端获取
                            readonly property string firstName: parent.chamberData && parent.chamberData.patientFirstName ? String(parent.chamberData.patientFirstName) : ""
                            readonly property string lastName: parent.chamberData && parent.chamberData.patientLastName ? String(parent.chamberData.patientLastName) : ""
                            readonly property string id1: parent.chamberData && parent.chamberData.patientId1 ? String(parent.chamberData.patientId1) : ""
                            readonly property string id2: parent.chamberData && parent.chamberData.patientId2 ? String(parent.chamberData.patientId2) : ""
                            readonly property string chineseName: parent.chamberData && parent.chamberData.chineseName ? String(parent.chamberData.chineseName) : ""

                            // 检查是否为中文模式（基于chineseName是否存在）
                            readonly property bool isChineseMode: chineseName.length > 0
                            
                            // 英文模式患者信息
                            Column {
                                anchors.fill: parent
                                visible: !patientInfoArea.isChineseMode
                                
                                // 姓名区域
                                Item {
                                    width: parent.width
                                    height: 58
                                    
                                    // 名 - 第一行
                                    Text {
                                        x: 0
                                        y: 0
                                        width: parent.width
                                        height: 29
                                        text: patientInfoArea.firstName
                                        font.family: "Helvetica"
                                        font.pixelSize: 24
                                        color: "#FFFFFF"
                                        elide: Text.ElideRight
                                        horizontalAlignment: Text.AlignLeft
                                        verticalAlignment: Text.AlignTop
                                    }
                                    
                                    // 姓 - 第二行
                                    Text {
                                        x: 0
                                        y: 29
                                        width: parent.width
                                        height: 29
                                        text: patientInfoArea.lastName
                                        font.family: "Helvetica"
                                        font.pixelSize: 24
                                        color: "#FFFFFF"
                                        elide: Text.ElideRight
                                        horizontalAlignment: Text.AlignLeft
                                        verticalAlignment: Text.AlignTop
                                    }
                                }
                                
                                // ID信息
                                Text {
                                    x: 0
                                    y: 57  // y:70 - y:13 = 57
                                    width: 114
                                    height: 29
                                    text: patientInfoArea.id1 !== "" ? patientInfoArea.id1 : (patientInfoArea.id2 !== "" ? patientInfoArea.id2 : "")
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    opacity: 0.4
                                    elide: Text.ElideRight
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignTop
                                    visible: patientInfoArea.id1 !== "" || patientInfoArea.id2 !== ""
                                }
                            }
                            
                            // 中文模式患者信息
                            Column {
                                anchors.fill: parent
                                visible: patientInfoArea.isChineseMode
                                
                                // 中文姓名
                                Text {
                                    width: parent.width
                                    height: 58
                                    text: patientInfoArea.chineseName
                                    font.family: "Microsoft YaHei"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    wrapMode: Text.Wrap
                                    elide: Text.ElideRight
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                    maximumLineCount: 2
                                }
                                
                                // ID信息
                                Text {
                                    width: 114
                                    height: 29
                                    text: patientInfoArea.id1 !== "" ? patientInfoArea.id1 : (patientInfoArea.id2 !== "" ? patientInfoArea.id2 : "")
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    opacity: 0.4
                                    elide: Text.ElideRight
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignTop
                                    visible: patientInfoArea.id1 !== "" || patientInfoArea.id2 !== ""
                                }
                            }
                        }
                        
                        // 环境信息区域
                        Item {
                            id: envInfoArea
                            x: 17
                            y: 108
                            width: 289
                            height: 155
                            visible: parent.isChamberOn
                            
                            // 从Python后端获取环境数据
                            readonly property real temperature: parent.chamberData ? parent.chamberData.temperature : 0
                            readonly property string tempStatus: parent.chamberData ? parent.chamberData.tempStatus : "initial"
                            readonly property string flowStatus: parent.chamberData ? parent.chamberData.flowStatus : "initial"
                            readonly property int flowValue: parent.chamberData ? parent.chamberData.flowValue : 0
                            readonly property string humidityStatus: parent.chamberData ? parent.chamberData.humidityStatus : "off"
                            readonly property bool humidityEnabled: parent.chamberData ? parent.chamberData.humidityEnabled : false

                            // 报警状态 - 从Python后端获取
                            readonly property bool lidOpenAlarm: parent.chamberData ? parent.chamberData.lidOpenAlarm : false
                            readonly property bool thermalCutoffAlarm: parent.chamberData ? parent.chamberData.thermalCutoffAlarm : false
                            readonly property bool serviceAlarm: parent.chamberData ? parent.chamberData.serviceAlarm : false
                            readonly property bool illuminationAlarm: parent.chamberData ? parent.chamberData.illuminationAlarm : false

                            // 警告状态 - 从Python后端获取
                            readonly property bool cameraOffline: parent.chamberData ? parent.chamberData.cameraOffline : false
                            readonly property bool controllerOffline: parent.chamberData ? parent.chamberData.controllerOffline : false
                            
                            // 1. 温度区域
                            Rectangle {
                                id: temperatureArea
                                x: 0
                                y: 0
                                width: 191
                                height: 49
                                radius: 24
                                color: envInfoArea.tempStatus === "normal" ? "transparent" : (envInfoArea.tempStatus === "abnormal" ? "transparent" : "#313542")
                                
                                // 只保留左上圆角，其他角变直角
                                Rectangle {
                                    anchors.right: parent.right
                                    anchors.top: parent.top
                                    anchors.bottom: parent.bottom
                                    width: 24
                                    color: parent.color
                                }
                                Rectangle {
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    anchors.bottom: parent.bottom
                                    height: 24
                                    color: parent.color
                                }
                                
                                // 温度背景渐变 - 使用Canvas绘制-83度渐变
                                Canvas {
                                    anchors.fill: parent
                                    visible: envInfoArea.tempStatus !== "initial"
                                    
                                    // 监听状态变化以触发重绘
                                    onVisibleChanged: requestPaint()
                                    Component.onCompleted: requestPaint()
                                    
                                    onPaint: {
                                        var ctx = getContext("2d");
                                        ctx.reset();
                                        
                                        // 定义角度和起始位置
                                        var angle = -83;
                                        var startPos = 0.04;
                                        
                                        // 颜色根据状态
                                        var firstColor = envInfoArea.tempStatus === "normal" ? "#00A605" : "#DA0000";
                                        var secondColor = envInfoArea.tempStatus === "normal" ? "#00C89B" : "#FA007D";
                                        
                                        // 计算渐变起点和终点
                                        var rad = angle * Math.PI / 180;
                                        var dx = Math.sin(rad);
                                        var dy = -Math.cos(rad);
                                        
                                        var cx = width / 2;
                                        var cy = height / 2;
                                        
                                        // 对于终点 (positive direction)
                                        var tPos = [];
                                        if (dx !== 0) {
                                            var t = dx > 0 ? (width - cx) / dx : (0 - cx) / dx;
                                            tPos.push(t);
                                        }
                                        if (dy !== 0) {
                                            var t = dy > 0 ? (height - cy) / dy : (0 - cy) / dy;
                                            tPos.push(t);
                                        }
                                        var t = Math.min.apply(null, tPos);
                                        var endX = cx + t * dx;
                                        var endY = cy + t * dy;
                                        
                                        // 对于起点 (negative direction)
                                        var sx = -dx;
                                        var sy = -dy;
                                        var sTPos = [];
                                        if (sx !== 0) {
                                            var st = sx > 0 ? (width - cx) / sx : (0 - cx) / sx;
                                            sTPos.push(st);
                                        }
                                        if (sy !== 0) {
                                            var st = sy > 0 ? (height - cy) / sy : (0 - cy) / sy;
                                            sTPos.push(st);
                                        }
                                        var st = Math.min.apply(null, sTPos);
                                        var startX = cx + st * sx;
                                        var startY = cy + st * sy;
                                        
                                        // 调整起点以匹配 startPos (例如 4%)
                                        var vx = endX - startX;
                                        var vy = endY - startY;
                                        var newStartX = startX + startPos * vx;
                                        var newStartY = startY + startPos * vy;
                                        
                                        // 创建渐变
                                        var gradient = ctx.createLinearGradient(newStartX, newStartY, endX, endY);
                                        gradient.addColorStop(0, firstColor);
                                        gradient.addColorStop(1, secondColor);
                                        
                                        ctx.fillStyle = gradient;
                                        
                                        // 绘制圆角矩形 - 只有左上角有圆角
                                        ctx.beginPath();
                                        ctx.moveTo(24, 0); // 从左上圆角结束位置开始
                                        ctx.lineTo(width, 0); // 上边到右上角
                                        ctx.lineTo(width, height); // 右边到右下角
                                        ctx.lineTo(0, height); // 下边到左下角
                                        ctx.lineTo(0, 24); // 左边到左上角圆角开始
                                        ctx.arcTo(0, 0, 24, 0, 24); // 左上角圆角
                                        ctx.closePath();
                                        ctx.fill();
                                    }
                                }
                                
                                // 温度图标
                                Image {
                                    x: 19
                                    y: (parent.height - height) / 2
                                    width: 20.33
                                    height: 38.99
                                    source: "../Resource/Image/Temperature_Icon.png"
                                    fillMode: Image.PreserveAspectFit
                                }
                                
                                // 实时温度文字
                                Text {
                                    x: 62
                                    y: (parent.height - height) / 2
                                    width: 47
                                    height: 29
                                    text: envInfoArea.temperature.toFixed(1)
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                // 摄氏度固定文字
                                Text {
                                    x: 105
                                    y: 12
                                    width: 18
                                    height: 19
                                    text: "°C"
                                    font.family: "Helvetica"
                                    font.pixelSize: 16
                                    color: "#FFFFFF"
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                // 状态图标
                                Image {
                                    x: 147
                                    y: (parent.height - height) / 2
                                    width: 39
                                    height: 39
                                    source: envInfoArea.tempStatus === "normal" ? "../Resource/Image/OK_icon.png" : "../Resource/Image/Critical_Icon.png"
                                    fillMode: Image.PreserveAspectFit
                                }
                            }
                            
                            // 2. 流量区域
                            Rectangle {
                                id: flowArea
                                x: 0
                                y: 53
                                width: 191
                                height: 49
                                radius: 0
                                color: envInfoArea.flowStatus === "initial" ? "#313542" : "transparent"
                                
                                // 流量背景渐变 - 异常/正常(-83度)，吹扫(102度)
                                Canvas {
                                    anchors.fill: parent
                                    visible: envInfoArea.flowStatus !== "initial"
                                    
                                    // 监听状态变化以触发重绘
                                    onVisibleChanged: requestPaint()
                                    Component.onCompleted: requestPaint()
                                    
                                    onPaint: {
                                        var ctx = getContext("2d");
                                        ctx.reset();
                                        
                                        // 根据状态选择角度和起始位置
                                        var angle = (envInfoArea.flowStatus === "purge" ? 102 : -83);
                                        var startPos = (envInfoArea.flowStatus === "purge" ? 0.0 : 0.04);
                                        
                                        // 颜色根据状态
                                        var firstColor = envInfoArea.flowStatus === "normal" ? "#00A605" :
                                                         envInfoArea.flowStatus === "purge" ? "#00D9FB" : "#DA0000";
                                        var secondColor = envInfoArea.flowStatus === "normal" ? "#00C89B" :
                                                          envInfoArea.flowStatus === "purge" ? "#0041EE" : "#FA007D";
                                        
                                        // 计算渐变起点和终点 (同上)
                                        var rad = angle * Math.PI / 180;
                                        var dx = Math.sin(rad);
                                        var dy = -Math.cos(rad);
                                        
                                        var cx = width / 2;
                                        var cy = height / 2;
                                        
                                        var tPos = [];
                                        if (dx !== 0) {
                                            var t = dx > 0 ? (width - cx) / dx : (0 - cx) / dx;
                                            tPos.push(t);
                                        }
                                        if (dy !== 0) {
                                            var t = dy > 0 ? (height - cy) / dy : (0 - cy) / dy;
                                            tPos.push(t);
                                        }
                                        var t = Math.min.apply(null, tPos);
                                        var endX = cx + t * dx;
                                        var endY = cy + t * dy;
                                        
                                        var sx = -dx;
                                        var sy = -dy;
                                        var sTPos = [];
                                        if (sx !== 0) {
                                            var st = sx > 0 ? (width - cx) / sx : (0 - cx) / sx;
                                            sTPos.push(st);
                                        }
                                        if (sy !== 0) {
                                            var st = sy > 0 ? (height - cy) / sy : (0 - cy) / sy;
                                            sTPos.push(st);
                                        }
                                        var st = Math.min.apply(null, sTPos);
                                        var startX = cx + st * sx;
                                        var startY = cy + st * sy;
                                        
                                        // 调整起点以匹配 startPos
                                        var vx = endX - startX;
                                        var vy = endY - startY;
                                        var newStartX = startX + startPos * vx;
                                        var newStartY = startY + startPos * vy;
                                        
                                        // 创建渐变
                                        var gradient = ctx.createLinearGradient(newStartX, newStartY, endX, endY);
                                        gradient.addColorStop(0, firstColor);
                                        gradient.addColorStop(1, secondColor);
                                        
                                        ctx.fillStyle = gradient;
                                        ctx.fillRect(0, 0, width, height);
                                    }
                                }
                                
                                // 流量图标
                                Image {
                                    x: 13
                                    y: 11
                                    width: 42.03
                                    height: 34.95
                                    source: "../Resource/Image/Flow_Icon.png"
                                    fillMode: Image.PreserveAspectFit
                                }
                                
                                // 流量文字 - 正常和异常状态
                                Text {
                                    x: 62
                                    y: (parent.height - height) / 2
                                    width: 27
                                    height: 29
                                    text: envInfoArea.flowValue.toString()
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    visible: envInfoArea.flowStatus !== "purge"
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                // 流量单位
                                Text {
                                    x: 89
                                    y: 18
                                    width: 48
                                    height: 19
                                    text: "ml/min"
                                    font.family: "Helvetica"
                                    font.pixelSize: 16
                                    color: "#FFFFFF"
                                    visible: envInfoArea.flowStatus !== "purge"
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                // 吹扫文字
                                Text {
                                    x: 62
                                    y: (parent.height - height) / 2
                                    width: 65
                                    height: 29
                                    text: "Purge"
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    visible: envInfoArea.flowStatus === "purge"
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                // 状态图标
                                Image {
                                    x: 147
                                    y: (parent.height - height) / 2
                                    width: 39
                                    height: 39
                                    source: envInfoArea.flowStatus === "normal" ? "../Resource/Image/OK_icon.png" : 
                                            envInfoArea.flowStatus === "purge" ? "../Resource/Image/Purge_Icon.png" : "../Resource/Image/Critical_Icon.png"
                                    fillMode: Image.PreserveAspectFit
                                }
                            }
                            
                            // 3. 湿度区域
                            Rectangle {
                                id: humidityArea
                                x: 0
                                y: 106
                                width: 191
                                height: 49
                                radius: 24
                                color: envInfoArea.humidityStatus === "off" ? "#313542" : "transparent"
                                
                                // 只保留左下圆角，其他角变直角
                                Rectangle {
                                    anchors.right: parent.right
                                    anchors.top: parent.top
                                    anchors.bottom: parent.bottom
                                    width: 24
                                    color: parent.color
                                }
                                Rectangle {
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    anchors.top: parent.top
                                    height: 24
                                    color: parent.color
                                }
                                
                                // 湿度背景渐变 - 使用Canvas绘制-83度渐变
                                Canvas {
                                    anchors.fill: parent
                                    visible: envInfoArea.humidityStatus === "normal" || envInfoArea.humidityStatus === "abnormal"
                                    
                                    // 监听状态变化以触发重绘
                                    onVisibleChanged: requestPaint()
                                    Component.onCompleted: requestPaint()
                                    
                                    onPaint: {
                                        var ctx = getContext("2d");
                                        ctx.reset();
                                        
                                        // 定义角度和起始位置
                                        var angle = -83;
                                        var startPos = 0.04;
                                        
                                        // 颜色根据状态
                                        var firstColor = envInfoArea.humidityStatus === "normal" ? "#00A605" : "#DA0000";
                                        var secondColor = envInfoArea.humidityStatus === "normal" ? "#00C89B" : "#FA007D";
                                        
                                        // 计算渐变起点和终点 (同上)
                                        var rad = angle * Math.PI / 180;
                                        var dx = Math.sin(rad);
                                        var dy = -Math.cos(rad);
                                        
                                        var cx = width / 2;
                                        var cy = height / 2;
                                        
                                        var tPos = [];
                                        if (dx !== 0) {
                                            var t = dx > 0 ? (width - cx) / dx : (0 - cx) / dx;
                                            tPos.push(t);
                                        }
                                        if (dy !== 0) {
                                            var t = dy > 0 ? (height - cy) / dy : (0 - cy) / dy;
                                            tPos.push(t);
                                        }
                                        var t = Math.min.apply(null, tPos);
                                        var endX = cx + t * dx;
                                        var endY = cy + t * dy;
                                        
                                        var sx = -dx;
                                        var sy = -dy;
                                        var sTPos = [];
                                        if (sx !== 0) {
                                            var st = sx > 0 ? (width - cx) / sx : (0 - cx) / sx;
                                            sTPos.push(st);
                                        }
                                        if (sy !== 0) {
                                            var st = sy > 0 ? (height - cy) / sy : (0 - cy) / sy;
                                            sTPos.push(st);
                                        }
                                        var st = Math.min.apply(null, sTPos);
                                        var startX = cx + st * sx;
                                        var startY = cy + st * sy;
                                        
                                        // 调整起点以匹配 startPos
                                        var vx = endX - startX;
                                        var vy = endY - startY;
                                        var newStartX = startX + startPos * vx;
                                        var newStartY = startY + startPos * vy;
                                        
                                        // 创建渐变
                                        var gradient = ctx.createLinearGradient(newStartX, newStartY, endX, endY);
                                        gradient.addColorStop(0, firstColor);
                                        gradient.addColorStop(1, secondColor);
                                        
                                        ctx.fillStyle = gradient;
                                        
                                        // 绘制圆角矩形 - 只有左下角有圆角
                                        ctx.beginPath();
                                        ctx.moveTo(0, 0); // 从左上角开始
                                        ctx.lineTo(width, 0); // 上边到右上角
                                        ctx.lineTo(width, height); // 右边到右下角
                                        ctx.lineTo(24, height); // 下边到左下角圆角开始
                                        ctx.arcTo(0, height, 0, height - 24, 24); // 左下角圆角
                                        ctx.lineTo(0, 0); // 左边到左上角
                                        ctx.closePath();
                                        ctx.fill();
                                    }
                                }
                                
                                // 湿度图标
                                Image {
                                    x: 13
                                    y: 5
                                    width: 48.06
                                    height: 37.57
                                    source: "../Resource/Image/Humidity_Icon.png"
                                    fillMode: Image.PreserveAspectFit
                                }
                                
                                // 湿度状态文字
                                Text {
                                    x: 62
                                    y: (parent.height - height) / 2
                                    width: 48
                                    height: 29
                                    text: envInfoArea.humidityEnabled ? "ON" : "OFF"
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    color: "#FFFFFF"
                                    horizontalAlignment: Text.AlignLeft
                                    verticalAlignment: Text.AlignVCenter
                                }
                                
                                // 状态图标
                                Image {
                                    x: 147
                                    y: (parent.height - height) / 2
                                    width: 39
                                    height: 39
                                    source: envInfoArea.humidityStatus === "normal" ? "../Resource/Image/OK_icon.png" : "../Resource/Image/Critical_Icon.png"
                                    fillMode: Image.PreserveAspectFit
                                }
                            }
                            
                            // 4. 报警区域
                            Rectangle {
                                id: alarmArea
                                x: 195
                                y: 0
                                width: 94
                                height: 102
                                color: (envInfoArea.lidOpenAlarm || envInfoArea.thermalCutoffAlarm || 
                                       envInfoArea.serviceAlarm || envInfoArea.illuminationAlarm) ? "#DA0000" : "#313542"
                                radius: 24
                                
                                // 只保留右上圆角，其他角变直角
                                Rectangle {
                                    anchors.left: parent.left
                                    anchors.top: parent.top
                                    anchors.bottom: parent.bottom
                                    width: 24
                                    color: parent.color
                                }
                                Rectangle {
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    anchors.bottom: parent.bottom
                                    height: 24
                                    color: parent.color
                                }
                                
                                // 开盖超时报警图标
                                Image {
                                    x: 5
                                    y: 5
                                    width: 39.95
                                    height: 37
                                    source: "../Resource/Image/Lid_open_alarm_icon.png"
                                    fillMode: Image.PreserveAspectFit
                                    visible: envInfoArea.lidOpenAlarm
                                }
                                
                                // 热切断报警图标
                                Image {
                                    x: 48.98
                                    y: 5
                                    width: 39.95
                                    height: 37
                                    source: "../Resource/Image/Thermal_cutoff_alarm_icon.png"
                                    fillMode: Image.PreserveAspectFit
                                    visible: envInfoArea.thermalCutoffAlarm
                                }
                                
                                // 舱室检修报警图标
                                Image {
                                    x: 5
                                    y: 58
                                    width: 39.95
                                    height: 37
                                    source: "../Resource/Image/Service_alarm_icon.png"
                                    fillMode: Image.PreserveAspectFit
                                    visible: envInfoArea.serviceAlarm
                                }
                                
                                // 照明异常报警图标
                                Image {
                                    x: 48.98
                                    y: 58
                                    width: 39.95
                                    height: 37
                                    source: "../Resource/Image/Illumination_alarm_icon.png"
                                    fillMode: Image.PreserveAspectFit
                                    visible: envInfoArea.illuminationAlarm
                                }
                            }
                            
                            // 5. 警告区域
                            Rectangle {
                                id: warningArea
                                x: 195
                                y: 106
                                width: 94
                                height: 49
                                color: (envInfoArea.cameraOffline || envInfoArea.controllerOffline) ? "#FF9500" : "#313542"
                                radius: 24
                                
                                // 只保留右下圆角，其他角变直角
                                Rectangle {
                                    anchors.left: parent.left
                                    anchors.top: parent.top
                                    anchors.right: parent.right
                                    height: 24
                                    color: parent.color
                                }
                                Rectangle {
                                    anchors.left: parent.left
                                    anchors.top: parent.top
                                    anchors.bottom: parent.bottom
                                    width: 24
                                    color: parent.color
                                }
                                
                                // 相机故障警告图标
                                Image {
                                    x: 5
                                    y: 3.5
                                    width: 39.95
                                    height: 37
                                    source: "../Resource/Image/camera_offline_icon.png"
                                    fillMode: Image.PreserveAspectFit
                                    visible: envInfoArea.cameraOffline
                                }
                                
                                // 舱室断联警告图标
                                Image {
                                    x: 48.98
                                    y: 3.5
                                    width: 39.95
                                    height: 37
                                    source: "../Resource/Image/controller_offline_icon.png"
                                    fillMode: Image.PreserveAspectFit
                                    visible: envInfoArea.controllerOffline
                                }
                            }
                        }

                        // “舱室已关闭”提示文字
                        Text {
                            x: 51
                            y: 135
                            width: 222
                            height: 87
                            text: "You can turn on the chamber in the settings page"
                            font.family: "Helvetica"
                            font.pixelSize: 24
                            color: "#FFFFFF"
                            horizontalAlignment: Text.AlignHCenter
                            wrapMode: Text.WordWrap
                            visible: !parent.isChamberOn
                        }
                         
                         // 点击区域 - 跳转到舱室详情
                         MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                var deviceId = parent.chamberData ? parent.chamberData.deviceId : ("A" + String(index + 1).padStart(2, '0'))
                                console.log("点击了舱室", index + 1, "设备ID:", deviceId)
                                dashboardPage.navigateToChamberInfo(deviceId)
                            }
                        }
                    }
                }
            }
        }

        // 系统状态栏 - 显示CO2、硬盘空间、报警警告数量
        Rectangle {
            id: systemStatusBar
            x: 0
            y: 650  // 底部位置
            width: 3840
            height: 70
            color: "#2A2D3A"

            Row {
                anchors.centerIn: parent
                spacing: 100

                // CO2状态
                Rectangle {
                    width: 200
                    height: 50
                    color: ChamberDataManager.getCO2Status() === "normal" ? "#4CAF50" : "#F44336"
                    radius: 25

                    Row {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "CO₂:"
                            font.family: "Helvetica"
                            font.pixelSize: 16
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Text {
                            text: ChamberDataManager.getCO2Value().toFixed(1) + "%"
                            font.family: "Helvetica"
                            font.pixelSize: 18
                            font.bold: true
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }

                // 硬盘空间状态
                Rectangle {
                    width: 300
                    height: 50
                    color: {
                        var status = ChamberDataManager.getDiskStatus()
                        if (status === "critical") return "#F44336"
                        if (status === "warning") return "#FF9800"
                        return "#4CAF50"
                    }
                    radius: 25

                    Row {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "Disk:"
                            font.family: "Helvetica"
                            font.pixelSize: 16
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Text {
                            text: ChamberDataManager.getDiskSpaceGB() + "GB (" + ChamberDataManager.getDiskUsagePercent().toFixed(1) + "%)"
                            font.family: "Helvetica"
                            font.pixelSize: 18
                            font.bold: true
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }

                // 报警数量
                Rectangle {
                    width: 150
                    height: 50
                    color: ChamberDataManager.getTotalAlarms() > 0 ? "#F44336" : "#4CAF50"
                    radius: 25

                    Row {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "Alarms:"
                            font.family: "Helvetica"
                            font.pixelSize: 16
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Text {
                            text: ChamberDataManager.getTotalAlarms().toString()
                            font.family: "Helvetica"
                            font.pixelSize: 18
                            font.bold: true
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }

                // 警告数量
                Rectangle {
                    width: 150
                    height: 50
                    color: ChamberDataManager.getTotalWarnings() > 0 ? "#FF9800" : "#4CAF50"
                    radius: 25

                    Row {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "Warnings:"
                            font.family: "Helvetica"
                            font.pixelSize: 16
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }

                        Text {
                            text: ChamberDataManager.getTotalWarnings().toString()
                            font.family: "Helvetica"
                            font.pixelSize: 18
                            font.bold: true
                            color: "#FFFFFF"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                    }
                }
            }
        }
    }
}