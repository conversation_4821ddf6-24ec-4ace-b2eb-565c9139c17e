import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: virtualKeyboard
    width: 1890
    height: 598
    color: "#3C4043"  // 设计图背景色
    radius: 12
    
    // 对外暴露的属性和信号
    property bool capsLock: false
    property bool shiftPressed: false
    property bool showNumbers: false

    // 多语言支持
    property string currentLanguage: "EN"  // EN, CN, JP
    property var supportedLanguages: ["EN", "CN", "JP"]

    // 中文输入法支持
    property string pinyinBuffer: ""  // 拼音缓冲区
    property var candidateWords: []   // 候选词列表
    property bool showCandidates: false  // 是否显示候选词
    property int selectedCandidate: 0    // 选中的候选词索引
    property bool hideInternalCandidates: false  // 是否隐藏内部候选词显示

    // 专业中文输入法引擎
    ProfessionalChineseEngine {
        id: professionalEngine
    }

    // 日文罗马字输入法引擎
    JapaneseRomajiEngine {
        id: japaneseEngine
    }

    signal keyPressed(string key)
    signal backspacePressed()
    signal enterPressed()
    signal spacePressed()
    signal shiftToggled()
    signal capsToggled()
    signal languageChanged(string language)
    signal hideKeyboard()
    signal chineseTextSelected(string text)  // 中文文字选择信号
    
    // 根据设计图计算的精确尺寸
    property real leftMargin: 85
    property real rightMargin: 85
    property real topMargin: 61  // 调整为垂直居中：(598 - 476) / 2 = 61
    property real bottomMargin: 61
    property real availableWidth: width - leftMargin - rightMargin  // 1720px
    property real availableHeight: height - topMargin - bottomMargin  // 476px (5行×88px + 4个间距×12px)
    
    // 按键尺寸计算（精确到像素，确保完美对齐）
    property real keySpacing: 6  // 按键间距
    property real rowSpacing: 12  // 行间距
    property real keyHeight: 88  // 按键高度
    
    // 精确计算每行按键宽度，确保每行总宽度都是1720px
    // 第一行：13个普通键 + 1个退格键，13个间距(78px)
    // 剩余宽度：1720 - 78 = 1642px
    // 13个普通键 + 1个退格键 = 1642px
    // 设普通键113px，退格键 = 1642 - 13*113 = 173px
    property real row1NormalWidth: 113
    property real row1BackspaceWidth: 173
    
    // 第二行：1个Tab + 13个普通键，13个间距(78px)
    // 剩余宽度：1720 - 78 = 1642px
    // Tab + 13个普通键 = 1642px
    // 设普通键113px，Tab = 1642 - 13*113 = 173px
    property real row2NormalWidth: 113
    property real row2TabWidth: 173
    
    // 第三行：1个Caps + 11个普通键 + 1个Enter，12个间距(72px)
    // 剩余宽度：1720 - 72 = 1648px
    // Caps + 11个普通键 + Enter = 1648px
    // 设普通键110px，Caps = 198px，Enter = 1648 - 11*110 - 198 = 240px
    property real row3NormalWidth: 110
    property real row3CapsWidth: 198
    property real row3EnterWidth: 240
    
    // 第四行：1个左Shift + 10个普通键 + 1个右Shift，11个间距(66px)
    // 剩余宽度：1720 - 66 = 1654px
    // 左Shift + 10个普通键 + 右Shift = 1654px
    // 设普通键110px，左Shift = 253px，右Shift = 1654 - 10*110 - 253 = 301px
    property real row4NormalWidth: 110
    property real row4LeftShiftWidth: 253
    property real row4RightShiftWidth: 301
    
    // 第五行：2个Ctrl + 1个Alt + 1个空格 + 1个语言切换 + 1个隐藏键盘，5个间距(30px)
    // 剩余宽度：1720 - 30 = 1690px
    // 2*Ctrl + Alt + 空格 + 语言切换 + 隐藏键盘 = 1690px
    // 设Ctrl=153px, Alt=153px, 语言切换=118px, 隐藏键盘=118px
    // 空格 = 1690 - 2*153 - 153 - 118 - 118 = 1690 - 306 - 153 - 118 - 118 = 995px
    property real row5CtrlWidth: 153
    property real row5AltWidth: 153
    property real row5LangWidth: 118    // 语言切换按钮
    property real row5HideWidth: 118    // 隐藏键盘按钮
    property real row5SpaceWidth: 995
    
    // 处理按键点击
    function handleKeyPress(key) {
        if (key === "SHIFT") {
            shiftPressed = !shiftPressed
            shiftToggled()
            return
        } else if (key === "CAPS") {
            capsLock = !capsLock
            capsToggled()
            return
        } else if (key === "TAB") {
            console.log("⭾ Tab键 - 输出缩进")
            keyPressed("    ")  // 输出4个空格作为缩进
            return
        } else if (key === "CTRL") {
            console.log("⌃ Ctrl键 - 修饰键(暂不处理)")
            // Ctrl键通常与其他键组合使用，这里暂不处理
            return
        } else if (key === "ALT") {
            console.log("⌥ Alt键 - 修饰键(暂不处理)")
            // Alt键通常与其他键组合使用，这里暂不处理
            return
        } else if (key === "HIDE_KEYBOARD") {
            console.log("⌨ 隐藏键盘")
            hideKeyboard()
            return
        } else if (key === "BACKSPACE") {
            console.log("⬅️ 退格键处理 - 语言:", currentLanguage, "缓冲区:", pinyinBuffer)

            // 中文模式下优先删除拼音缓冲区
            if (currentLanguage === "CN" && pinyinBuffer.length > 0) {
                pinyinBuffer = pinyinBuffer.slice(0, -1)
                console.log("🇨🇳 删除中文拼音，剩余:", pinyinBuffer)
                updateCandidates()
            }
            // 日文模式下优先删除罗马字缓冲区
            else if (currentLanguage === "JP" && pinyinBuffer.length > 0) {
                pinyinBuffer = pinyinBuffer.slice(0, -1)
                console.log("🇯🇵 删除日文罗马字，剩余:", pinyinBuffer)
                updateJapaneseCandidates()
            }
            // 如果没有缓冲区内容，删除文本内容
            else {
                console.log("📝 删除文本内容")
                backspacePressed()
            }
            return
        } else if (key === "ENTER") {
            console.log("↩️ Enter键处理 - 语言:", currentLanguage, "候选词:", showCandidates, "数量:", candidateWords.length)

            // 中文模式下如果有候选词，选择第一个
            if (currentLanguage === "CN" && showCandidates && candidateWords.length > 0) {
                console.log("🇨🇳 中文Enter选择候选词:", candidateWords[selectedCandidate])
                selectCandidate(selectedCandidate)
            }
            // 日文模式下如果有候选词，选择第一个
            else if (currentLanguage === "JP" && showCandidates && candidateWords.length > 0) {
                console.log("🇯🇵 日文Enter选择候选词:", candidateWords[selectedCandidate])
                selectCandidate(selectedCandidate)
            }
            // 如果没有候选词，输出换行
            else {
                console.log("📝 Enter输出换行")
                enterPressed()
            }
            return
        } else if (key === "SPACE") {
            console.log("␣ 空格键处理 - 语言:", currentLanguage, "候选词:", showCandidates, "数量:", candidateWords.length)

            // 中文模式下如果有候选词，选择第一个
            if (currentLanguage === "CN" && showCandidates && candidateWords.length > 0) {
                console.log("🇨🇳 中文空格选择候选词:", candidateWords[selectedCandidate])
                selectCandidate(selectedCandidate)
            }
            // 日文模式下如果有候选词，选择第一个
            else if (currentLanguage === "JP" && showCandidates && candidateWords.length > 0) {
                console.log("🇯🇵 日文空格选择候选词:", candidateWords[selectedCandidate])
                selectCandidate(selectedCandidate)
            }
            // 如果没有候选词，输出空格
            else {
                console.log("📝 空格输出空格")
                spacePressed()
            }
            return
        } else if (key === "LANG_SWITCH") {
            switchLanguage()
            return
        } else if (key === "HIDE_KEYBOARD") {
            hideKeyboard()
            return
        }
        
        // 多语言智能处理 - 完全重写
        // console.log("🔍 多语言处理 - 语言:", currentLanguage, "按键:", key, "长度:", key.length)

        // 检查是否为特殊键（应该已经被上面的逻辑处理了）
        var specialKeys = ["TAB", "CTRL", "ALT", "HIDE_KEYBOARD"]
        if (specialKeys.indexOf(key) !== -1) {
            console.log("⚠️ 特殊键未被正确处理:", key)
            return  // 不应该到达这里
        }

        // 统一的字母判断
        var isLetter = /^[a-zA-Z]$/.test(key) && key.length === 1
        var isDigit = /^[1-9]$/.test(key) && key.length === 1
        var isSpace = key === " "

        // console.log("🔍 按键类型判断 - 字母:", isLetter, "数字:", isDigit, "空格:", isSpace)

        // 中文模式处理
        if (currentLanguage === "CN" && isLetter) {
            // console.log("🇨🇳 中文字母处理")
            pinyinBuffer += key.toLowerCase()
            // console.log("🔍 拼音缓冲区:", pinyinBuffer)
            updateCandidates()
            // console.log("🔍 中文候选词数量:", candidateWords.length)
            return
        }

        // 日文模式处理
        else if (currentLanguage === "JP" && isLetter) {
            console.log("🇯🇵 日文字母处理")
            pinyinBuffer += key.toLowerCase()
            console.log("🔍 罗马字缓冲区:", pinyinBuffer)
            updateJapaneseCandidates()
            console.log("🔍 日文候选词数量:", candidateWords.length)
            return
        }

        // 候选词选择 (中文和日文通用)
        else if ((currentLanguage === "CN" || currentLanguage === "JP") && isDigit && showCandidates) {
            console.log("🔢 候选词选择:", key)
            var index = parseInt(key) - 1
            if (index < candidateWords.length) {
                selectCandidate(index)
                return
            }
        }

        // 空格键处理 (中文和日文通用)
        else if ((currentLanguage === "CN" || currentLanguage === "JP") && isSpace) {
            console.log("␣ 空格键处理")
            if (showCandidates && candidateWords.length > 0) {
                selectCandidate(0)
                return
            } else if (pinyinBuffer.length > 0) {
                clearPinyinBuffer()
            }
        }

        // 如果不是特殊处理的按键，继续正常流程
        console.log("➡️ 继续正常按键处理:", key)

        var displayChar = key

        // 字母键：受Caps Lock和Shift影响
        if (key >= "a" && key <= "z") {
            if (shiftPressed || capsLock) {
                displayChar = key.toUpperCase()
            }
        }
        // 数字和符号键：只受Shift影响，不受Caps Lock影响
        else if (shiftPressed && currentLanguage === "EN") {
            switch (key) {
                case "1": displayChar = "!"; break
                case "2": displayChar = "@"; break
                case "3": displayChar = "#"; break
                case "4": displayChar = "$"; break
                case "5": displayChar = "%"; break
                case "6": displayChar = "^"; break
                case "7": displayChar = "&"; break
                case "8": displayChar = "*"; break
                case "9": displayChar = "("; break
                case "0": displayChar = ")"; break
                case "-": displayChar = "_"; break
                case "=": displayChar = "+"; break
                case "[": displayChar = "{"; break
                case "]": displayChar = "}"; break
                case "\\": displayChar = "|"; break
                case ";": displayChar = ":"; break
                case "'": displayChar = "\""; break
                case ",": displayChar = "<"; break
                case ".": displayChar = ">"; break
                case "/": displayChar = "?"; break
                case "`": displayChar = "~"; break
                default: displayChar = key; break
            }
        }

        keyPressed(displayChar)

        // 单次 Shift 后自动取消
        if (shiftPressed && !capsLock) {
            shiftPressed = false
        }
    }

    // 语言切换函数
    function switchLanguage() {
        // 切换语言前先清空中文输入状态
        clearPinyinBuffer()

        var currentIndex = supportedLanguages.indexOf(currentLanguage)
        var nextIndex = (currentIndex + 1) % supportedLanguages.length
        currentLanguage = supportedLanguages[nextIndex]
        languageChanged(currentLanguage)

        console.log("🌐 语言切换到:", currentLanguage, "中文状态已清空")
    }

    // 获取语言显示名称
    function getLanguageDisplayName(lang) {
        switch(lang) {
            case "EN": return "EN"
            case "CN": return "中"
            case "JP": return "あ"
            default: return lang
        }
    }

    // 简单的拼音到汉字映射字典
    function getPinyinDictionary() {
        return {
            "ni": ["你", "尼", "泥", "逆"],
            "hao": ["好", "号", "豪", "毫"],
            "wo": ["我", "握", "沃", "卧"],
            "de": ["的", "得", "德", "地"],
            "shi": ["是", "时", "事", "试", "世", "市"],
            "zhe": ["这", "着", "者", "哲"],
            "ge": ["个", "各", "格", "歌"],
            "ren": ["人", "任", "认", "仁"],
            "ta": ["他", "她", "它", "塔"],
            "men": ["们", "门", "闷", "焖"],
            "you": ["有", "又", "右", "友", "游"],
            "zai": ["在", "再", "载", "栽"],
            "shuo": ["说", "朔", "硕", "烁"],
            "dao": ["到", "道", "倒", "导"],
            "chu": ["出", "初", "除", "处"],
            "lai": ["来", "莱", "赖", "徕"],
            "dou": ["都", "斗", "豆", "逗"],
            "neng": ["能", "嗯"],
            "guo": ["过", "国", "果", "锅"],
            "hui": ["会", "回", "汇", "慧"],
            "qu": ["去", "取", "趣", "区"],
            "shang": ["上", "商", "伤", "赏"],
            "xia": ["下", "夏", "吓", "侠"],
            "kan": ["看", "刊", "砍", "坎"],
            "jian": ["见", "间", "建", "健"],
            "xiang": ["想", "向", "象", "香"],
            "yao": ["要", "药", "腰", "摇"],
            "jin": ["进", "今", "金", "近"],
            "xing": ["行", "性", "星", "兴"],
            "fa": ["发", "法", "罚", "乏"],
            "sheng": ["生", "声", "省", "胜"],
            "huo": ["或", "火", "活", "货"],
            "zuo": ["做", "作", "坐", "左"],
            "wei": ["为", "位", "未", "味", "围"],
            "le": ["了", "乐", "勒"],
            "ma": ["吗", "妈", "马", "麻"],
            "ba": ["吧", "把", "爸", "八"],
            "ne": ["呢", "哪"],
            "a": ["啊", "阿"],
            "o": ["哦", "噢"],
            "en": ["嗯", "恩"],
            "ai": ["爱", "哀", "挨"],
            "bei": ["被", "北", "背", "杯"],
            "gei": ["给", "给"],
            "dui": ["对", "队", "堆"],
            "hen": ["很", "恨", "狠"],
            "da": ["大", "打", "答", "达"],
            "xiao": ["小", "笑", "校", "效"],
            "duo": ["多", "朵", "躲"],
            "shao": ["少", "烧", "勺"],
            "hao": ["好", "号", "豪"],
            "bu": ["不", "步", "布", "部"],
            "mei": ["没", "美", "每", "梅"],
            "cong": ["从", "丛", "聪"],
            "dao": ["到", "道", "倒"],
            "li": ["里", "理", "力", "立", "离"],
            "mian": ["面", "免", "棉"],
            "hou": ["后", "候", "厚"],
            "qian": ["前", "钱", "千", "签"],
            "zhong": ["中", "种", "重", "钟"],
            "wai": ["外", "歪"],
            "nei": ["内", "那"],
            "bian": ["边", "变", "便"],
            "pang": ["旁", "胖"],
            "zuo": ["左", "做", "作"],
            "you": ["右", "有", "又"],
            "tian": ["天", "田", "甜"],
            "di": ["地", "第", "底"],
            "nian": ["年", "念"],
            "yue": ["月", "越", "乐"],
            "ri": ["日", "入"],
            "xing": ["星", "行", "性"],
            "qi": ["期", "气", "起", "七"],
            "dian": ["点", "电", "店"],
            "fen": ["分", "份", "粉"],
            "miao": ["秒", "妙", "苗"],
            "ke": ["可", "课", "客"],
            "yi": ["一", "以", "已", "意", "易"],
            "er": ["二", "而", "儿", "耳"],
            "san": ["三", "散", "伞"],
            "si": ["四", "死", "思", "丝"],
            "wu": ["五", "无", "舞", "武"],
            "liu": ["六", "流", "留"],
            "qi": ["七", "起", "气", "期"],
            "ba": ["八", "把", "爸"],
            "jiu": ["九", "久", "酒"],
            "shi": ["十", "是", "时", "事"]
        }
    }

    // 根据拼音获取候选词 - 使用专业引擎
    function getCandidateWords(pinyin) {
        return professionalEngine.getCandidateWords(pinyin)
    }

    // 智能拼音分词
    function getSmartPinyinCandidates(pinyin, dict) {
        var candidates = []
        var pinyinKeys = Object.keys(dict)

        // 尝试从最长的拼音开始匹配
        for (var len = Math.min(pinyin.length, 6); len >= 2; len--) {
            for (var i = 0; i <= pinyin.length - len; i++) {
                var segment = pinyin.substring(i, i + len)
                if (dict[segment]) {
                    // 找到匹配的拼音段
                    var remaining = pinyin.substring(0, i) + pinyin.substring(i + len)
                    if (remaining.length === 0) {
                        // 完全匹配
                        candidates = candidates.concat(dict[segment])
                    } else if (remaining.length >= 2) {
                        // 递归处理剩余部分
                        var remainingCandidates = getSmartPinyinCandidates(remaining, dict)
                        if (remainingCandidates.length > 0) {
                            // 组合候选词 - 优先常用组合
                            if (i === 0) {
                                // 从前面开始匹配，正常顺序
                                for (var j = 0; j < dict[segment].length && j < 3; j++) {
                                    for (var k = 0; k < remainingCandidates.length && k < 3; k++) {
                                        candidates.push(dict[segment][j] + remainingCandidates[k])
                                    }
                                }
                            } else {
                                // 从后面开始匹配，颠倒顺序
                                for (var k = 0; k < remainingCandidates.length && k < 3; k++) {
                                    for (var j = 0; j < dict[segment].length && j < 3; j++) {
                                        candidates.push(remainingCandidates[k] + dict[segment][j])
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 如果还是没有找到，尝试前缀匹配
        if (candidates.length === 0) {
            for (var key in dict) {
                if (key.startsWith(pinyin) || pinyin.startsWith(key)) {
                    candidates = candidates.concat(dict[key].slice(0, 2)) // 只取前2个
                }
            }
        }

        // 去重并限制数量
        var uniqueCandidates = []
        for (var i = 0; i < candidates.length && uniqueCandidates.length < 9; i++) {
            if (uniqueCandidates.indexOf(candidates[i]) === -1) {
                uniqueCandidates.push(candidates[i])
            }
        }

        return uniqueCandidates
    }

    // 更新候选词
    function updateCandidates() {
        if (currentLanguage === "CN" && pinyinBuffer.length > 0) {
            candidateWords = getCandidateWords(pinyinBuffer)
            showCandidates = true  // 中文输入时始终显示候选词区域，即使没有候选词
            selectedCandidate = 0

            // 如果拼音过长且没有候选词，自动清空重新开始
            if (pinyinBuffer.length > 6 && candidateWords.length === 0) {
                clearPinyinBuffer()
            }
        } else {
            candidateWords = []
            showCandidates = false
            selectedCandidate = 0
        }
    }

    // 更新日文候选词 - 改进版
    function updateJapaneseCandidates() {
        if (currentLanguage === "JP" && pinyinBuffer.length > 0) {
            candidateWords = japaneseEngine.getJapaneseCandidates(pinyinBuffer)
            showCandidates = true  // 日文输入时始终显示候选词区域，即使没有候选词
            selectedCandidate = 0

            console.log("🇯🇵 日文候选词更新:", pinyinBuffer, "→", candidateWords.length, "个候选词")

            // 智能处理无效罗马字组合 - 更宽容的版本
            if (candidateWords.length === 0 && pinyinBuffer.length > 1) {
                // 只在非常明确的无效情况下进行分割
                var shouldAutoSplit = false

                // 1. 连续相同字母超过2个 (如 "aaa", "fff", "kkk")
                if (pinyinBuffer.length >= 3) {
                    var allSame = true
                    for (var i = 1; i < pinyinBuffer.length; i++) {
                        if (pinyinBuffer[i] !== pinyinBuffer[0]) {
                            allSame = false
                            break
                        }
                    }
                    if (allSame) {
                        shouldAutoSplit = true
                    }
                }

                // 2. 非常长的无效组合 (超过6个字符且无候选词)
                if (pinyinBuffer.length > 6) {
                    shouldAutoSplit = true
                }

                if (shouldAutoSplit) {
                    var validPart = findValidRomajiPrefix(pinyinBuffer)
                    if (validPart.length > 0 && validPart.length < pinyinBuffer.length) {
                        console.log("🔧 日文智能分割:", pinyinBuffer, "→", validPart)
                        pinyinBuffer = validPart
                        candidateWords = japaneseEngine.getJapaneseCandidates(pinyinBuffer)
                        showCandidates = true  // 分割后也始终显示
                    }
                } else {
                    console.log("⏳ 日文等待更多输入:", pinyinBuffer, "(暂无候选词)")
                }
            }

            // 如果罗马字过长且没有候选词，提示用户
            if (pinyinBuffer.length > 6 && candidateWords.length === 0) {
                console.log("⚠️ 日文罗马字过长且无效:", pinyinBuffer)
                // 不自动清空，让用户手动删除
            }
        } else {
            candidateWords = []
            showCandidates = false
            selectedCandidate = 0
        }
    }

    // 查找有效的罗马字前缀
    function findValidRomajiPrefix(romaji) {
        // 从最长开始尝试，找到第一个有效的前缀
        for (var i = romaji.length - 1; i > 0; i--) {
            var prefix = romaji.substring(0, i)
            var candidates = japaneseEngine.getJapaneseCandidates(prefix)
            if (candidates.length > 0) {
                return prefix
            }
        }
        return ""
    }

    // 选择候选词
    function selectCandidate(index) {
        if (index >= 0 && index < candidateWords.length) {
            var selectedText = candidateWords[index]
            chineseTextSelected(selectedText)
            clearPinyinBuffer()
        }
    }

    // 清空拼音缓冲区
    function clearPinyinBuffer() {
        pinyinBuffer = ""
        candidateWords = []
        showCandidates = false
        selectedCandidate = 0
    }

    // 获取当前语言的键盘布局
    function getKeyboardLayout() {
        switch(currentLanguage) {
            case "EN": return getEnglishLayout()
            case "CN": return getChineseLayout()
            case "JP": return getJapaneseLayout()
            default: return getEnglishLayout()
        }
    }

    // 根据语言和行号获取按键数据
    function getRowKeys(rowNumber) {
        var layout = getKeyboardLayout()
        switch(rowNumber) {
            case 1: return layout.row1
            case 2: return layout.row2
            case 3: return layout.row3
            case 4: return layout.row4
            case 5: return layout.row5
            default: return []
        }
    }

    // 创建动态按键模型
    function createDynamicKeyModel(rowNumber) {
        var keys = getRowKeys(rowNumber)
        var model = []

        for (var i = 0; i < keys.length; i++) {
            var keyText = keys[i]
            var keyCode = keyText
            var keyWidth = getKeyWidth(rowNumber, i)
            var isSpecial = isSpecialKey(keyText)

            // 处理特殊按键的显示文本
            if (keyText === "⌫") {
                keyCode = "BACKSPACE"
            } else if (keyText === "Tab") {
                keyCode = "TAB"
            } else if (keyText === "Caps") {
                keyCode = "CAPS"
            } else if (keyText === "Enter") {
                keyCode = "ENTER"
            } else if (keyText === "Shift") {
                keyCode = "SHIFT"
            } else if (keyText === "Ctrl") {
                keyCode = "CTRL"
            } else if (keyText === "Alt") {
                keyCode = "ALT"
            } else if (keyText === "⌨") {
                keyCode = "HIDE_KEYBOARD"
            } else if (keyText === "") {
                keyCode = "SPACE"
            } else if (keyText === getLanguageDisplayName(currentLanguage)) {
                keyCode = "LANG_SWITCH"
            }

            // 处理大小写和Shift状态
            if (keyText.length === 1 && keyText >= "a" && keyText <= "z") {
                if (shiftPressed || capsLock) {
                    keyText = keyText.toUpperCase()
                }
            } else if (shiftPressed && currentLanguage === "EN") {
                // 英文模式下的Shift符号
                switch (keyText) {
                    case "`": keyText = "~"; break
                    case "1": keyText = "!"; break
                    case "2": keyText = "@"; break
                    case "3": keyText = "#"; break
                    case "4": keyText = "$"; break
                    case "5": keyText = "%"; break
                    case "6": keyText = "^"; break
                    case "7": keyText = "&"; break
                    case "8": keyText = "*"; break
                    case "9": keyText = "("; break
                    case "0": keyText = ")"; break
                    case "-": keyText = "_"; break
                    case "=": keyText = "+"; break
                    case "[": keyText = "{"; break
                    case "]": keyText = "}"; break
                    case "\\": keyText = "|"; break
                    case ";": keyText = ":"; break
                    case "'": keyText = "\""; break
                    case ",": keyText = "<"; break
                    case ".": keyText = ">"; break
                    case "/": keyText = "?"; break
                }
            }

            model.push(createKey(keyText, keyCode, keyWidth, isSpecial))
        }

        return model
    }

    // 判断是否为特殊按键
    function isSpecialKey(keyText) {
        var specialKeys = ["⌫", "Tab", "Caps", "Enter", "Shift", "Ctrl", "Alt", "⌨", "", getLanguageDisplayName(currentLanguage)]
        return specialKeys.indexOf(keyText) !== -1
    }

    // 获取按键宽度
    function getKeyWidth(rowNumber, keyIndex) {
        switch(rowNumber) {
            case 1:
                if (keyIndex === 13) return row1BackspaceWidth  // 退格键
                return row1NormalWidth
            case 2:
                if (keyIndex === 0) return row2TabWidth  // Tab键
                return row2NormalWidth
            case 3:
                if (keyIndex === 0) return row3CapsWidth  // Caps键
                if (keyIndex === 12) return row3EnterWidth  // Enter键
                return row3NormalWidth
            case 4:
                if (keyIndex === 0) return row4LeftShiftWidth  // 左Shift键
                if (keyIndex === 11) return row4RightShiftWidth  // 右Shift键
                return row4NormalWidth
            case 5:
                if (keyIndex === 0 || keyIndex === 5) return row5CtrlWidth  // Ctrl键
                if (keyIndex === 1) return row5AltWidth  // Alt键
                if (keyIndex === 2) return row5SpaceWidth  // 空格键
                if (keyIndex === 3) return row5LangWidth  // 语言切换键
                if (keyIndex === 4) return row5HideWidth  // 隐藏键盘键
                return row5CtrlWidth
            default:
                return row1NormalWidth
        }
    }

    // 英文键盘布局
    function getEnglishLayout() {
        return {
            row1: ["`", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "-", "=", "⌫"],
            row2: ["Tab", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "[", "]", "\\"],
            row3: ["Caps", "a", "s", "d", "f", "g", "h", "j", "k", "l", ";", "'", "Enter"],
            row4: ["Shift", "z", "x", "c", "v", "b", "n", "m", ",", ".", "/", "Shift"],
            row5: ["Ctrl", "Alt", "", getLanguageDisplayName(currentLanguage), "⌨", "Ctrl"]
        }
    }

    // 中文键盘布局（拼音输入）
    function getChineseLayout() {
        return {
            row1: ["~", "！", "@", "#", "￥", "%", "……", "&", "*", "（", "）", "_", "+", "⌫"],
            row2: ["Tab", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "【", "】", "、"],
            row3: ["Caps", "a", "s", "d", "f", "g", "h", "j", "k", "l", "；", "'", "Enter"],
            row4: ["Shift", "z", "x", "c", "v", "b", "n", "m", "，", "。", "？", "Shift"],
            row5: ["Ctrl", "Alt", "", getLanguageDisplayName(currentLanguage), "⌨", "Ctrl"]
        }
    }

    // 日文键盘布局（罗马字输入）
    function getJapaneseLayout() {
        return {
            row1: ["~", "！", "\"", "＃", "＄", "％", "＆", "'", "（", "）", "０", "ー", "＝", "⌫"],
            row2: ["Tab", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "「", "」", "￥"],
            row3: ["Caps", "a", "s", "d", "f", "g", "h", "j", "k", "l", "；", "：", "Enter"],
            row4: ["Shift", "z", "x", "c", "v", "b", "n", "m", "、", "。", "・", "Shift"],
            row5: ["Ctrl", "Alt", "", getLanguageDisplayName(currentLanguage), "⌨", "Ctrl"]
        }
    }
    
    // 创建按键的函数
    function createKey(keyText, keyCode, keyWidth, isSpecial) {
        var keyColor = "#989AA1"  // 设计图精确颜色 - 普通按键
        if (isSpecial) {
            if ((keyCode === "CAPS" && capsLock) || (keyCode === "SHIFT" && shiftPressed)) {
                keyColor = "#3B82F6"  // 激活状态蓝色
            } else {
                keyColor = "#696C77"  // 设计图精确颜色 - 特殊按键
            }
        }

        return {
            text: keyText,
            code: keyCode,
            width: keyWidth,
            color: keyColor,
            isSpecial: isSpecial
        }
    }

    // 中文候选词显示区域
    Rectangle {
        id: candidateArea
        anchors.left: parent.left
        anchors.top: parent.top
        anchors.leftMargin: leftMargin
        anchors.topMargin: 10
        width: availableWidth
        height: showCandidates ? 50 : 0
        color: "#2D3748"
        radius: 6
        border.color: "#4A5568"
        border.width: 1
        visible: showCandidates && !hideInternalCandidates
        z: 2000  // 确保在键盘按键之上

        Row {
            anchors.left: parent.left
            anchors.verticalCenter: parent.verticalCenter
            anchors.leftMargin: 15
            spacing: 10

            // 拼音显示
            Text {
                text: pinyinBuffer
                color: "#10B981"
                font.pixelSize: 16
                font.weight: Font.Bold
                font.family: "Microsoft YaHei, Helvetica"
            }

            // 分隔符
            Text {
                text: "|"
                color: "#6B7280"
                font.pixelSize: 16
                font.family: "Microsoft YaHei, Helvetica"
                visible: pinyinBuffer.length > 0 && candidateWords.length > 0
            }

            // 候选词列表
            Repeater {
                model: Math.min(candidateWords.length, 9)  // 最多显示9个候选词

                Rectangle {
                    width: candidateText.width + 20
                    height: 35
                    color: selectedCandidate === index ? "#3B82F6" : "transparent"
                    radius: 4

                    Row {
                        anchors.centerIn: parent
                        spacing: 5

                        Text {
                            text: (index + 1).toString()
                            color: selectedCandidate === index ? "#FFFFFF" : "#9CA3AF"
                            font.pixelSize: 12
                            font.family: "Microsoft YaHei, Helvetica"
                        }

                        Text {
                            id: candidateText
                            text: candidateWords[index] || ""
                            color: selectedCandidate === index ? "#FFFFFF" : "#FFFFFF"
                            font.pixelSize: 16
                            font.family: "Microsoft YaHei, Helvetica"
                        }
                    }

                    MouseArea {
                        anchors.fill: parent
                        z: 2100  // 确保候选词可以点击
                        onClicked: selectCandidate(index)
                    }
                }
            }
        }
    }

    Column {
        anchors.left: parent.left
        anchors.verticalCenter: parent.verticalCenter
        anchors.leftMargin: leftMargin
        spacing: rowSpacing
        
        // 第一行：数字行 - 总宽度：13*113 + 173 + 13*6 = 1469 + 173 + 78 = 1720px
        Row {
            spacing: keySpacing
            
            Repeater {
                model: createDynamicKeyModel(1)
                
                Rectangle {
                    width: modelData.width
                    height: keyHeight
                    color: modelData.color
                    radius: 6
                    
                    Text {
                        anchors.centerIn: parent
                        text: modelData.text
                        color: "#FFFFFF"
                        font.pixelSize: modelData.isSpecial ? 28 : 26
                        font.weight: Font.Normal
                        font.family: "Helvetica"
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        z: 1000
                        enabled: true
                        onClicked: {
                            // console.log("按键点击:", modelData.code)
                            virtualKeyboard.handleKeyPress(modelData.code)
                        }
                    }
                }
            }
        }

        // 第二行：QWERTY行 - 总宽度：173 + 13*113 + 13*6 = 173 + 1469 + 78 = 1720px
        Row {
            spacing: keySpacing

            Repeater {
                model: createDynamicKeyModel(2)

                Rectangle {
                    width: modelData.width
                    height: keyHeight
                    color: modelData.color
                    radius: 6

                    Text {
                        anchors.centerIn: parent
                        text: modelData.text
                        color: "#FFFFFF"
                        font.pixelSize: modelData.isSpecial ? 28 : 26
                        font.weight: Font.Normal
                        font.family: "Helvetica"
                    }

                    MouseArea {
                        anchors.fill: parent
                        z: 1000
                        enabled: true
                        onClicked: {
                            // console.log("按键点击:", modelData.code)
                            virtualKeyboard.handleKeyPress(modelData.code)
                        }
                    }
                }
            }
        }

        // 第三行：ASDF行 - 总宽度：198 + 11*110 + 240 + 12*6 = 198 + 1210 + 240 + 72 = 1720px
        Row {
            spacing: keySpacing

            Repeater {
                model: createDynamicKeyModel(3)

                Rectangle {
                    width: modelData.width
                    height: keyHeight
                    color: modelData.color
                    radius: 6

                    Text {
                        anchors.centerIn: parent
                        text: modelData.text
                        color: "#FFFFFF"
                        font.pixelSize: modelData.isSpecial ? 28 : 26
                        font.weight: Font.Normal
                        font.family: "Helvetica"
                    }

                    MouseArea {
                        anchors.fill: parent
                        z: 1000
                        enabled: true
                        onClicked: {
                            // console.log("按键点击:", modelData.code)
                            virtualKeyboard.handleKeyPress(modelData.code)
                        }
                    }
                }
            }
        }

        // 第四行：ZXCV行 - 总宽度：253 + 10*110 + 301 + 11*6 = 253 + 1100 + 301 + 66 = 1720px
        Row {
            spacing: keySpacing

            Repeater {
                model: createDynamicKeyModel(4)

                Rectangle {
                    width: modelData.width
                    height: keyHeight
                    color: modelData.color
                    radius: 6

                    Text {
                        anchors.centerIn: parent
                        text: modelData.text
                        color: "#FFFFFF"
                        font.pixelSize: modelData.isSpecial ? 28 : 26
                        font.weight: Font.Normal
                        font.family: "Helvetica"
                    }

                    MouseArea {
                        anchors.fill: parent
                        z: 1000
                        enabled: true
                        onClicked: {
                            // console.log("按键点击:", modelData.code)
                            virtualKeyboard.handleKeyPress(modelData.code)
                        }
                    }
                }
            }
        }

        // 第五行：空格行 - 总宽度：2*153 + 2*118 + 2*153 + 712 + 118 + 7*6 = 306 + 236 + 306 + 712 + 118 + 42 = 1720px
        Row {
            spacing: keySpacing

            Repeater {
                model: createDynamicKeyModel(5)

                Rectangle {
                    width: modelData.width
                    height: keyHeight
                    color: modelData.color
                    radius: 6

                    Text {
                        anchors.centerIn: parent
                        text: modelData.text
                        color: "#FFFFFF"
                        font.pixelSize: 20
                        font.weight: Font.Normal
                        font.family: "Helvetica"
                    }

                    MouseArea {
                        anchors.fill: parent
                        z: 1000
                        enabled: true
                        onClicked: {
                            // console.log("按键点击:", modelData.code)
                            virtualKeyboard.handleKeyPress(modelData.code)
                        }
                    }
                }
            }
        }
    }
}
