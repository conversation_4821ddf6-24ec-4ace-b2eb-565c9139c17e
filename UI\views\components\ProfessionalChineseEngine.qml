import QtQuick 2.15

// 专业中文输入法引擎 - 基于真实输入法算法
QtObject {
    id: engine
    
    // 获取专业级拼音词典 - 基于真实使用频率
    function getProfessionalDictionary() {
        return {
            // 超高频单字 (每日使用)
            "de": ["的", "得", "德", "地"],
            "yi": ["一", "以", "已", "意", "易", "艺", "议", "亿", "医", "移"],
            "shi": ["是", "时", "事", "十", "实", "试", "世", "市", "室", "师"],
            "bu": ["不", "部", "步", "布", "补", "捕"],
            "zai": ["在", "再", "载", "栽", "灾"],
            "you": ["有", "又", "右", "友", "游", "油", "由", "邮"],
            "ren": ["人", "任", "认", "仁", "忍"],
            "le": ["了", "乐", "勒"],
            "wo": ["我", "握", "沃", "卧"],
            "ta": ["他", "她", "它", "塔", "踏"],
            "zhe": ["这", "着", "者", "哲", "折"],
            "ge": ["个", "各", "格", "歌", "革", "隔"],
            "shang": ["上", "商", "伤", "赏", "尚"],
            "lai": ["来", "莱", "赖", "徕"],
            "dou": ["都", "斗", "豆", "逗", "抖"],
            "neng": ["能", "嗯"],
            "dao": ["到", "道", "倒", "导", "刀"],
            "shuo": ["说", "朔", "硕", "烁"],
            "qu": ["去", "取", "趣", "区", "曲"],
            "guo": ["过", "国", "果", "锅", "裹"],
            "hui": ["会", "回", "汇", "慧", "挥"],
            "hao": ["好", "号", "豪", "毫", "浩"],
            "ni": ["你", "尼", "泥", "逆", "匿"],
            "men": ["们", "门", "闷", "焖"],
            "chu": ["出", "初", "除", "处", "触"],
            "xia": ["下", "夏", "吓", "侠", "峡"],
            "kan": ["看", "刊", "砍", "坎"],
            "jian": ["见", "间", "建", "健", "简", "减", "检"],
            "xiang": ["想", "向", "象", "香", "相", "详"],
            "yao": ["要", "药", "腰", "摇", "遥"],
            "jin": ["进", "今", "金", "近", "紧", "尽"],
            "xing": ["行", "性", "星", "兴", "形", "醒"],
            "fa": ["发", "法", "罚", "乏"],
            "sheng": ["生", "声", "省", "胜", "升"],
            "huo": ["或", "火", "活", "货", "获"],
            "zuo": ["做", "作", "坐", "左", "昨"],
            "wei": ["为", "位", "未", "味", "围", "微", "危"],
            "ma": ["吗", "妈", "马", "麻", "码"],
            "ba": ["吧", "把", "爸", "八", "巴"],
            "ne": ["呢", "哪"],
            "a": ["啊", "阿", "呀"],
            "o": ["哦", "噢", "哟"],
            "en": ["嗯", "恩"],
            "ai": ["爱", "哀", "挨", "癌"],
            "bei": ["被", "北", "背", "杯", "悲"],
            "gei": ["给"],
            "dui": ["对", "队", "堆", "兑"],
            "hen": ["很", "恨", "狠"],
            "da": ["大", "打", "答", "达", "搭"],
            "xiao": ["小", "笑", "校", "效", "消"],
            "duo": ["多", "朵", "躲", "夺"],
            "shao": ["少", "烧", "勺", "稍"],
            "mei": ["没", "每", "美", "妹", "梅"],
            "tian": ["天", "田", "添", "甜", "填"],
            "nian": ["年", "念", "粘"],
            "yue": ["月", "越", "约", "乐", "跃"],
            "ri": ["日", "入"],
            "hou": ["后", "候", "厚", "吼"],
            "qian": ["前", "钱", "千", "签", "浅"],
            "zhong": ["中", "重", "种", "钟", "终", "众"],
            "wai": ["外", "歪"],
            "li": ["里", "理", "力", "立", "利", "李", "离"],
            "mian": ["面", "免", "棉", "绵"],
            "shou": ["手", "首", "受", "收", "守"],
            "tou": ["头", "投", "偷", "透"],
            "jia": ["家", "加", "假", "价", "夹"],
            "xin": ["新", "心", "信", "辛", "薪"],
            "lao": ["老", "劳", "牢", "捞"],
            "zi": ["自", "字", "子", "紫", "资"],
            "ji": ["己", "机", "及", "级", "记", "几", "集", "急"],
            "ran": ["然", "燃", "染"],
            "xian": ["现", "先", "线", "县", "限", "鲜"],
            "tong": ["同", "通", "统", "痛", "铜"],
            "gong": ["工", "公", "功", "共", "供"],
            "si": ["四", "死", "思", "私", "司", "丝"],
            "wu": ["五", "无", "物", "务", "舞", "武"],
            "liu": ["六", "流", "留", "刘", "柳"],
            "qi": ["七", "起", "气", "其", "期", "奇"],
            "jiu": ["九", "就", "久", "酒", "救"],
            "ling": ["零", "领", "令", "灵", "铃"],
            
            // 单字母快速输入 (T9风格)
            "n": ["你", "那", "哪", "内", "年", "能"],
            "h": ["好", "和", "很", "会", "还", "后"],
            "w": ["我", "为", "王", "万", "问", "文"],
            "d": ["的", "大", "都", "到", "地", "得"],
            "s": ["是", "时", "说", "三", "上", "生"],
            "z": ["这", "在", "中", "自", "做", "走"],
            "g": ["个", "过", "国", "工", "公", "高"],
            "r": ["人", "如", "让", "日", "然", "认"],
            "t": ["他", "她", "它", "天", "太", "同"],
            "m": ["们", "没", "么", "美", "每", "面"],
            "y": ["有", "要", "也", "一", "用", "以"],
            "l": ["了", "来", "老", "里", "两", "力"],
            "c": ["出", "从", "长", "成", "车", "次"],
            "k": ["看", "可", "开", "快", "课", "空"],
            "j": ["就", "家", "见", "进", "今", "经"],
            "x": ["想", "小", "下", "现", "新", "学"],
            "f": ["发", "法", "放", "方", "分", "非"],
            "b": ["不", "把", "被", "本", "比", "别"],
            "p": ["跑", "朋", "片", "平", "破", "怕"],
            "v": ["很", "和", "还", "会", "或", "话"],
            "q": ["去", "起", "前", "请", "其", "全"],
            "e": ["而", "二", "儿", "耳", "额", "恶"],
            "a": ["啊", "爱", "安", "按", "案", "暗"],
            "o": ["哦", "噢", "欧", "偶"],
            "i": ["一", "以", "意", "已", "易", "艺"],
            "u": ["无", "五", "舞", "武", "务", "物"],
            
            // 常用双字词 (高频)
            "zhongguo": ["中国"],
            "beijing": ["北京"],
            "shanghai": ["上海"],
            "guangzhou": ["广州"],
            "shenzhen": ["深圳"],
            "tianjin": ["天津"],
            "chongqing": ["重庆"],
            "nanjing": ["南京"],
            "hangzhou": ["杭州"],
            "chengdu": ["成都"],
            "wuhan": ["武汉"],
            "xian": ["西安", "现", "先", "线"],
            "shenyang": ["沈阳"],
            "dalian": ["大连"],
            "qingdao": ["青岛"],
            "xiamen": ["厦门"],
            "kunming": ["昆明"],
            "taiyuan": ["太原"],
            "changsha": ["长沙"],
            "nanchang": ["南昌"],
            "fuzhou": ["福州"],
            "guiyang": ["贵阳"],
            "haikou": ["海口"],
            "lanzhou": ["兰州"],
            "yinchuan": ["银川"],
            "xining": ["西宁"],
            "lasa": ["拉萨"],
            
            // 常用词汇
            "pengyou": ["朋友"],
            "laoshi": ["老师"],
            "xuesheng": ["学生"],
            "gongzuo": ["工作"],
            "xuexi": ["学习"],
            "shenghuo": ["生活"],
            "jiating": ["家庭"],
            "aiqing": ["爱情"],
            "hunyin": ["婚姻"],
            "haizi": ["孩子"],
            "fumu": ["父母"],
            "xiongdi": ["兄弟"],
            "jiemei": ["姐妹"],
            "tongxue": ["同学"],
            "tongshi": ["同事"],
            "lingdao": ["领导"],
            "jinggli": ["经理"],
            "dongshi": ["董事"],
            "zongcai": ["总裁"],
            "zhuren": ["主任"],
            "buzhang": ["部长"],
            "shizhang": ["市长"],
            "shengzhang": ["省长"],
            "zongli": ["总理"],
            "zhuxi": ["主席"],
            
            // 时间词汇
            "jintian": ["今天"],
            "mingtian": ["明天"],
            "zuotian": ["昨天"],
            "xianzai": ["现在"],
            "yihou": ["以后"],
            "yiqian": ["以前"],
            "shangwu": ["上午"],
            "xiawu": ["下午"],
            "wanshang": ["晚上"],
            "zhongwu": ["中午"],
            "banye": ["半夜"],
            "qingzao": ["清早"],
            "huanghun": ["黄昏"],
            
            // 方向位置
            "dongfang": ["东方"],
            "xifang": ["西方"],
            "nanfang": ["南方"],
            "beifang": ["北方"],
            "zhongyang": ["中央"],
            "zuobian": ["左边"],
            "youbian": ["右边"],
            "shangmian": ["上面"],
            "xiamian": ["下面"],
            "limian": ["里面"],
            "waimian": ["外面"],
            "qianmian": ["前面"],
            "houmian": ["后面"],
            
            // 颜色
            "hongse": ["红色"],
            "huangse": ["黄色"],
            "lanse": ["蓝色"],
            "lvse": ["绿色"],
            "heise": ["黑色"],
            "baise": ["白色"],
            "fense": ["粉色"],
            "zise": ["紫色"],
            "chengse": ["橙色"],
            "huise": ["灰色"],
            
            // 数字
            "yibai": ["一百"],
            "yiqian": ["一千"],
            "yiwan": ["一万"],
            "shiwan": ["十万"],
            "yibaiwan": ["一百万"],
            "yiqianwan": ["一千万"],
            "yiyi": ["一亿"],
            
            // 常用动词
            "chi": ["吃", "迟"],
            "he": ["喝", "和", "河"],
            "shui": ["睡", "水"],
            "zou": ["走", "奏"],
            "pao": ["跑", "泡", "炮"],
            "tiao": ["跳", "条", "调"],
            "fei": ["飞", "费", "非"],
            "kai": ["开", "凯"],
            "guan": ["关", "观", "管", "官"],
            "mai": ["买", "卖", "埋"],
            "song": ["送", "松", "宋"],
            "na": ["拿", "那", "哪"],
            "fang": ["放", "房", "方", "防"],
            "zhao": ["找", "照", "招", "朝"],
            "ding": ["定", "订", "顶", "丁"],
            "yong": ["用", "永", "勇", "泳"],
            "xie": ["写", "谢", "些", "鞋"],
            "du": ["读", "度", "毒", "独"],
            "ting": ["听", "停", "庭"],
            "chang": ["唱", "长", "常", "场", "厂"],
            "wu": ["舞", "五", "无", "物", "务"],

            // 医学生物词汇
            "peitai": ["胚胎"],
            "xibao": ["细胞"],
            "jiyin": ["基因"],
            "dna": ["DNA"],
            "rna": ["RNA"],
            "danbaizhi": ["蛋白质"],
            "weishengsu": ["维生素"],
            "kangshengsu": ["抗生素"],
            "mianyili": ["免疫力"],
            "bingli": ["病理"],
            "zhenduan": ["诊断"],
            "zhiliao": ["治疗"],
            "shousu": ["手术"],
            "yiyuan": ["医院"],
            "yisheng": ["医生"],
            "hushi": ["护士"],
            "yaoshi": ["药师"],
            "bingren": ["病人"],
            "jiankang": ["健康"],
            "jibing": ["疾病"],
            "zhengzhuang": ["症状"],
            "fayan": ["发炎"],
            "ganran": ["感染"],
            "liuxing": ["流行"],
            "chuanran": ["传染"],
            "yufang": ["预防"],
            "kangfu": ["康复"],
            "baojian": ["保健"],
            "yingyang": ["营养"],
            "yundon": ["运动"],
            "xiuxi": ["休息"],
            "shuimian": ["睡眠"],

            // 科技词汇
            "jisuanji": ["计算机"],
            "diannao": ["电脑"],
            "shouji": ["手机"],
            "ruanjian": ["软件"],
            "yingjian": ["硬件"],
            "wangluo": ["网络"],
            "hulianwang": ["互联网"],
            "shujuku": ["数据库"],
            "suanfa": ["算法"],
            "biancheng": ["编程"],
            "kaifa": ["开发"],
            "ceshu": ["测试"],
            "tiaosu": ["调试"],
            "bushu": ["部署"],
            "fuwuqi": ["服务器"],
            "yunjisuanji": ["云计算"],
            "rengongzhineng": ["人工智能"],
            "jiqixuexi": ["机器学习"],
            "shenjingwangluo": ["神经网络"],
            "qukuailian": ["区块链"],
            "wulianwang": ["物联网"],
            "dasha": ["大数据"],
            "xuniixianshi": ["虚拟现实"],
            "zengqiangxianshi": ["增强现实"],

            // 科学仪器设备
            "xianweijing": ["显微镜"],
            "wangyuanjing": ["望远镜"],
            "fangdajing": ["放大镜"],
            "jisuanqi": ["计算器"],
            "ceshiyi": ["测试仪"],
            "jiansuoyi": ["检索仪"],
            "fenxiyi": ["分析仪"],
            "celiangyi": ["测量仪"],
            "jiansuoqi": ["检测器"],
            "chuanganqi": ["传感器"],
            "kongzhiqi": ["控制器"],
            "chuliqiqi": ["处理器"],
            "cunchu": ["存储"],
            "neicun": ["内存"],
            "yingpan": ["硬盘"],
            "guangqu": ["光驱"],
            "xiankaqia": ["显卡"],
            "shengka": ["声卡"],
            "wangka": ["网卡"],
            "zhuban": ["主板"],
            "dianyuan": ["电源"],
            "sanreqi": ["散热器"],
            "jianpan": ["键盘"],
            "shubiao": ["鼠标"],
            "xianshiqi": ["显示器"],
            "dayinji": ["打印机"],
            "saomayi": ["扫描仪"],
            "sheying": ["摄影"],
            "luxiang": ["录像"],
            "luyin": ["录音"],
            "bofang": ["播放"],
            "xiazai": ["下载"],
            "shangchuan": ["上传"],
            "liulan": ["浏览"],
            "sousuo": ["搜索"],
            "chazhao": ["查找"],
            "tihuang": ["替换"],
            "fuzhi": ["复制"],
            "zhantie": ["粘贴"],
            "jiaqie": ["剪切"],
            "chexiao": ["撤销"],
            "huifu": ["恢复"],
            "baocun": ["保存"],
            "dakai": ["打开"],
            "guanbi": ["关闭"],
            "tuichu": ["退出"],

            // 更多科学词汇
            "fenzi": ["分子"],
            "yuanzi": ["原子"],
            "dianzhi": ["电子"],
            "zhongzi": ["中子"],
            "zhizi": ["质子"],
            "lizi": ["离子"],
            "huaxue": ["化学"],
            "wuli": ["物理"],
            "shengwu": ["生物"],
            "shuxue": ["数学"],
            "jixue": ["几何"],
            "daishu": ["代数"],
            "weixiji": ["微积分"],
            "tongji": ["统计"],
            "gailu": ["概率"],
            "hanshu": ["函数"],
            "fangcheng": ["方程"],
            "bianliangshi": ["变量"],
            "changliangshi": ["常量"],
            "xiangliangshi": ["向量"],
            "juzhen": ["矩阵"],
            "tuopu": ["拓扑"],
            "jixue": ["几何"],
            "sanjiaofa": ["三角法"],
            "duishu": ["对数"],
            "zhishu": ["指数"],
            "jisuan": ["计算"],
            "yunsuanfa": ["运算法"],
            "luoji": ["逻辑"],
            "tuili": ["推理"],
            "zhengming": ["证明"],
            "dingli": ["定理"],
            "gongshi": ["公式"],
            "biaoda": ["表达"],
            "jieshi": ["解释"],
            "fenxi": ["分析"],
            "zonghe": ["综合"],
            "bijiao": ["比较"],
            "duibi": ["对比"],
            "leibie": ["类别"],
            "fenlei": ["分类"],
            "guilu": ["规律"],
            "moshi": ["模式"],
            "jiegou": ["结构"],
            "xitong": ["系统"],
            "liucheng": ["流程"],
            "buzhou": ["步骤"],
            "fangfa": ["方法"],
            "jiqiao": ["技巧"],
            "celue": ["策略"],
            "fangan": ["方案"],
            "jihua": ["计划"],
            "mubiao": ["目标"],
            "renwu": ["任务"],
            "xiangmu": ["项目"],
            "jindu": ["进度"],
            "xiaolu": ["效率"],
            "zhiliang": ["质量"],
            "biaozhun": ["标准"],
            "guifan": ["规范"],
            "liucheng": ["流程"],
            "caozuo": ["操作"],
            "shiyong": ["使用"],
            "yingyong": ["应用"],
            "shijian": ["实践"],
            "jingyan": ["经验"],
            "jineng": ["技能"],
            "nengli": ["能力"],
            "shuiping": ["水平"],
            "chengdu": ["程度"],
            "fanwei": ["范围"],
            "quyu": ["区域"],
            "kongjian": ["空间"],
            "shijian": ["时间"],
            "sudui": ["速度"],
            "jiasu": ["加速"],
            "jiansu": ["减速"],
            "tingzhi": ["停止"],
            "kaishi": ["开始"],
            "jieshu": ["结束"],
            "wancheng": ["完成"],
            "chenggong": ["成功"],
            "shibai": ["失败"],
            "cuowu": ["错误"],
            "zhengque": ["正确"],
            "jingque": ["精确"],
            "zhunque": ["准确"],
            "qingchu": ["清楚"],
            "mingque": ["明确"],
            "xiangxi": ["详细"],
            "jiandan": ["简单"],
            "fuzha": ["复杂"],
            "kunnan": ["困难"],
            "rongyi": ["容易"],
            "keneng": ["可能"],
            "bukeneng": ["不可能"],
            "yiding": ["一定"],
            "kending": ["肯定"],
            "fouding": ["否定"],
            "queren": ["确认"],
            "quxiao": ["取消"],
            "tongguo": ["通过"],
            "jujue": ["拒绝"],
            "jieshou": ["接受"],
            "tongyi": ["同意"],
            "fandui": ["反对"],
            "zhichi": ["支持"],
            "bangzhu": ["帮助"],
            "xiezhu": ["协助"],
            "hezuo": ["合作"],
            "jingzheng": ["竞争"],
            "bijiao": ["比较"],
            "xuanze": ["选择"],
            "jueding": ["决定"],
            "panduan": ["判断"],
            "fenxi": ["分析"],
            "yanjiu": ["研究"],
            "diaocha": ["调查"],
            "ceshi": ["测试"],
            "shiyan": ["实验"],
            "guancha": ["观察"],
            "jilu": ["记录"],
            "baogao": ["报告"],
            "zongjie": ["总结"],
            "jielun": ["结论"],
            "jianyi": ["建议"],
            "yijian": ["意见"],
            "kanfa": ["看法"],
            "guandian": ["观点"],
            "taidu": ["态度"],
            "lichang": ["立场"],
            "yuanze": ["原则"],
            "biaozhun": ["标准"],
            "yaoqiu": ["要求"],
            "tiaojian": ["条件"],
            "qingkuang": ["情况"],
            "zhuangtai": ["状态"],
            "bianhua": ["变化"],
            "fazhan": ["发展"],
            "jinbu": ["进步"],
            "tigao": ["提高"],
            "gaishan": ["改善"],
            "youhua": ["优化"],
            "wanshan": ["完善"],
            "gaijin": ["改进"],
            "chuangxin": ["创新"],
            "faming": ["发明"],
            "faxian": ["发现"],
            "tansuo": ["探索"],
            "yanjiu": ["研究"],
            "xuexi": ["学习"],
            "jiaoxue": ["教学"],
            "peixun": ["培训"],
            "zhidao": ["指导"],
            "bangzhu": ["帮助"],
            "fuwu": ["服务"],
            "guanli": ["管理"],
            "lingdao": ["领导"],
            "zuzhi": ["组织"],
            "anpai": ["安排"],
            "jihua": ["计划"],
            "zhunbei": ["准备"],
            "shishi": ["实施"],
            "zhixing": ["执行"],
            "wancheng": ["完成"],
            "jiancha": ["检查"],
            "jiandu": ["监督"],
            "kongzhi": ["控制"],
            "tiaozheng": ["调整"],
            "xiuzheng": ["修正"],
            "gengxin": ["更新"],
            "shengji": ["升级"],
            "weihu": ["维护"],
            "xiufu": ["修复"],
            "chuli": ["处理"],
            "jiejue": ["解决"],
            "chengdan": ["承担"],
            "fuze": ["负责"],
            "danxin": ["担心"],
            "guanxin": ["关心"],
            "zhuyi": ["注意"],
            "xiaoxin": ["小心"],
            "jinsheng": ["谨慎"],
            "renzhen": ["认真"],
            "nuli": ["努力"],
            "jiancheng": ["坚持"],
            "fendou": ["奋斗"],
            "pinbo": ["拼搏"],
            "chenggong": ["成功"],
            "shengli": ["胜利"],
            "chengjiu": ["成就"],
            "chengjiu": ["成绩"],
            "xiaoguo": ["效果"],
            "jieguo": ["结果"],
            "yingxiang": ["影响"],
            "zuoyong": ["作用"],
            "yiyi": ["意义"],
            "jiazhi": ["价值"],
            "zhongyao": ["重要"],
            "guanjian": ["关键"],
            "hexin": ["核心"],
            "zhongdian": ["重点"],
            "nandian": ["难点"],
            "tedian": ["特点"],
            "youshi": ["优势"],
            "lieshi": ["劣势"],
            "wenti": ["问题"],
            "maodun": ["矛盾"],
            "chongtu": ["冲突"],
            "fenqi": ["分歧"],
            "yijian": ["异议"],
            "zhengyi": ["争议"],
            "taolun": ["讨论"],
            "shangyi": ["商议"],
            "xieshang": ["协商"],
            "tanpan": ["谈判"],
            "hetan": ["和谈"],
            "heping": ["和平"],
            "zhanzheng": ["战争"],
            "douzheng": ["斗争"],
            "jingzheng": ["竞争"],
            "bisai": ["比赛"],
            "youxi": ["游戏"],
            "yule": ["娱乐"],
            "xiuxi": ["休息"],
            "dujia": ["度假"],
            "lvyou": ["旅游"],
            "lvxing": ["旅行"],
            "chuxing": ["出行"],
            "jiaotong": ["交通"],
            "daolu": ["道路"],
            "jiedao": ["街道"],
            "gonglu": ["公路"],
            "tielu": ["铁路"],
            "hangxian": ["航线"],
            "jichang": ["机场"],
            "huochezhan": ["火车站"],
            "qichezhan": ["汽车站"],
            "ditiezhan": ["地铁站"],
            "gongjiaozhan": ["公交站"],
            "matou": ["码头"],
            "gangkou": ["港口"],
            "haiguan": ["海关"],
            "bianjing": ["边境"],
            "guojia": ["国家"],
            "chengshi": ["城市"],
            "xiangcun": ["乡村"],
            "nongcun": ["农村"],
            "shanzhai": ["山寨"],
            "haibian": ["海边"],
            "hubian": ["湖边"],
            "hebian": ["河边"],
            "shanshang": ["山上"],
            "shandi": ["山地"],
            "pingyuan": ["平原"],
            "gaoyuan": ["高原"],
            "pendi": ["盆地"],
            "shangu": ["山谷"],
            "xiagu": ["峡谷"],
            "senlin": ["森林"],
            "caoyuan": ["草原"],
            "shamo": ["沙漠"],
            "haiyang": ["海洋"],
            "dahai": ["大海"],
            "xiaohe": ["小河"],
            "dahe": ["大河"],
            "jianghe": ["江河"],
            "hupo": ["湖泊"],
            "chizi": ["池子"],
            "shuiku": ["水库"],
            "quanshui": ["泉水"],
            "jingshui": ["井水"],
            "yushui": ["雨水"],
            "xueshui": ["雪水"],
            "bingshui": ["冰水"],
            "reshu": ["热水"],
            "lengshui": ["冷水"],
            "wenshui": ["温水"],
            "kaishui": ["开水"],
            "zhengliushui": ["蒸馏水"],
            "kuangquanshui": ["矿泉水"],
            "zishui": ["自水"],
            "shuizheng": ["水蒸"],
            "shuiqi": ["水汽"],
            "yunwu": ["云雾"],
            "wumai": ["雾霾"],
            "kongqi": ["空气"],
            "yangqi": ["氧气"],
            "danqi": ["氮气"],
            "eryanghuatan": ["二氧化碳"],
            "jiawan": ["甲烷"],
            "qingqi": ["氢气"],
            "heliu": ["氦气"],
            "neon": ["氖气"],
            "yaxon": ["氩气"],

            // 教育词汇
            "xuexiao": ["学校"],
            "daxue": ["大学"],
            "zhongxue": ["中学"],
            "xiaoxue": ["小学"],
            "youeryuan": ["幼儿园"],
            "jiaoshi": ["教室"],
            "tushuguan": ["图书馆"],
            "shiyanshi": ["实验室"],
            "kecheng": ["课程"],
            "kaoshi": ["考试"],
            "zuoye": ["作业"],
            "chengji": ["成绩"],
            "xueli": ["学历"],
            "xuewei": ["学位"],
            "boshi": ["博士"],
            "shuoshi": ["硕士"],
            "benke": ["本科"],
            "zhuanke": ["专科"],
            "yanjiusheng": ["研究生"],
            "liuxuesheng": ["留学生"],

            // 商业经济
            "gongsi": ["公司"],
            "qiye": ["企业"],
            "shangye": ["商业"],
            "jingji": ["经济"],
            "jinrong": ["金融"],
            "yinhang": ["银行"],
            "baoxian": ["保险"],
            "gupiao": ["股票"],
            "jijin": ["基金"],
            "touzi": ["投资"],
            "licai": ["理财"],
            "daikuan": ["贷款"],
            "xingyong": ["信用"],
            "zhanghu": ["账户"],
            "jiaoyi": ["交易"],
            "xiaoshou": ["销售"],
            "shichang": ["市场"],
            "pinpai": ["品牌"],
            "chanpin": ["产品"],
            "fuwu": ["服务"],
            "kehu": ["客户"],
            "hetong": ["合同"],
            "xiangmu": ["项目"],
            "yusuan": ["预算"],
            "chengben": ["成本"],
            "lirun": ["利润"],
            "yingye": ["营业"],
            "guanli": ["管理"],
            "yunying": ["运营"],

            // 更多专业词汇
            "kexue": ["科学"],
            "jishu": ["技术"],
            "gongcheng": ["工程"],
            "shuxue": ["数学"],
            "wuli": ["物理"],
            "huaxue": ["化学"],
            "shengwu": ["生物"],
            "dili": ["地理"],
            "lishi": ["历史"],
            "zhengzhi": ["政治"],
            "jingji": ["经济"],
            "falv": ["法律"],
            "yishu": ["艺术"],
            "wenxue": ["文学"],
            "yinyue": ["音乐"],
            "huihua": ["绘画"],
            "diaosu": ["雕塑"],
            "sheying": ["摄影"],
            "dianying": ["电影"],
            "dianshi": ["电视"],
            "guangbo": ["广播"],
            "xinwen": ["新闻"],
            "baozhi": ["报纸"],
            "zazhi": ["杂志"],
            "tushu": ["图书"],
            "xiaoshuo": ["小说"],
            "shige": ["诗歌"],
            "sanwen": ["散文"],

            // 生活用品
            "shouji": ["手机"],
            "diannao": ["电脑"],
            "dianshi": ["电视"],
            "bingxiang": ["冰箱"],
            "xiyiji": ["洗衣机"],
            "kongtiao": ["空调"],
            "dianfan": ["电饭"],
            "weibolv": ["微波炉"],
            "diancihu": ["电磁炉"],
            "reshuiqi": ["热水器"],
            "chuanghu": ["窗户"],
            "fangmen": ["房门"],
            "chuang": ["床"],
            "zhuozi": ["桌子"],
            "yizi": ["椅子"],
            "shafa": ["沙发"],
            "diandeng": ["电灯"],
            "dianshan": ["电扇"],
            "zhentou": ["枕头"],
            "beizi": ["被子"],
            "chuangdan": ["床单"],
            "maojin": ["毛巾"],
            "yashua": ["牙刷"],
            "yagao": ["牙膏"],
            "xiangzao": ["香皂"],
            "xizao": ["洗澡"],
            "linyu": ["淋浴"],
            "matong": ["马桶"],
            "xishoujian": ["洗手间"],
            "weishengjian": ["卫生间"],
            "chufang": ["厨房"],
            "keting": ["客厅"],
            "woshi": ["卧室"],
            "shufang": ["书房"],
            "yangtai": ["阳台"],
            "huayuan": ["花园"],
            "cheku": ["车库"]
        }
    }
    
    // 专业智能匹配算法
    function getSmartCandidates(pinyin) {
        var dict = getProfessionalDictionary()
        var candidates = []
        var lowerPinyin = pinyin.toLowerCase()
        
        // 1. 完全匹配 (最高优先级)
        if (dict[lowerPinyin]) {
            candidates = candidates.concat(dict[lowerPinyin])
        }
        
        // 2. 前缀匹配 (智能预测)
        for (var key in dict) {
            if (key.startsWith(lowerPinyin) && key !== lowerPinyin) {
                // 根据长度差异调整优先级
                var priority = Math.max(1, 4 - (key.length - lowerPinyin.length))
                for (var i = 0; i < Math.min(dict[key].length, priority); i++) {
                    candidates.push(dict[key][i])
                }
            }
        }
        
        // 3. 模糊匹配 (容错处理)
        if (lowerPinyin.length >= 2) {
            for (var key in dict) {
                if (key.includes(lowerPinyin) && !key.startsWith(lowerPinyin)) {
                    candidates = candidates.concat(dict[key].slice(0, 1))
                }
            }
        }
        
        // 4. 去重并按使用频率排序
        var uniqueCandidates = []
        var seen = {}
        for (var i = 0; i < candidates.length && uniqueCandidates.length < 9; i++) {
            if (!seen[candidates[i]]) {
                seen[candidates[i]] = true
                uniqueCandidates.push(candidates[i])
            }
        }
        
        return uniqueCandidates
    }
    
    // 智能分词算法 (类似搜狗输入法)
    function intelligentSegmentation(pinyin) {
        var dict = getProfessionalDictionary()
        var candidates = []
        
        // 首先尝试完整匹配
        if (dict[pinyin]) {
            return dict[pinyin]
        }
        
        // 动态规划分词
        var dp = []
        var path = []
        for (var i = 0; i <= pinyin.length; i++) {
            dp[i] = []
            path[i] = []
        }
        dp[0] = [""]
        
        for (var i = 0; i < pinyin.length; i++) {
            if (dp[i].length === 0) continue
            
            // 尝试不同长度的分割
            for (var len = 1; len <= Math.min(8, pinyin.length - i); len++) {
                var segment = pinyin.substring(i, i + len)
                if (dict[segment]) {
                    for (var j = 0; j < dp[i].length; j++) {
                        for (var k = 0; k < Math.min(dict[segment].length, 3); k++) {
                            var newCandidate = dp[i][j] + dict[segment][k]
                            if (dp[i + len].length < 9) {
                                dp[i + len].push(newCandidate)
                            }
                        }
                    }
                }
            }
        }
        
        return dp[pinyin.length] || []
    }
    
    // 主要的候选词获取函数
    function getCandidateWords(pinyin) {
        if (!pinyin || pinyin.length === 0) {
            return []
        }
        
        var candidates = []
        
        // 使用智能分词
        var segmentedCandidates = intelligentSegmentation(pinyin)
        candidates = candidates.concat(segmentedCandidates)
        
        // 如果分词结果不足，使用智能匹配
        if (candidates.length < 6) {
            var smartCandidates = getSmartCandidates(pinyin)
            candidates = candidates.concat(smartCandidates)
        }
        
        // 去重并限制数量
        var uniqueCandidates = []
        var seen = {}
        for (var i = 0; i < candidates.length && uniqueCandidates.length < 9; i++) {
            if (!seen[candidates[i]]) {
                seen[candidates[i]] = true
                uniqueCandidates.push(candidates[i])
            }
        }
        
        return uniqueCandidates
    }
}
