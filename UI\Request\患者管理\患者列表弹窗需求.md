# 患者列表弹窗技术需求文档

## 📋 实现状态概览
- ✅ **弹窗基础结构** - 完全实现
- ✅ **动画效果** - 从底部渐入弹出
- ✅ **表头设计** - 6列表头，支持排序
- ✅ **患者数据显示** - 8行患者数据
- ✅ **排序功能** - 5列支持排序
- ✅ **选择交互** - 单选模式
- ✅ **虚拟键盘集成** - 支持搜索输入
- ✅ **确认取消按钮** - 完整交互流程

## 🎯 弹窗功能概述

### 触发条件
- **触发位置**：舱室详情界面
- **触发操作**：点击"分配患者"按钮
- **显示条件**：当前舱室未分配患者时

### 主要功能
1. **患者列表显示**：显示所有未分配的患者信息
2. **排序功能**：支持按姓名、ID、出生日期排序
3. **患者选择**：单选模式，选中高亮显示
4. **搜索功能**：集成虚拟键盘，支持实时搜索
5. **确认分配**：选择患者后确认分配到目标舱室

## 🏗️ 弹窗结构设计

### 整体布局规格
- **弹窗尺寸**：width: 1890px, height: 620px
- **弹窗位置**：x: 1930px (右侧弹出)
- **弹窗颜色**：#313542 (与舱室背景一致)
- **圆角设计**：radius: 40px (仅顶部圆角)
- **层级关系**：显示在舱室详情界面之上

### 弹出动画效果
```javascript
// 位置动画 - 从底部渐入
y: isVisible ? 0 : 720  // 从屏幕底部弹出

// 动画参数
Behavior on y {
    NumberAnimation {
        duration: 300
        easing.type: Easing.OutCubic
    }
}

// 透明度动画
Behavior on opacity {
    NumberAnimation {
        duration: 300
        easing.type: Easing.OutCubic
    }
}
```

## 📊 表头设计规格

### 表头整体规格
- **位置**：弹窗顶部
- **尺寸**：width: 1860px, height: 78px
- **背景色**：transparent
- **布局方式**：Row布局，6列等宽

### 列头规格
| 列名 | 宽度 | 排序支持 | 数据字段 | 显示内容 |
|------|------|----------|----------|----------|
| First name | 310px | ✅ | firstName | 患者名 |
| Last name | 310px | ✅ | lastName | 患者姓 |
| ID1 | 310px | ✅ | id1 | 患者ID1 |
| ID2 | 310px | ✅ | id2 | 患者ID2 |
| Date of Birth | 310px | ✅ | dateOfBirth | 出生日期 |
| Chamber | 310px | ❌ | chamber | 分配状态 |

### 列头文字规格
- **字体**：Helvetica, 24px
- **颜色**：#81828B (灰色)
- **对齐**：左对齐，垂直居中
- **位置**：距离左边缘10px

### 排序按钮规格
- **尺寸**：width: 78px, height: 78px
- **位置**：紧挨着列名右侧，spacing: 5px
- **图标资源**：
  - 未激活状态：Down_Icon.png
  - 激活状态：Down_Icon_1.png
- **旋转逻辑**：
  - 正序：rotation: 180度
  - 倒序：rotation: 0度

## 📋 患者数据显示规格

### 数据列表规格
- **列表容器**：ListView组件
- **位置**：表头下方
- **尺寸**：width: 1860px, height: 462px
- **滚动支持**：clip: true，支持垂直滚动
- **数据绑定**：patientsData数组

### 单行数据规格
- **行高**：height: 78px
- **行宽**：width: 1860px
- **背景色**：#313542 (基础色)
- **选中高亮**：白色叠加层，opacity: 0.25

### 数据字段显示
- **字体**：Helvetica, 20px
- **颜色**：#FFFFFF (白色)
- **对齐**：左对齐，垂直居中
- **边距**：距离左边缘20px (10px容器边距 + 10px文字边距)

### 分割线规格
- **位置**：每行底部
- **尺寸**：width: 1860px, height: 1px
- **样式**：border: 2px solid #3D404D

### 滚动条样式
```javascript
ScrollBar.vertical: ScrollBar {
    active: true
    policy: ScrollBar.AsNeeded
    width: 12
    
    background: Rectangle {
        color: "#2A2A2A"
        radius: 6
    }
    
    contentItem: Rectangle {
        color: "#81828B"
        radius: 6
        opacity: parent.pressed ? 0.8 : 0.6
    }
}
```

## 🎮 交互功能规格

### 患者选择交互
- **选择模式**：单选模式
- **选中状态**：selectedIndex属性控制
- **视觉反馈**：选中行显示白色半透明叠加层
- **点击区域**：整行可点击

### 排序功能交互
- **排序状态管理**：sortStates对象
- **排序逻辑**：
  1. 首次点击：激活该列，倒序排列
  2. 再次点击：切换为正序排列
  3. 第三次点击：取消排序，恢复原始顺序
- **状态同步**：sortStateUpdated信号通知界面更新

### 排序状态数据结构
```javascript
property var sortStates: {
    "firstName": {ascending: false, active: false},
    "lastName": {ascending: false, active: false},
    "id1": {ascending: false, active: false},
    "id2": {ascending: false, active: false},
    "dateOfBirth": {ascending: false, active: false}
}
```

## 🎨 标题栏设计规格

### 标题栏整体规格
- **位置**：弹窗顶部
- **尺寸**：width: 1890px, height: 78px
- **背景色**：transparent
- **布局**：左中右三区域布局

### 标题图标规格
- **位置**：x: 779px, y: 18px (居中偏左)
- **尺寸**：width: 90.08px, height: 41.98px
- **图片源**：New_Patients.png
- **填充模式**：Image.PreserveAspectFit

### 标题文字规格
- **位置**：图标右侧21px
- **尺寸**：width: 221px, height: 29px
- **字体**：Helvetica, 24px, #FFFFFF
- **内容**："Unallocated Patients"
- **对齐**：水平居中，垂直居中

### Cancel按钮规格
- **位置**：左侧，距离边缘20px
- **尺寸**：width: 153px, height: 78px
- **布局**：Row布局，图标+文字

#### Cancel图标
- **尺寸**：width: 78px, height: 78px
- **图片源**：Cancel_Icon.png
- **旋转**：rotation: 90度

#### Cancel文字
- **尺寸**：width: 75px, height: 29px
- **字体**：Helvetica, 24px, #FFFFFF
- **内容**："Cancel"

### OK按钮规格
- **位置**：右侧，距离边缘20px
- **尺寸**：width: 150px, height: 78px
- **启用条件**：selectedIndex >= 0
- **禁用样式**：opacity: 0.4

#### OK文字
- **位置**：x: 17px
- **尺寸**：width: 35px, height: 29px
- **字体**：Helvetica, 24px, #FFFFFF
- **内容**："OK"
- **对齐**：右对齐，垂直居中

#### OK图标
- **位置**：x: 76px
- **尺寸**：width: 60px, height: 60px
- **图片源**：OK_Icon_2.png
- **填充模式**：Image.PreserveAspectFit

## 🔧 技术实现特性

### 数据管理
- **数据源**：patientsData数组 (8个患者数据)
- **数据备份**：originalPatientsData (用于排序重置)
- **数据同步**：与后端ChamberDataManager集成
- **状态管理**：selectedIndex, isVisible, chamberId

### 信号系统
```javascript
// 主要信号定义
signal patientSelected(var patientData)      // 患者选择
signal cancelled()                           // 取消操作
signal confirmed()                           // 确认操作
signal sortStateUpdated()                    // 排序状态更新
signal patientAssigned(var patientData, string targetChamberId)  // 患者分配
```

### 虚拟键盘集成
- **触发条件**：搜索输入框获得焦点
- **键盘类型**：FixedDesignKeyboard组件
- **输入支持**：中英文输入，实时搜索
- **位置控制**：键盘弹出时调整弹窗位置

## 📐 尺寸规格总览

### 弹窗主体
- **总尺寸**：1890×620像素
- **位置**：x: 1930px (右侧弹出)
- **圆角**：40px (仅顶部)
- **背景色**：#313542

### 内容区域
- **标题栏**：1890×78像素
- **表头**：1860×78像素
- **数据区**：1860×462像素
- **内容边距**：左右各15px

### 列宽分配
- **6列等宽**：每列310px
- **总宽度**：1860px (6×310px)
- **列间距**：无间距，紧密排列

## 🎯 用户体验设计

### 动画体验
- **弹出动画**：300ms缓动动画
- **选中反馈**：即时高亮显示
- **排序反馈**：图标旋转动画
- **按钮反馈**：透明度变化

### 交互反馈
- **选中状态**：白色半透明叠加
- **按钮状态**：启用/禁用视觉区分
- **排序状态**：图标状态和旋转角度
- **滚动体验**：自定义滚动条样式

### 数据展示
- **信息层次**：表头灰色，数据白色
- **可读性**：合适的字体大小和行高
- **对齐方式**：统一左对齐，视觉整齐
- **分割线**：清晰的行分割，便于阅读

## 📊 患者数据结构规格

### 标准患者数据格式
```javascript
{
    "firstName": "Jessica",        // 患者名
    "lastName": "Smith",           // 患者姓
    "id1": "11456768-1-3",        // 患者ID1
    "id2": "893456781-2-2",       // 患者ID2
    "dateOfBirth": "26th Nov 1978", // 出生日期
    "chamber": "?"                 // 分配状态 ("?" = 未分配)
}
```

### 数据验证规则
- **firstName/lastName**：非空字符串，支持中英文
- **id1/id2**：格式 "XXXXXXXX-X-X"，数字-数字-数字
- **dateOfBirth**：格式 "DDth MMM YYYY" (如 "26th Nov 1978")
- **chamber**：字符串，"?" 表示未分配，"A01" 等表示已分配

### 示例数据集
当前系统包含8个测试患者：
1. Jessica Smith (两个不同ID的患者)
2. Emily Johnson
3. Sarah Williams
4. Amanda Brown
5. Lisa Davis
6. Michael Wilson
7. Jennifer Taylor

## 🔍 搜索功能规格

### 搜索触发机制
- **触发方式**：双击弹窗空白区域
- **键盘弹出**：FixedDesignKeyboard组件
- **输入模式**：支持中英文输入
- **实时搜索**：输入时即时过滤结果

### 搜索范围
- **搜索字段**：firstName, lastName, id1, id2, dateOfBirth
- **搜索模式**：模糊匹配，不区分大小写
- **搜索逻辑**：包含匹配，支持部分字符串

### 搜索结果显示
- **过滤显示**：只显示匹配的患者
- **高亮显示**：匹配的文字高亮标记
- **空结果处理**：显示"无匹配结果"提示

## 🎛️ 排序算法规格

### 排序状态机
```
未激活 → 点击 → 倒序激活 → 点击 → 正序激活 → 点击 → 未激活
```

### 排序实现逻辑
```javascript
// 排序函数示例
function sortPatients(column, ascending) {
    patientsData.sort(function(a, b) {
        var valueA = a[column].toString().toLowerCase()
        var valueB = b[column].toString().toLowerCase()

        if (ascending) {
            return valueA.localeCompare(valueB)
        } else {
            return valueB.localeCompare(valueA)
        }
    })
}
```

### 特殊排序处理
- **日期排序**：按实际日期值排序，非字符串排序
- **ID排序**：按数字部分排序，非字符串排序
- **姓名排序**：支持中英文混合排序
- **多列排序**：同时只能有一列处于激活状态

## 🔄 状态管理流程

### 弹窗生命周期
```mermaid
stateDiagram-v2
    [*] --> 隐藏状态
    隐藏状态 --> 显示动画: show()调用
    显示动画 --> 显示状态: 动画完成
    显示状态 --> 选择状态: 点击患者行
    选择状态 --> 确认状态: 点击OK按钮
    确认状态 --> 隐藏动画: 分配完成
    显示状态 --> 隐藏动画: 点击Cancel
    隐藏动画 --> 隐藏状态: 动画完成
    隐藏状态 --> [*]
```

### 关键状态属性
- **isVisible**: 控制弹窗显示/隐藏
- **selectedIndex**: 当前选中的患者索引 (-1表示未选中)
- **chamberId**: 目标舱室ID
- **sortStates**: 各列的排序状态对象

### 状态同步机制
- **界面更新**：通过信号机制通知状态变化
- **数据同步**：与ChamberDataManager实时同步
- **动画控制**：通过Behavior实现平滑过渡

## 🎨 视觉设计细节

### 颜色规范
- **主背景色**：#313542 (深灰色)
- **文字颜色**：#FFFFFF (白色)
- **标题颜色**：#81828B (浅灰色)
- **选中高亮**：#FFFFFF, opacity: 0.25
- **分割线色**：#3D404D (中灰色)

### 字体规范
- **主字体**：Helvetica
- **标题字号**：24px
- **数据字号**：20px
- **按钮字号**：24px

### 间距规范
- **外边距**：20px (弹窗边缘到内容)
- **内边距**：10px (列内容到边缘)
- **行间距**：78px (固定行高)
- **列间距**：0px (列之间无间距)

## 🔧 集成接口规格

### 与舱室详情界面集成
```javascript
// 显示患者列表
function showPatientsList(targetChamberId) {
    unallocatedPatientsPopup.chamberId = targetChamberId
    unallocatedPatientsPopup.show()
}

// 处理患者分配结果
onPatientAssigned: function(patientData, chamberId) {
    // 更新舱室数据
    ChamberDataManager.assignPatientToChamber(patientData, chamberId)
    // 隐藏弹窗
    hide()
}
```

### 与虚拟键盘集成
```javascript
// 键盘弹出处理
onKeyboardShow: {
    // 调整弹窗位置，避免被键盘遮挡
    y = keyboardVisible ? -200 : 0
}

// 搜索输入处理
onSearchTextChanged: {
    filterPatients(searchText)
}
```

### 与后端数据集成
```javascript
// 数据获取
Component.onCompleted: {
    patientsData = ChamberDataManager.getUnallocatedPatients()
}

// 数据更新监听
Connections {
    target: ChamberDataManager
    function onPatientsDataChanged() {
        patientsData = ChamberDataManager.getUnallocatedPatients()
    }
}
```

## 📱 响应式设计

### 屏幕适配
- **固定尺寸**：1890×620px，不进行响应式缩放
- **位置适配**：始终从右侧弹出，x: 1930px
- **内容适配**：列宽固定，通过滚动处理内容溢出

### 性能优化
- **虚拟化列表**：大数据量时使用ListView的虚拟化
- **延迟加载**：图标和资源按需加载
- **动画优化**：使用GPU加速的动画效果
- **内存管理**：及时释放不需要的数据引用

## 🧪 测试用例规格

### 基础功能测试
1. **弹窗显示**：点击分配患者按钮，弹窗正确弹出
2. **患者选择**：点击患者行，正确选中并高亮
3. **确认分配**：选中患者后点击OK，正确分配
4. **取消操作**：点击Cancel，弹窗正确关闭

### 排序功能测试
1. **首次排序**：点击列头，数据按倒序排列
2. **切换排序**：再次点击，切换为正序排列
3. **取消排序**：第三次点击，恢复原始顺序
4. **多列排序**：切换列时，其他列排序状态重置

### 搜索功能测试
1. **搜索触发**：双击弹窗，键盘正确弹出
2. **实时搜索**：输入文字，结果实时过滤
3. **搜索清空**：清空输入，显示所有患者
4. **无结果处理**：搜索无匹配时的提示显示

### 边界情况测试
1. **空数据**：无患者数据时的界面显示
2. **单条数据**：只有一个患者时的交互
3. **大量数据**：100+患者时的性能表现
4. **网络异常**：数据加载失败时的错误处理
