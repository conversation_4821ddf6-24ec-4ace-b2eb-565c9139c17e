#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Geri2 主程序
完整的数据同步功能演示
"""

import sys
import os
from PySide6.QtGui import QGuiApplication
from PySide6.QtQml import QQmlApplicationEngine
from PySide6.QtCore import QUrl

# 添加backend目录到Python路径
# 获取项目根目录（UI目录的父目录）
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
backend_dir = os.path.join(project_root, "backend")
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

# 添加UI目录到Python路径
ui_dir = os.path.dirname(os.path.abspath(__file__))
if ui_dir not in sys.path:
    sys.path.insert(0, ui_dir)

# 导入后端数据管理器
from chamber_data_manager import get_chamber_data_manager, register_qml_types
from patient_manager import get_patient_manager, register_patient_manager_qml

# 导入患者模型 - 暂时注释掉
# from views.models.UnallocatedPatientsModel import register_types as register_patient_model_types


def main():
    """主函数"""
    print("=== Geri2 应用程序启动 ===")

    # 设置环境变量以使用Basic样式并抑制样式警告
    os.environ["QT_QUICK_CONTROLS_STYLE"] = "Basic"
    os.environ["QT_LOGGING_RULES"] = "qt.quick.controls.style.warning=false;qt.quick.controls.style=false"

    # 创建应用程序
    app = QGuiApplication(sys.argv)
    
    # 注册QML类型和后端数据管理器
    try:
        register_qml_types()
        register_patient_manager_qml()
        # register_patient_model_types()  # 注册患者模型类型 - 暂时注释掉
        chamber_data_manager = get_chamber_data_manager()
        patient_manager = get_patient_manager()
        # 启动自动数据更新
        chamber_data_manager.setTimerEnabled(True)
        print("✅ Geri2 后端系统初始化完成")
        
    except Exception as e:
        print(f"❌ 后端数据管理器初始化失败: {e}")
        return -1
    
    # 创建QML引擎
    engine = QQmlApplicationEngine()
    
    # 加载主QML文件 - 使用您原有的界面结构
    current_dir = os.path.dirname(os.path.abspath(__file__))
    main_qml_path = os.path.join(current_dir, "main.qml")

    if not os.path.exists(main_qml_path):
        print(f"❌ 主QML文件不存在: {main_qml_path}")
        return -1
    
    try:
        # 加载QML文件
        engine.load(QUrl.fromLocalFile(os.path.abspath(main_qml_path)))
        
        # 检查是否成功加载
        if not engine.rootObjects():
            print("❌ QML文件加载失败")
            return -1
        
        print("✅ Geri2 应用程序启动成功")
        
        # 连接quit信号
        engine.quit.connect(app.quit)
        
        # 运行应用程序
        result = app.exec()
        
        return result
        
    except Exception as e:
        print(f"❌ 应用程序运行失败: {e}")
        return -1


if __name__ == "__main__":
    sys.exit(main())
