# 患者页面文档

本文档基于QML代码，提供Geri应用程序中患者页面的概述。

## 1. 主视图 (`PatientsView.qml`)

患者页面的主视图作为所有患者相关功能的入口点。它围绕三个主要类别构建，并提供添加新患者的按钮。

### 1.1. 结构

- **添加患者按钮**: 一个突出的按钮，允许用户导航到"新建患者"界面。
- **患者分类**: 视图的主要区域分为三个类别：
  - **活跃患者**: 显示当前正在接受治疗或在系统中处于活跃状态的患者。
  - **未分配患者**: 列出已注册但尚未分配到培养箱的患者。
  - **历史患者**: 显示治疗已完成或不再活跃的患者记录。

### 1.2. 功能

- **导航**: 每个类别都是可点击的项目，将（最终）导向该类别内患者的详细列表。
- **患者创建**: "添加患者"按钮触发`addPatientClicked`信号，由父视图(`Home.qml`)处理以显示`AddPatientView`。

## 2. 新建患者视图 (`AddPatientView.qml`)

该视图提供一个用于创建新患者记录的表单。它包括个人信息、医疗详情和周期类型的字段。

### 2.1. 表单字段

以下字段可用于创建新患者：

- **名字 (First name)**: 患者的名字。
- **姓氏 (Last name)**: 患者的姓氏。
- **ID1**: 患者的主要身份识别号码。
- **ID2**: 备用身份识别号码。
- **出生日期 (Date of birth)**: 患者的出生日期，分为日、月、年输入。
- **卵子年龄 (年) (Egg age (Years))**: 患者卵子的年龄（以年为单位）。
- **周期类型 (Cycle type)**: 用于选择治疗周期类型的下拉菜单。选项包括：
  - 卵胞浆内单精子显微注射 (ICSI)
  - 温热/解冻卵母细胞
  - 体外人工授精(IVF)
  - 温热/解冻第2天的胚胎
  - 温热/解冻第3天的胚胎
  - 温热/解冻第4天的胚胎
  - 温热/解冻第5天的胚胎
  - 温热/解冻第6天的胚胎
  - ICSI补救

### 2.2. 功能

- **数据输入**: 用户可以使用虚拟键盘输入患者数据。
- **确认**: "OK"按钮收集表单数据到一个`newPatientData`对象中，并发出`confirmed`信号。
- **取消**: "Cancel"按钮发出`cancelled`信号，关闭视图而不保存。

## 3. 分类项 (`CategoryItem.qml`)

这是一个可重用组件，用于定义主患者页面上分类链接的外观和行为。

### 3.1. 结构

- **图标**: 代表分类的图标。
- **文本**: 分类的名称。
- **导航箭头**: 一个向右的箭头，表示该项是可点击的。

### 3.2. 功能

- **可点击**: 整个项目是一个可点击区域，在按下时会发出`clicked`信号。

