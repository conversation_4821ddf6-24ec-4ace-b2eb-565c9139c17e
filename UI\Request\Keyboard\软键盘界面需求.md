# Geri2 软键盘界面需求规格

## 1. 概述

Geri2 软键盘是一个专为舱室详情界面设计的虚拟键盘组件，支持中文拼音输入、英文输入和日文罗马字输入，具有智能候选词显示和流式布局功能。

## 2. 布局规格

### 2.1 键盘主体
- **位置**: 右侧弹出，与患者列表弹出方式一致
- **尺寸**:
  - 宽度: `parent.width * 0.5` (屏幕宽度的50%)
  - 高度: `500px`
  - x坐标: `parent.width * 0.5` (右侧位置)
  - y坐标: `keyboardVisible ? 100 : parent.height` (弹出时y=100，隐藏时在屏幕底部)

### 2.2 候选词区域
- **位置**: 键盘上方，与键盘按键左对齐
- **尺寸**:
  - x坐标: `keyboard.x + 85` (与键盘按键左边缘对齐)
  - y坐标: `keyboardVisible ? -10 : parent.height` (屏幕顶部-10px处)
  - 宽度: `keyboard.width - 170` (与键盘按键区域宽度一致)
  - 高度: `90px` (支持两排候选词显示)

### 2.3 分割线
- **位置**: 候选词区域下方3px处 (`y: 83`)
- **样式**: 1px高度，颜色 `#4A5568`
- **宽度**: 与候选词区域宽度一致

## 3. 功能特性

### 3.1 多语言支持
- **中文输入**: 拼音输入法，支持智能候选词
- **英文输入**: 直接字母输入
- **日文输入**: 罗马字转假名输入
- **默认语言**: 中文 (CN)

### 3.2 候选词功能
- **显示数量**: 最多8个候选词
- **布局方式**: 流式布局 (Flow)，自动换行
- **编号显示**: 候选词带编号 1-8
- **点击选择**: 支持鼠标点击选择候选词
- **键盘选择**: 支持数字键1-8快速选择

### 3.3 拼音输入限制
- **最大长度**: 20个字符
- **超长处理**: 超过20字符时自动截断并提示
- **缓冲区管理**: 双层缓冲区同步 (内外层)

## 4. 视觉设计

### 4.1 键盘样式
- **背景色**: 深色主题，与应用整体风格一致
- **按键样式**: 圆角矩形，支持悬停和按下状态
- **字体**: Microsoft YaHei
- **边距**: 左右各85px边距

### 4.2 候选词样式
- **背景色**: 半透明深色
- **文字颜色**: 白色 `#FFFFFF`
- **编号颜色**: 灰色 `#9CA3AF`
- **悬停效果**: 背景色变为 `#4A5568`
- **按下效果**: 背景色变为 `#3B82F6`
- **首选词**: 粗体显示，背景色 `#4A5568`

### 4.3 分割线样式
- **颜色**: `#4A5568`
- **高度**: 1px
- **透明度**: 与候选词区域可见性同步

## 5. 交互行为

### 5.1 键盘弹出/隐藏
- **触发方式**: 双击舱室详情界面任意区域
- **动画效果**: 300ms缓动动画 (OutCubic)
- **弹出位置**: 从屏幕底部弹出到y=100位置
- **z-index**: 键盘可见时为2000，隐藏时为-1

### 5.2 候选词交互
- **显示条件**: 有拼音输入且生成候选词时显示
- **隐藏条件**: 无拼音输入或候选词为空时隐藏
- **点击行为**: 点击候选词插入文本并清空拼音缓冲区
- **自动换行**: 第一排显示不下时自动换到第二排

### 5.3 按键响应
- **字母键**: 中文模式下添加到拼音缓冲区，英文模式下直接输入
- **数字键**: 选择对应编号的候选词
- **空格键**: 选择第一个候选词
- **退格键**: 删除拼音缓冲区最后一个字符
- **回车键**: 确认当前输入

## 6. 技术实现

### 6.1 核心组件
- **FixedDesignKeyboard.qml**: 键盘主文件
- **VirtualKeyboardPerfect.qml**: 键盘核心组件
- **ProfessionalChineseEngine.qml**: 中文输入引擎
- **JapaneseRomajiEngine.qml**: 日文输入引擎
- **KeyButton.qml**: 按键组件

### 6.2 数据流
1. 用户按键 → VirtualKeyboardPerfect处理
2. 中文字母 → ProfessionalChineseEngine生成候选词
3. 候选词同步到外层FixedDesignKeyboard
4. 候选词数量限制为8个
5. 候选词显示在Flow布局中

### 6.3 状态管理
- **keyboardVisible**: 键盘可见性状态
- **pinyinBuffer**: 拼音缓冲区内容
- **candidateWords**: 候选词数组
- **currentLanguage**: 当前输入语言

## 7. 性能要求

### 7.1 响应时间
- **按键响应**: < 50ms
- **候选词生成**: < 100ms
- **动画流畅度**: 60fps

### 7.2 内存使用
- **候选词缓存**: 最多8个候选词
- **拼音缓冲区**: 最大20字符
- **组件复用**: 按键组件复用，减少内存占用

## 8. 兼容性

### 8.1 屏幕分辨率
- **目标分辨率**: 3840x720
- **适配方式**: 相对布局，支持不同屏幕尺寸

### 8.2 输入法引擎
- **中文引擎**: 自研拼音输入法
- **日文引擎**: 罗马字转假名
- **扩展性**: 支持添加其他语言输入法

## 9. 测试要求

### 9.1 功能测试
- [ ] 键盘弹出/隐藏正常
- [ ] 中文拼音输入正确
- [ ] 候选词显示和选择正常
- [ ] 英文输入正确
- [ ] 日文输入正确
- [ ] 候选词左对齐显示
- [ ] 分割线正确显示
- [ ] 流式布局自动换行

### 9.2 性能测试
- [ ] 长时间输入无内存泄漏
- [ ] 快速连续按键响应正常
- [ ] 动画流畅无卡顿

### 9.3 边界测试
- [ ] 拼音长度达到20字符限制
- [ ] 候选词数量超过8个时正确限制
- [ ] 特殊字符输入处理

---

**最后更新**: 2025年8月13日
**版本**: v1.0
**状态**: 已实现并测试通过