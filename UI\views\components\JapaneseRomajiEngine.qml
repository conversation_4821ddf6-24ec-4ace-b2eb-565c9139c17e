import QtQuick 2.15

// 日文罗马字输入法引擎
QtObject {
    id: romajiEngine
    
    // 获取日文罗马字词典
    function getRomajiDictionary() {
        return {
            // 基本平假名 (あ行)
            "a": ["あ", "ア"],
            "i": ["い", "イ"],
            "u": ["う", "ウ"],
            "e": ["え", "エ"],
            "o": ["お", "オ"],
            
            // か行
            "ka": ["か", "カ"],
            "ki": ["き", "キ"],
            "ku": ["く", "ク"],
            "ke": ["け", "ケ"],
            "ko": ["こ", "コ"],
            
            // が行
            "ga": ["が", "ガ"],
            "gi": ["ぎ", "ギ"],
            "gu": ["ぐ", "グ"],
            "ge": ["げ", "ゲ"],
            "go": ["ご", "ゴ"],
            
            // さ行
            "sa": ["さ", "サ"],
            "si": ["し", "シ"],
            "su": ["す", "ス"],
            "se": ["せ", "セ"],
            "so": ["そ", "ソ"],
            "shi": ["し", "シ"],
            
            // ざ行
            "za": ["ざ", "ザ"],
            "zi": ["じ", "ジ"],
            "zu": ["ず", "ズ"],
            "ze": ["ぜ", "ゼ"],
            "zo": ["ぞ", "ゾ"],
            "ji": ["じ", "ジ"],
            
            // た行
            "ta": ["た", "タ"],
            "ti": ["ち", "チ"],
            "tu": ["つ", "ツ"],
            "te": ["て", "テ"],
            "to": ["と", "ト"],
            "chi": ["ち", "チ"],
            "tsu": ["つ", "ツ"],
            
            // だ行
            "da": ["だ", "ダ"],
            "di": ["ぢ", "ヂ"],
            "du": ["づ", "ヅ"],
            "de": ["で", "デ"],
            "do": ["ど", "ド"],
            
            // な行
            "na": ["な", "ナ"],
            "ni": ["に", "ニ"],
            "nu": ["ぬ", "ヌ"],
            "ne": ["ね", "ネ"],
            "no": ["の", "ノ"],
            
            // は行
            "ha": ["は", "ハ"],
            "hi": ["ひ", "ヒ"],
            "hu": ["ふ", "フ"],
            "he": ["へ", "ヘ"],
            "ho": ["ほ", "ホ"],
            "fu": ["ふ", "フ"],
            
            // ば行
            "ba": ["ば", "バ"],
            "bi": ["び", "ビ"],
            "bu": ["ぶ", "ブ"],
            "be": ["べ", "ベ"],
            "bo": ["ぼ", "ボ"],
            
            // ぱ行
            "pa": ["ぱ", "パ"],
            "pi": ["ぴ", "ピ"],
            "pu": ["ぷ", "プ"],
            "pe": ["ぺ", "ペ"],
            "po": ["ぽ", "ポ"],
            
            // ま行
            "ma": ["ま", "マ"],
            "mi": ["み", "ミ"],
            "mu": ["む", "ム"],
            "me": ["め", "メ"],
            "mo": ["も", "モ"],
            
            // や行
            "ya": ["や", "ヤ"],
            "yu": ["ゆ", "ユ"],
            "yo": ["よ", "ヨ"],
            
            // ら行
            "ra": ["ら", "ラ"],
            "ri": ["り", "リ"],
            "ru": ["る", "ル"],
            "re": ["れ", "レ"],
            "ro": ["ろ", "ロ"],
            
            // わ行
            "wa": ["わ", "ワ"],
            "wi": ["ゐ", "ヰ"],
            "we": ["ゑ", "ヱ"],
            "wo": ["を", "ヲ"],
            "n": ["ん", "ン"],
            
            // 拗音 (きゃ行)
            "kya": ["きゃ", "キャ"],
            "kyu": ["きゅ", "キュ"],
            "kyo": ["きょ", "キョ"],
            
            "gya": ["ぎゃ", "ギャ"],
            "gyu": ["ぎゅ", "ギュ"],
            "gyo": ["ぎょ", "ギョ"],
            
            "sha": ["しゃ", "シャ"],
            "shu": ["しゅ", "シュ"],
            "sho": ["しょ", "ショ"],
            
            "ja": ["じゃ", "ジャ"],
            "ju": ["じゅ", "ジュ"],
            "jo": ["じょ", "ジョ"],
            
            "cha": ["ちゃ", "チャ"],
            "chu": ["ちゅ", "チュ"],
            "cho": ["ちょ", "チョ"],
            
            "nya": ["にゃ", "ニャ"],
            "nyu": ["にゅ", "ニュ"],
            "nyo": ["にょ", "ニョ"],
            
            "hya": ["ひゃ", "ヒャ"],
            "hyu": ["ひゅ", "ヒュ"],
            "hyo": ["ひょ", "ヒョ"],
            
            "bya": ["びゃ", "ビャ"],
            "byu": ["びゅ", "ビュ"],
            "byo": ["びょ", "ビョ"],
            
            "pya": ["ぴゃ", "ピャ"],
            "pyu": ["ぴゅ", "ピュ"],
            "pyo": ["ぴょ", "ピョ"],
            
            "mya": ["みゃ", "ミャ"],
            "myu": ["みゅ", "ミュ"],
            "myo": ["みょ", "ミョ"],
            
            "rya": ["りゃ", "リャ"],
            "ryu": ["りゅ", "リュ"],
            "ryo": ["りょ", "リョ"],
            
            // 常用日文词汇
            "arigatou": ["ありがとう", "アリガトウ"],
            "konnichiwa": ["こんにちは", "コンニチワ"],
            "sayonara": ["さようなら", "サヨナラ"],
            "ohayou": ["おはよう", "オハヨウ"],
            "konbanwa": ["こんばんは", "コンバンワ"],
            "sumimasen": ["すみません", "スミマセン"],
            "gomenasai": ["ごめんなさい", "ゴメンナサイ"],
            "hajimemashite": ["はじめまして", "ハジメマシテ"],
            "yoroshiku": ["よろしく", "ヨロシク"],
            "genki": ["げんき", "ゲンキ"],
            "tanoshii": ["たのしい", "タノシイ"],
            "ureshii": ["うれしい", "ウレシイ"],
            "kanashii": ["かなしい", "カナシイ"],
            "kowai": ["こわい", "コワイ"],
            "muzukashii": ["むずかしい", "ムズカシイ"],
            "yasashii": ["やさしい", "ヤサシイ"],
            "oishii": ["おいしい", "オイシイ"],
            "atarashii": ["あたらしい", "アタラシイ"],
            "furui": ["ふるい", "フルイ"],
            "ookii": ["おおきい", "オオキイ"],
            "chiisai": ["ちいさい", "チイサイ"],
            "takai": ["たかい", "タカイ"],
            "yasui": ["やすい", "ヤスイ"],
            "hayai": ["はやい", "ハヤイ"],
            "osoi": ["おそい", "オソイ"],
            "atsui": ["あつい", "アツイ"],
            "samui": ["さむい", "サムイ"],
            "suzushii": ["すずしい", "スズシイ"],
            "atatakaii": ["あたたかい", "アタタカイ"],
            
            // 日常用品
            "hon": ["ほん", "ホン", "本"],
            "enpitsu": ["えんぴつ", "エンピツ", "鉛筆"],
            "kami": ["かみ", "カミ", "紙"],
            "tsukue": ["つくえ", "ツクエ", "机"],
            "isu": ["いす", "イス", "椅子"],
            "mado": ["まど", "マド", "窓"],
            "doa": ["どあ", "ドア"],
            "kagi": ["かぎ", "カギ", "鍵"],
            "denwa": ["でんわ", "デンワ", "電話"],
            "terebi": ["てれび", "テレビ"],
            "rajio": ["らじお", "ラジオ"],
            "tokei": ["とけい", "トケイ", "時計"],
            "kamera": ["かめら", "カメラ"],
            "kuruma": ["くるま", "クルマ", "車"],
            "jitensha": ["じてんしゃ", "ジテンシャ", "自転車"],
            "densha": ["でんしゃ", "デンシャ", "電車"],
            "hikouki": ["ひこうき", "ヒコウキ", "飛行機"],
            "fune": ["ふね", "フネ", "船"],
            
            // 食物
            "gohan": ["ごはん", "ゴハン", "御飯"],
            "pan": ["ぱん", "パン"],
            "mizu": ["みず", "ミズ", "水"],
            "ocha": ["おちゃ", "オチャ", "お茶"],
            "koohii": ["こーひー", "コーヒー"],
            "gyuunyuu": ["ぎゅうにゅう", "ギュウニュウ", "牛乳"],
            "tamago": ["たまご", "タマゴ", "卵"],
            "niku": ["にく", "ニク", "肉"],
            "sakana": ["さかな", "サカナ", "魚"],
            "yasai": ["やさい", "ヤサイ", "野菜"],
            "kudamono": ["くだもの", "クダモノ", "果物"],
            "ringo": ["りんご", "リンゴ"],
            "mikan": ["みかん", "ミカン"],
            "banana": ["ばなな", "バナナ"],
            "ichigo": ["いちご", "イチゴ"],
            
            // 家族
            "kazoku": ["かぞく", "カゾク", "家族"],
            "chichi": ["ちち", "チチ", "父"],
            "haha": ["はは", "ハハ", "母"],
            "ani": ["あに", "アニ", "兄"],
            "ane": ["あね", "アネ", "姉"],
            "otouto": ["おとうと", "オトウト", "弟"],
            "imouto": ["いもうと", "イモウト", "妹"],
            "kodomo": ["こども", "コドモ", "子供"],
            "akachan": ["あかちゃん", "アカチャン", "赤ちゃん"],

            // 临时候选词 - 帮助用户在输入过程中看到提示
            "tr": ["とる", "トル"],
            "pr": ["ぷる", "プル"],
            "br": ["ぶる", "ブル"],
            "gr": ["ぐる", "グル"],
            "dr": ["どる", "ドル"],
            "fr": ["ふる", "フル"],
            "kr": ["くる", "クル"],
            "sr": ["する", "スル"],
            "wr": ["わる", "ワル"],
            "yr": ["やる", "ヤル"],
            "nr": ["なる", "ナル"],
            "mr": ["まる", "マル"],
            "lr": ["らる", "ラル"],
            "hr": ["はる", "ハル"],
            "jr": ["じる", "ジル"],
            "cr": ["くる", "クル"],
            "vr": ["ばる", "バル"]
        }
    }
    
    // 日文候选词获取
    function getJapaneseCandidates(romaji) {
        if (!romaji || romaji.length === 0) {
            return []
        }
        
        var dict = getRomajiDictionary()
        var candidates = []
        var lowerRomaji = romaji.toLowerCase()
        
        // 1. 完全匹配
        if (dict[lowerRomaji]) {
            candidates = candidates.concat(dict[lowerRomaji])
        }
        
        // 2. 前缀匹配
        for (var key in dict) {
            if (key.startsWith(lowerRomaji) && key !== lowerRomaji) {
                candidates = candidates.concat(dict[key].slice(0, 2))
            }
        }
        
        // 去重并限制数量
        var uniqueCandidates = []
        var seen = {}
        for (var i = 0; i < candidates.length && uniqueCandidates.length < 9; i++) {
            if (!seen[candidates[i]]) {
                seen[candidates[i]] = true
                uniqueCandidates.push(candidates[i])
            }
        }
        
        return uniqueCandidates
    }
}
