import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: patientListRoot

    // Properties for customization
    property string titleText: "Unallocated Patients"
    property string titleIconSource: "../../Resource/Image/New_Patients.png"
    property var patientsData: []
    property alias selectedIndex: listView.selectedIndex

    // Customizable content for buttons
    property Component leftButtonComponent: null
    property Component rightButtonComponent: null

    // Signals
    signal patientSelected(var patientData)
    signal sortStateUpdated()

    color: "#1E2233" // 列表背景颜色

    // Title Bar
    Rectangle {
        id: titleBar
        width: parent.width
        height: 78
        color: "transparent"

        Image {
            id: titleIcon
            x: 779 // This will be adjusted or made more dynamic later
            y: 18
            width: 79.82
            height: 63.96
            source: titleIconSource
        }

        Text {
            anchors.left: titleIcon.right
            anchors.leftMargin: 21
            anchors.verticalCenter: parent.verticalCenter
            font.pixelSize: 24
            color: "#FFFFFF"
            text: titleText
        }

        // Loader for the left button
        Loader {
            id: leftButtonLoader
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            sourceComponent: leftButtonComponent
        }

        // Loader for the right button
        Loader {
            id: rightButtonLoader
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            sourceComponent: rightButtonComponent
        }
    }

    property var sortStates: {
        "firstName": {ascending: false, active: false},
        "lastName": {ascending: false, active: false},
        "id1": {ascending: false, active: false},
        "id2": {ascending: false, active: false},
        "dateOfBirth": {ascending: false, active: false}
    }

    // 列表区域
    Rectangle {
        id: listArea
        anchors.top: titleBar.bottom
        width: 1860
        height: 540
        x: 35  // 调整位置，向右移动
        color: "transparent"

        // 列表头
        Rectangle {
            id: listHeader
            width: 1860
            height: 78
            color: "transparent"

            Row {
                anchors.fill: parent

                // 列头组件
                Repeater {
                    model: [
                        {text: "First name", width: 310, sortable: true, column: "firstName"},
                        // 新增关联图标列
                        {text: "", width: 78, sortable: false, column: ""},
                        {text: "Last name", width: 232, sortable: true, column: "lastName"},
                        {text: "ID1", width: 310, sortable: true, column: "id1"},
                        {text: "ID2", width: 310, sortable: true, column: "id2"},
                        {text: "Date of Birth", width: 310, sortable: true, column: "dateOfBirth"},
                        {text: "Chamber", width: 310, sortable: false, column: ""}
                    ]

                    Rectangle {
                        width: modelData.width
                        height: 78
                        color: "transparent"

                        Row {
                            anchors.left: parent.left
                            anchors.leftMargin: 10
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 5

                            Text {
                                anchors.verticalCenter: parent.verticalCenter
                                font.family: "Helvetica"
                                font.pixelSize: 24
                                color: "#81828B"
                                text: modelData.text
                            }

                            Rectangle {
                                width: 78
                                height: 78
                                color: "transparent"
                                visible: modelData.sortable
                                anchors.verticalCenter: parent.verticalCenter

                                property string columnName: modelData.column || ""

                                Image {
                                    id: sortIcon
                                    anchors.centerIn: parent
                                    width: 78
                                    height: 78
                                    source: {
                                        var state = parent.columnName && sortStates[parent.columnName] ? sortStates[parent.columnName] : {active: false, ascending: false}
                                        return !state.active ? "../../Resource/Image/Down_Icon.png" : "../../Resource/Image/Down_Icon_1.png"
                                    }
                                    rotation: {
                                        var state = parent.columnName && sortStates[parent.columnName] ? sortStates[parent.columnName] : {active: false, ascending: false}
                                        if (!state.active) return 0
                                        return state.ascending ? 180 : 0
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        if (modelData.sortable && parent.columnName) {
                                            toggleSort(parent.columnName)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Rectangle {
                anchors.bottom: parent.bottom
                width: parent.width
                height: 2
                color: "#81828B"
                opacity: 0.5
            }
        }

        ListView {
            id: listView
            anchors.top: listHeader.bottom
            width: 1860
            height: 462
            model: patientListRoot.patientsData
            clip: true
            property int selectedIndex: -1

            ScrollBar.vertical: ScrollBar {
                active: true
                policy: ScrollBar.AsNeeded
                width: 12

                background: Rectangle {
                    color: "#2A2A2A"
                    radius: 6
                }

                contentItem: Rectangle {
                    color: "#81828B"
                    radius: 6
                    opacity: parent.pressed ? 0.8 : 0.6
                }
            }

            delegate: Rectangle {
                width: 1860
                height: 78
                color: "#1E2233"  // 列表项背景颜色

                Rectangle {
                    anchors.fill: parent
                    color: "#FFFFFF"
                    opacity: 0.25
                    visible: index === listView.selectedIndex
                }

                Row {
                    anchors.fill: parent
                    anchors.leftMargin: 10

                    // Helper component for text cells
                    Component {
                        id: textCell
                        Rectangle {
                            height: 78
                            color: "transparent"
                            property string cellText
                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 10
                                anchors.verticalCenter: parent.verticalCenter
                                font.family: "Helvetica"
                                font.pixelSize: 20
                                color: "#FFFFFF"
                                text: cellText
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }

                    // First Name
                    Loader {
                        sourceComponent: textCell
                        width: 310
                        property var cellText: firstName || ""
                    }

                    // Association Icon
                    Image {
                        width: 78
                        height: 78
                        anchors.verticalCenter: parent.verticalCenter
                        source: {
                            var assoc = association || 0
                            if (assoc === 1) {
                                return "../../Resource/Image/Patient_link_1.png"  // 主患者图标
                            } else if (assoc === 2) {
                                return "../../Resource/Image/Patient_link_2.png"  // 从患者图标
                            } else {
                                return ""
                            }
                        }
                        visible: association === 1 || association === 2
                        fillMode: Image.PreserveAspectFit
                    }

                    // Last Name
                    Loader {
                        sourceComponent: textCell
                        width: 232
                        property var cellText: lastName || ""
                    }

                    // ID1
                    Loader {
                        sourceComponent: textCell
                        width: 310
                        property var cellText: id1 || ""
                    }

                    // ID2
                    Loader {
                        sourceComponent: textCell
                        width: 310
                        property var cellText: id2 || ""
                    }

                    // Date of Birth
                    Loader {
                        sourceComponent: textCell
                        width: 310
                        property var cellText: dateOfBirth || ""
                    }

                    // Chamber
                    Loader {
                        sourceComponent: textCell
                        width: 310
                        property var cellText: chamber || ""
                    }
                }

                Rectangle {
                    anchors.bottom: parent.bottom
                    width: 1860
                    height: 1
                    border.width: 2
                    border.color: "#3D404D"
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        listView.selectedIndex = index
                        // 为QAbstractListModel创建数据对象
                        var patientData = {
                            "firstName": firstName || "",
                            "lastName": lastName || "",
                            "id1": id1 || "",
                            "id2": id2 || "",
                            "dateOfBirth": dateOfBirth || "",
                            "chamber": chamber || "",
                            "association": association || 0
                        }
                        patientListRoot.patientSelected(patientData)
                    }
                }
            }
        }
    }

    function toggleSort(column) {
        if (!column) return

        if (!sortStates[column]) {
            sortStates[column] = {ascending: false, active: false}
        }

        for (var key in sortStates) {
            if (key !== column) {
                sortStates[key].active = false
                sortStates[key].ascending = false
            }
        }

        if (!sortStates[column].active) {
            sortStates[column].active = true
            sortStates[column].ascending = false
        } else {
            sortStates[column].ascending = !sortStates[column].ascending
        }

        sortPatientData(column, sortStates[column].ascending)

        patientListRoot.sortStateUpdated()
    }

    function sortPatientData(column, ascending) {
        if (!column) {
            return
        }

        var sortedData = patientListRoot.patientsData.slice()

        sortedData.sort(function(a, b) {
            var valueA = a[column] || ""
            var valueB = b[column] || ""

            if (typeof valueA === "string" && typeof valueB === "string") {
                valueA = valueA.toLowerCase()
                valueB = valueB.toLowerCase()
            }

            var result = 0
            if (valueA < valueB) {
                result = -1
            } else if (valueA > valueB) {
                result = 1
            }

            return ascending ? result : -result
        })

        patientListRoot.patientsData = sortedData
    }
}

