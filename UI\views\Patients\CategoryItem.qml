import QtQuick 2.15

Rectangle {
    id: itemRoot
    width: 1860
    height: 78
    color: "transparent"

    property alias icon: leftIcon.source
    property alias text: label.text
    signal clicked

    Image {
        id: leftIcon
        x: 0
        y: 7
        width: 79.82
        height: 63.96
        fillMode: Image.PreserveAspectFit
    }

    Text {
        id: label
        x: 95 // A bit of padding from the icon
        anchors.verticalCenter: parent.verticalCenter
        width: 221
        height: 29
        font.family: "Helvetica"
        font.pixelSize: 24
        color: "#FFFFFF"
    }

    Image {
        id: rightIcon
        x: 1782
        y: 2.5
        width: 78
        height: 78
        source: "../../Resource/Image/Right_Navigation_Icon.png"
        fillMode: Image.PreserveAspectFit
    }

    MouseArea {
        anchors.fill: parent
        cursorShape: Qt.PointingHandCursor
        onClicked: itemRoot.clicked()
    }
}

