import QtQuick 2.15

Rectangle {
    id: editButton
    width: 150
    height: 78
    color: "transparent"

    property string text: "Edit"
    property string iconSource: ""
    signal clicked()

    Row {
        anchors.centerIn: parent
        spacing: 10

        Image {
            width: 78
            height: 78
            source: iconSource
            fillMode: Image.PreserveAspectFit
        }

        Text {
            anchors.verticalCenter: parent.verticalCenter
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            text: editButton.text
        }
    }

    MouseArea {
        anchors.fill: parent
        onClicked: editButton.clicked()
    }
}

