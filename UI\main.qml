import QtQuick 2.15
import QtQuick.Controls 2.15
import "views"

ApplicationWindow {
    id: mainWindow
    width: 3840
    height: 720
    minimumWidth: 3840
    maximumWidth: 3840
    minimumHeight: 720
    maximumHeight: 720
    visible: true
    color: "#1E2233"
    title: "Geri Application"
    
    // 窗口设置
    flags: Qt.Window | Qt.FramelessWindowHint
    modality: Qt.NonModal
    
    // 应用程序状态管理
    property bool showDebugInfo: false
    
    // 直接加载Home界面
    Home {
        id: homeInterface
        anchors.fill: parent
    }
    
    // 状态指示器 (调试用)
    Rectangle {
        id: debugIndicator
        anchors.top: parent.top
        anchors.right: parent.right
        width: 200
        height: 60
        color: "#2A3441"
        border.color: "#00D7B3"
        border.width: 1
        radius: 8
        visible: showDebugInfo
        z: 1000
        
        Column {
            anchors.centerIn: parent
            spacing: 5
            
            Text {
                text: "当前界面: Home"
                font.family: "Helvetica"
                font.pixelSize: 12
                color: "#FFFFFF"
                anchors.horizontalCenter: parent.horizontalCenter
            }
            
            Text {
                text: "窗口尺寸: 3840x720"
                font.family: "Helvetica"
                font.pixelSize: 12
                color: "#FFFFFF"
                anchors.horizontalCenter: parent.horizontalCenter
            }
        }
    }
    
    // 全局快捷键处理
    Item {
        focus: true
        anchors.fill: parent
        
        Keys.onPressed: function(event) {
            // F1 - 切换调试信息显示
            if (event.key === Qt.Key_F1) {
                showDebugInfo = !showDebugInfo
                event.accepted = true
            }
            // F2 - 预留功能
            else if (event.key === Qt.Key_F2) {
                console.log("F2键按下 - 已经在Home界面")
                event.accepted = true
            }
            // ESC - 退出应用程序
            else if (event.key === Qt.Key_Escape) {
                Qt.quit()
                event.accepted = true
            }
        }
    }
    
    // 初始化
    Component.onCompleted: {
        console.log("Geri Application 启动完成")
        console.log("当前界面: Home")
        console.log("窗口尺寸: 3840x720")
        console.log("快捷键:")
        console.log("  F1 - 切换调试信息")
        console.log("  ESC - 退出应用程序")
        console.log("注意：锁屏界面只能通过Home界面中的锁屏按钮访问")
    }
}
