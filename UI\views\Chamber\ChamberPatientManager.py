# -*- coding: utf-8 -*-
"""
舱室患者管理器 - 专门处理舱室相关的患者操作
"""

from PySide6.QtCore import QObject, Signal, Slot
from PySide6.QtQml import qmlRegisterType
import json
from datetime import datetime

class ChamberPatientManager(QObject):
    """舱室患者管理器"""
    
    # 信号
    patientAssigned = Signal(str, str)  # 舱室ID, 患者数据JSON
    patientRemoved = Signal(str)        # 舱室ID
    patientUpdated = Signal(str, str)   # 舱室ID, 患者数据JSON
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._mock_patients = self._load_mock_patients()
    
    def _load_mock_patients(self):
        """加载模拟患者数据"""
        return [
            {
                "firstName": "Jessica",
                "lastName": "Smith", 
                "id1": "11456768-1-3",
                "id2": "893456781-2-2",
                "dateOfBirth": "26th Nov 1978",
                "chamber": "?",
                "assignedTime": None
            },
            {
                "firstName": "Jessica",
                "lastName": "<PERSON>",
                "id1": "11557556-2-5", 
                "id2": "475235789-3-6",
                "dateOfBirth": "13th Dec 1981",
                "chamber": "?",
                "assignedTime": None
            },
            {
                "firstName": "Emily",
                "lastName": "Johnson",
                "id1": "11234567-1-1",
                "id2": "987654321-1-1", 
                "dateOfBirth": "15th Mar 1985",
                "chamber": "?",
                "assignedTime": None
            },
            {
                "firstName": "Sarah",
                "lastName": "Williams",
                "id1": "11345678-2-2",
                "id2": "876543210-2-2",
                "dateOfBirth": "22nd Jul 1990", 
                "chamber": "?",
                "assignedTime": None
            },
            {
                "firstName": "Amanda",
                "lastName": "Brown",
                "id1": "11456789-3-3",
                "id2": "765432109-3-3",
                "dateOfBirth": "8th Jan 1987",
                "chamber": "?",
                "assignedTime": None
            },
            {
                "firstName": "Lisa",
                "lastName": "Davis",
                "id1": "11567890-4-4",
                "id2": "654321098-4-4",
                "dateOfBirth": "30th Sep 1992",
                "chamber": "?",
                "assignedTime": None
            }
        ]
    
    @Slot(result=str)
    def getUnallocatedPatients(self):
        """获取未分配的患者列表"""
        unallocated = [p for p in self._mock_patients if p["chamber"] == "?"]
        return json.dumps(unallocated)
    
    @Slot(str, str, result=bool)
    def assignPatientToChamber(self, chamber_id, patient_data_json):
        """分配患者到舱室"""
        try:
            patient_data = json.loads(patient_data_json)
            
            # 查找患者
            for patient in self._mock_patients:
                if (patient["id1"] == patient_data["id1"] and 
                    patient["id2"] == patient_data["id2"]):
                    
                    # 分配到舱室
                    patient["chamber"] = chamber_id
                    patient["assignedTime"] = datetime.now().isoformat()
                    
                    # 发送信号
                    self.patientAssigned.emit(chamber_id, json.dumps(patient))
                    
                    print(f"患者 {patient['firstName']} {patient['lastName']} 已分配到舱室 {chamber_id}")
                    return True
            
            return False
            
        except Exception as e:
            print(f"分配患者失败: {e}")
            return False
    
    @Slot(str, result=bool)
    def removePatientFromChamber(self, chamber_id):
        """从舱室移除患者"""
        try:
            for patient in self._mock_patients:
                if patient["chamber"] == chamber_id:
                    patient["chamber"] = "?"
                    patient["assignedTime"] = None
                    
                    # 发送信号
                    self.patientRemoved.emit(chamber_id)
                    
                    print(f"患者已从舱室 {chamber_id} 移除")
                    return True
            
            return False
            
        except Exception as e:
            print(f"移除患者失败: {e}")
            return False
    
    @Slot(str, result=str)
    def getPatientByChamber(self, chamber_id):
        """根据舱室ID获取患者信息"""
        for patient in self._mock_patients:
            if patient["chamber"] == chamber_id:
                return json.dumps(patient)
        return "{}"
    
    @Slot(str, result=bool)
    def hasPatientsInChamber(self, chamber_id):
        """检查舱室是否有患者"""
        for patient in self._mock_patients:
            if patient["chamber"] == chamber_id:
                return True
        return False

# 全局实例
_chamber_patient_manager = None

def get_chamber_patient_manager():
    """获取舱室患者管理器单例"""
    global _chamber_patient_manager
    if _chamber_patient_manager is None:
        _chamber_patient_manager = ChamberPatientManager()
    return _chamber_patient_manager

def register_chamber_patient_types():
    """注册QML类型"""
    qmlRegisterType(ChamberPatientManager, "ChamberPatient", 1, 0, "ChamberPatientManager")
    
    # 注册单例
    from PySide6.QtQml import qmlRegisterSingletonType
    qmlRegisterSingletonType(ChamberPatientManager, "ChamberPatient", 1, 0, "ChamberPatientManager", 
                           lambda qml_engine, js_engine: get_chamber_patient_manager())
