import QtQuick 2.15
import QtQuick.Controls 2.15
import "../components" as Components

Rectangle {
    id: unallocatedPatientsRoot

    x: 20
    y: 0
    width: 1890
    height: 620
    color: "#1E2233"

    // 模拟患者数据
    property var mockPatientsData: [
        {
            "firstName": "<PERSON>",
            "lastName": "<PERSON>",
            "id1": "11456768-1-3",
            "id2": "893456781-2-2",
            "dateOfBirth": "26th Nov 1978",
            "chamber": "?",
            "association": 1
        },
        {
            "firstName": "<PERSON>",
            "lastName": "<PERSON>",
            "id1": "11557556-2-5",
            "id2": "475235789-3-6",
            "dateOfBirth": "13th Dec 1981",
            "chamber": "?",
            "association": 2
        },
        {
            "firstName": "Emily",
            "lastName": "<PERSON>",
            "id1": "11234567-1-1",
            "id2": "987654321-1-1",
            "dateOfBirth": "15th Mar 1985",
            "chamber": "?",
            "association": 0
        },
        {
            "firstName": "<PERSON>",
            "lastName": "<PERSON>",
            "id1": "11345678-2-2",
            "id2": "876543210-2-2",
            "dateOfBirth": "22nd Jul 1990",
            "chamber": "?",
            "association": 1
        },
        {
            "firstName": "Amanda",
            "lastName": "Brown",
            "id1": "11456789-3-3",
            "id2": "765432109-3-3",
            "dateOfBirth": "8th Jan 1987",
            "chamber": "?",
            "association": 2
        }
    ]

    Components.PatientListView {
        id: patientListView
        anchors.fill: parent

        titleText: "Unallocated Patients"
        titleIconSource: "../../Resource/Image/Unallocated_patients.png"
        patientsData: mockPatientsData

        leftButtonComponent: Component {
            Rectangle {
                width: 240
                height: 78
                color: "transparent"

                Row {
                    anchors.centerIn: parent
                    spacing: 10

                    Image {
                        width: 78
                        height: 78
                        source: "../../Resource/Image/Left_Navigation_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        text: "Patients"
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("Back to Patients view triggered")
                        // Add navigation logic here
                    }
                }
            }
        }

        rightButtonComponent: Component {
            Rectangle {
                width: 150
                height: 78
                color: "transparent"

                Row {
                    anchors.centerIn: parent
                    spacing: 10

                    Image {
                        width: 78
                        height: 78
                        source: "../../Resource/Image/Edit_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        text: "Edit"
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("Edit button clicked")
                        // Add edit logic here
                    }
                }
            }
        }
    }

}

