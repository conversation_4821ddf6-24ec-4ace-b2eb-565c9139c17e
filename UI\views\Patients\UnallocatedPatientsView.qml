import QtQuick 2.15
import QtQuick.Controls 2.15
import "../components" as Components
import "../models"

Rectangle {
    id: unallocatedPatientsRoot

    x: 20
    y: 0
    width: 1890
    height: 620
    color: "#1E2233"

    UnallocatedPatientsModel {
        id: unallocatedPatientsModel
    }

    Components.PatientListView {
        id: patientListView
        anchors.fill: parent

        titleText: "Unallocated Patients"
        titleIconSource: "../../Resource/Image/Unallocated_patients.png"
        patientsData: unallocatedPatientsModel.patients

        leftButtonComponent: Components.BackButton {
            text: "Patients"
            iconSource: "../../Resource/Image/Left_Navigation_Icon.png"
            onClicked: {
                // Add navigation logic here
                console.log("Back to Patients view triggered")
            }
        }

        rightButtonComponent: Components.EditButton {
            text: "Edit"
            iconSource: "../../Resource/Image/Edit_Icon.png"
            onClicked: {
                // Add edit logic here
                console.log("Edit button clicked")
            }
        }
    }
}

