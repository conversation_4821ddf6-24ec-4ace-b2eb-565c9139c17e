import QtQuick 2.15
import QtQuick.Controls 2.15
import "../components" as Components
import "../models"

Rectangle {
    id: unallocatedPatientsRoot

    x: 20
    y: 0
    width: 1890
    height: 620
    color: "#1E2233"

    UnallocatedPatientsModel {
        id: unallocatedPatientsModel
    }

    Components.PatientListView {
        id: patientListView
        anchors.fill: parent

        titleText: "Unallocated Patients"
        titleIconSource: "../../Resource/Image/Unallocated_patients.png"
        patientsData: unallocatedPatientsModel.patients

        leftButtonComponent: BackButton {
            text: "Patients"
            iconSource: "../../Resource/Image/Left_Navigation_Icon.png"
            onClicked: {
                // Add navigation logic here
                console.log("Back to Patients view triggered")
            }
        }

        rightButtonComponent: EditButton {
            text: "Edit"
            iconSource: "../../Resource/Image/Edit_Icon.png"
            onClicked: {
                // Add edit logic here
                console.log("Edit button clicked")
            }
        }
    }

    // Button components defined locally
    Component {
        id: BackButton
        Rectangle {
            width: 240
            height: 78
            color: "transparent"

            property string text: "Patients"
            property string iconSource: ""
            signal clicked()

            Row {
                anchors.centerIn: parent
                spacing: 10

                Image {
                    width: 78
                    height: 78
                    source: iconSource
                    fillMode: Image.PreserveAspectFit
                }

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: parent.parent.text
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: parent.clicked()
            }
        }
    }

    Component {
        id: EditButton
        Rectangle {
            width: 150
            height: 78
            color: "transparent"

            property string text: "Edit"
            property string iconSource: ""
            signal clicked()

            Row {
                anchors.centerIn: parent
                spacing: 10

                Image {
                    width: 78
                    height: 78
                    source: iconSource
                    fillMode: Image.PreserveAspectFit
                }

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: parent.parent.text
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: parent.clicked()
            }
        }
    }
}

