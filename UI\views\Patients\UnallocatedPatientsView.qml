import QtQuick 2.15
import QtQuick.Controls 2.15
import "../components" as Components
import PatientModels 1.0

Rectangle {
    id: unallocatedPatientsRoot

    x: 20
    y: 0
    width: 1890
    height: 620
    color: "#1E2233"

    UnallocatedPatientsModel {
        id: unallocatedPatientsModel
    }

    Components.PatientListView {
        id: patientListView
        anchors.fill: parent

        titleText: "Unallocated Patients"
        titleIconSource: "../../Resource/Image/Unallocated_patients.png"
        patientsData: unallocatedPatientsModel

        leftButtonComponent: Component {
            Rectangle {
                width: 240
                height: 78
                color: "transparent"

                Row {
                    anchors.centerIn: parent
                    spacing: 10

                    Image {
                        width: 78
                        height: 78
                        source: "../../Resource/Image/Left_Navigation_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        text: "Patients"
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("Back to Patients view triggered")
                        // Add navigation logic here
                    }
                }
            }
        }

        rightButtonComponent: Component {
            Rectangle {
                width: 150
                height: 78
                color: "transparent"

                Row {
                    anchors.centerIn: parent
                    spacing: 10

                    Text {
                        anchors.verticalCenter: parent.verticalCenter
                        font.family: "Helvetica"
                        font.pixelSize: 24
                        color: "#FFFFFF"
                        text: "Edit"
                    }

                    Image {
                        width: 42
                        height: 42
                        source: "../../Resource/Image/Edit_Icon.png"
                        fillMode: Image.PreserveAspectFit
                    }
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        console.log("Edit button clicked")
                        // Add edit logic here
                    }
                }
            }
        }
    }

}

