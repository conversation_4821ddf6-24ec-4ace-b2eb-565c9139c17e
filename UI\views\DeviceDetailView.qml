import QtQuick 2.15
import QtQuick.Controls 2.15

Page {
    id: deviceDetailPage
    
    // 从上级页面传递的属性
    property string deviceId: ""
    property string deviceName: ""
    property string status: ""
    property string temperature: ""
    property string humidity: ""
    property string co2Level: ""
    property string statusColor: "#00A605"
    
    // 查找包含导航函数的父级组件
    function findHomeInterface() {
        var stackView = deviceDetailPage.StackView.view
        if (stackView && stackView.parent) {
            return stackView.parent
        }
        return null
    }
    
    Rectangle {
        anchors.fill: parent
        color: "#1E2233"
        
        // 页面标题区域
        Rectangle {
            id: titleArea
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            height: 60
            color: "transparent"
            
            Button {
                id: backButton
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                width: 120
                height: 40
                text: "← 返回"
                onClicked: {
                    var homeInterface = findHomeInterface()
                    if (homeInterface && homeInterface.navigateBack) {
                        homeInterface.navigateBack()
                    }
                }
                
                background: Rectangle {
                    color: "#2A3441"
                    radius: 8
                    border.color: "#00D7B3"
                    border.width: 1
                }
                
                contentItem: Text {
                    text: backButton.text
                    font.family: "Helvetica"
                    font.pixelSize: 16
                    color: "#FFFFFF"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
            
            Text {
                anchors.centerIn: parent
                text: deviceName + " - 详细信息"
                font.family: "Helvetica"
                font.pixelSize: 28
                font.weight: Font.Bold
                color: "#FFFFFF"
            }
        }
        
        // 详情内容区域
        ScrollView {
            anchors.top: titleArea.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.bottom: parent.bottom
            anchors.margins: 20
            
            Column {
                width: parent.width
                spacing: 30
                
                // 设备状态卡片
                Rectangle {
                    width: parent.width
                    height: 150
                    color: "#2A3441"
                    radius: 12
                    border.color: statusColor
                    border.width: 2
                    
                    Row {
                        anchors.fill: parent
                        anchors.margins: 20
                        spacing: 30
                        
                        // 状态指示器
                        Rectangle {
                            width: 100
                            height: 100
                            radius: 50
                            color: statusColor
                            anchors.verticalCenter: parent.verticalCenter
                            
                            Text {
                                anchors.centerIn: parent
                                text: status
                                font.family: "Helvetica"
                                font.pixelSize: 18
                                font.weight: Font.Bold
                                color: "#000000"
                            }
                        }
                        
                        // 基本信息
                        Column {
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 10
                            
                            Text {
                                text: "设备ID: " + deviceId
                                font.family: "Helvetica"
                                font.pixelSize: 16
                                color: "#CCCCCC"
                            }
                            
                            Text {
                                text: "设备名称: " + deviceName
                                font.family: "Helvetica"
                                font.pixelSize: 18
                                font.weight: Font.Bold
                                color: "#FFFFFF"
                            }
                            
                            Text {
                                text: "当前状态: " + status
                                font.family: "Helvetica"
                                font.pixelSize: 16
                                color: statusColor
                            }
                        }
                    }
                }
                
                // 环境参数卡片
                Rectangle {
                    width: parent.width
                    height: 200
                    color: "#2A3441"
                    radius: 12
                    
                    Column {
                        anchors.fill: parent
                        anchors.margins: 20
                        spacing: 15
                        
                        Text {
                            text: "环境参数"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            font.weight: Font.Bold
                            color: "#FFFFFF"
                        }
                        
                        Row {
                            width: parent.width
                            spacing: 40
                            
                            // 温度
                            Column {
                                spacing: 5
                                
                                Text {
                                    text: "温度"
                                    font.family: "Helvetica"
                                    font.pixelSize: 14
                                    color: "#CCCCCC"
                                }
                                
                                Text {
                                    text: temperature
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    font.weight: Font.Bold
                                    color: "#00D7B3"
                                }
                            }
                            
                            // 湿度
                            Column {
                                spacing: 5
                                
                                Text {
                                    text: "湿度"
                                    font.family: "Helvetica"
                                    font.pixelSize: 14
                                    color: "#CCCCCC"
                                }
                                
                                Text {
                                    text: humidity
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    font.weight: Font.Bold
                                    color: "#00D7B3"
                                }
                            }
                            
                            // CO2
                            Column {
                                spacing: 5
                                
                                Text {
                                    text: "CO₂浓度"
                                    font.family: "Helvetica"
                                    font.pixelSize: 14
                                    color: "#CCCCCC"
                                }
                                
                                Text {
                                    text: co2Level
                                    font.family: "Helvetica"
                                    font.pixelSize: 24
                                    font.weight: Font.Bold
                                    color: "#00D7B3"
                                }
                            }
                        }
                    }
                }
                
                // 控制按钮区域
                Rectangle {
                    width: parent.width
                    height: 120
                    color: "#2A3441"
                    radius: 12
                    
                    Column {
                        anchors.fill: parent
                        anchors.margins: 20
                        spacing: 15
                        
                        Text {
                            text: "设备控制"
                            font.family: "Helvetica"
                            font.pixelSize: 20
                            font.weight: Font.Bold
                            color: "#FFFFFF"
                        }
                        
                        Row {
                            spacing: 20
                            
                            Button {
                                text: "参数设置"
                                width: 120
                                height: 40
                                onClicked: {
                                    var homeInterface = findHomeInterface()
                                    if (homeInterface) {
                                        homeInterface.navigateToSubPage("views/DeviceSettingsView.qml", {
                                            deviceId: deviceDetailPage.deviceId,
                                            deviceName: deviceDetailPage.deviceName
                                        })
                                    }
                                }
                                
                                background: Rectangle {
                                    color: "#008FB9"
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    font.family: "Helvetica"
                                    font.pixelSize: 14
                                    color: "#FFFFFF"
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                            
                            Button {
                                text: "历史数据"
                                width: 120
                                height: 40
                                onClicked: {
                                    var homeInterface = findHomeInterface()
                                    if (homeInterface) {
                                        homeInterface.navigateToSubPage("views/DeviceHistoryView.qml", {
                                            deviceId: deviceDetailPage.deviceId,
                                            deviceName: deviceDetailPage.deviceName
                                        })
                                    }
                                }
                                
                                background: Rectangle {
                                    color: "#008FB9"
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    font.family: "Helvetica"
                                    font.pixelSize: 14
                                    color: "#FFFFFF"
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                            
                            Button {
                                text: "维护模式"
                                width: 120
                                height: 40
                                onClicked: {
                                    homeWindow.showDialog("dialogs/MaintenanceDialog.qml", {
                                        deviceId: deviceDetailPage.deviceId,
                                        deviceName: deviceDetailPage.deviceName
                                    })
                                }
                                
                                background: Rectangle {
                                    color: "#FF6E00"
                                    radius: 8
                                }
                                
                                contentItem: Text {
                                    text: parent.text
                                    font.family: "Helvetica"
                                    font.pixelSize: 14
                                    color: "#FFFFFF"
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                            }
                        }
                    }
                }
            }
        }
    }
} 