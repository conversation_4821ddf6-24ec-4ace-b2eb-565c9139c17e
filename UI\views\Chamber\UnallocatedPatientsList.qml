import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: unallocatedPatientsPopup
    
    // 组件属性
    property bool isVisible: false
    property int selectedIndex: -1
    property string chamberId: ""  // 目标舱室ID
    
    // 信号
    signal patientSelected(var patientData)
    signal cancelled()
    signal confirmed()
    signal sortStateUpdated()
    signal patientAssigned(var patientData, string targetChamberId)  // 新增：患者分配信号
    
    // 位置和尺寸 - 严格按照需求文档
    x: 1930
    y: isVisible ? 0 : 720  // 从底部渐入弹出
    width: 1890
    height: 620
    
    // 样式
    color: "#313542"
    radius: 40
    
    // 只有顶部圆角
    Rectangle {
        anchors.fill: parent
        anchors.topMargin: 40
        color: parent.color
    }
    
    // 可见性控制
    visible: opacity > 0
    opacity: isVisible ? 1.0 : 0.0
    
    // 动画效果 - 从底部渐入弹出
    Behavior on y {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }
    
    Behavior on opacity {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }
    
    // 模拟患者数据
    property var patientsData: [
        {
            "firstName": "Jessica",
            "lastName": "<PERSON>", 
            "id1": "11456768-1-3",
            "id2": "893456781-2-2",
            "dateOfBirth": "26th Nov 1978",
            "chamber": "?"
        },
        {
            "firstName": "Jessica",
            "lastName": "Smith",
            "id1": "11557556-2-5", 
            "id2": "475235789-3-6",
            "dateOfBirth": "13th Dec 1981",
            "chamber": "?"
        },
        {
            "firstName": "Emily",
            "lastName": "Johnson",
            "id1": "11234567-1-1",
            "id2": "987654321-1-1", 
            "dateOfBirth": "15th Mar 1985",
            "chamber": "?"
        },
        {
            "firstName": "Sarah",
            "lastName": "Williams",
            "id1": "11345678-2-2",
            "id2": "876543210-2-2",
            "dateOfBirth": "22nd Jul 1990", 
            "chamber": "?"
        },
        {
            "firstName": "Amanda",
            "lastName": "Brown",
            "id1": "11456789-3-3",
            "id2": "765432109-3-3",
            "dateOfBirth": "8th Jan 1987",
            "chamber": "?"
        },
        {
            "firstName": "Lisa",
            "lastName": "Davis",
            "id1": "11567890-4-4",
            "id2": "654321098-4-4",
            "dateOfBirth": "30th Sep 1992",
            "chamber": "?"
        },
        {
            "firstName": "Michael",
            "lastName": "Wilson",
            "id1": "11678901-5-5",
            "id2": "543210987-5-5",
            "dateOfBirth": "12th Apr 1988",
            "chamber": "?"
        },
        {
            "firstName": "Jennifer",
            "lastName": "Taylor",
            "id1": "11789012-6-6",
            "id2": "432109876-6-6",
            "dateOfBirth": "5th Jun 1993",
            "chamber": "?"
        }
    ]

    // 排序状态
    property var sortStates: {
        "firstName": {ascending: false, active: false},
        "lastName": {ascending: false, active: false},
        "id1": {ascending: false, active: false},
        "id2": {ascending: false, active: false},
        "dateOfBirth": {ascending: false, active: false}
    }

    // 原始数据备份
    property var originalPatientsData: [
        {
            "firstName": "Jessica",
            "lastName": "Smith",
            "id1": "11557556-2-5",
            "id2": "475235789-3-6",
            "dateOfBirth": "13th Dec 1981",
            "chamber": "?"
        },
        {
            "firstName": "Emily",
            "lastName": "Johnson",
            "id1": "11234567-1-1",
            "id2": "987654321-1-1",
            "dateOfBirth": "15th Mar 1985",
            "chamber": "?"
        },
        {
            "firstName": "Sarah",
            "lastName": "Williams",
            "id1": "11345678-2-2",
            "id2": "876543210-2-2",
            "dateOfBirth": "22nd Jul 1990",
            "chamber": "?"
        },
        {
            "firstName": "Amanda",
            "lastName": "Brown",
            "id1": "11456789-3-3",
            "id2": "765432109-3-3",
            "dateOfBirth": "8th Jan 1987",
            "chamber": "?"
        },
        {
            "firstName": "Lisa",
            "lastName": "Davis",
            "id1": "11567890-4-4",
            "id2": "654321098-4-4",
            "dateOfBirth": "30th Sep 1992",
            "chamber": "?"
        },
        {
            "firstName": "Michael",
            "lastName": "Wilson",
            "id1": "11678901-5-5",
            "id2": "543210987-5-5",
            "dateOfBirth": "12th Apr 1988",
            "chamber": "?"
        },
        {
            "firstName": "Jennifer",
            "lastName": "Taylor",
            "id1": "11789012-6-6",
            "id2": "432109876-6-6",
            "dateOfBirth": "5th Jun 1993",
            "chamber": "?"
        }
    ]
    
    // 标题栏
    Rectangle {
        id: titleBar
        width: 1890
        height: 78
        color: "transparent"
        
        // 标题图标 - 按需求文档位置
        Image {
            id: titleIcon
            x: 779
            y: 18
            width: 90.08
            height: 41.98
            source: "../../Resource/Image/New_Patients.png"
        }

        // 标题文字 - 在图标右侧21px
        Text {
            anchors.left: titleIcon.right
            anchors.leftMargin: 21
            anchors.verticalCenter: parent.verticalCenter
            width: 221
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            text: "Unallocated Patients"
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
        
        // Cancel按钮
        Rectangle {
            id: cancelButton
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            width: 153
            height: 78
            color: "transparent"
            
            Row {
                anchors.centerIn: parent
                spacing: 0
                
                Image {
                    width: 78
                    height: 78
                    source: "../../Resource/Image/Cancel_Icon.png"
                    rotation: 90
                }
                
                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 75
                    height: 29
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: "Cancel"
                    verticalAlignment: Text.AlignVCenter
                }
            }
            
            MouseArea {
                anchors.fill: parent
                onClicked: {
                    unallocatedPatientsPopup.cancelled()
                    unallocatedPatientsPopup.hide()
                }
            }
        }
        
        // 确认按钮
        Rectangle {
            id: confirmButton
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            width: 150
            height: 78
            color: "transparent"
            
            property bool enabled: selectedIndex >= 0
            opacity: enabled ? 1.0 : 0.4
            
            Row {
                anchors.centerIn: parent
                spacing: 24
                
                Text {
                    x: 17
                    anchors.verticalCenter: parent.verticalCenter
                    width: 35
                    height: 29
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: "OK"
                    horizontalAlignment: Text.AlignRight
                    verticalAlignment: Text.AlignVCenter
                }
                
                Image {
                    x: 76
                    anchors.verticalCenter: parent.verticalCenter
                    width: 60
                    height: 60
                    source: "../../Resource/Image/OK_Icon_2.png"
                }
            }
            
            MouseArea {
                anchors.fill: parent
                enabled: confirmButton.enabled
                onClicked: {
                    if (confirmButton.enabled && selectedIndex >= 0) {
                        var patientData = patientsData[selectedIndex]
                        // console.log("确认分配患者:", JSON.stringify(patientData), "到舱室:", chamberId)

                        // 调用后台API记录患者分配
                        if (typeof ChamberDataManager !== 'undefined') {
                            var success = ChamberDataManager.assignPatientToChamber(
                                chamberId,
                                patientData.firstName,
                                patientData.lastName,
                                patientData.id1
                            )
                            console.log("后台患者分配结果:", success)
                        }

                        // 更新前端显示的chamber字段
                        patientData.chamber = chamberId

                        // console.log("患者chamber字段已更新为:", chamberId)
                        // console.log("更新后的患者数据:", JSON.stringify(patientsData[selectedIndex]))
                        // console.log("所有患者数据的chamber字段:")
                        // for (var j = 0; j < patientsData.length; j++) {
                        //     console.log("  患者", j, ":", patientsData[j].firstName, patientsData[j].lastName, "- Chamber:", patientsData[j].chamber)
                        // }

                        // 发送患者分配信号
                        unallocatedPatientsPopup.patientAssigned(patientData, chamberId)
                        unallocatedPatientsPopup.patientSelected(patientData)
                        unallocatedPatientsPopup.confirmed()
                        unallocatedPatientsPopup.hide()
                    }
                }
            }
        }
    }
    
    // 列表区域
    Rectangle {
        id: listArea
        anchors.top: titleBar.bottom
        width: 1860
        height: 540
        x: 15  // 居中
        color: "transparent"
        
        // 列表头
        Rectangle {
            id: listHeader
            width: 1860
            height: 78
            color: "transparent"
            
            Row {
                anchors.fill: parent
                
                // 列头组件
                Repeater {
                    model: [
                        {text: "First name", width: 310, sortable: true, column: "firstName"},
                        {text: "Last name", width: 310, sortable: true, column: "lastName"},
                        {text: "ID1", width: 310, sortable: true, column: "id1"},
                        {text: "ID2", width: 310, sortable: true, column: "id2"},
                        {text: "Date of Birth", width: 310, sortable: true, column: "dateOfBirth"},
                        {text: "Chamber", width: 310, sortable: false, column: ""}
                    ]
                    
                    Rectangle {
                        width: modelData.width
                        height: 78
                        color: "transparent"

                        // 使用Row布局让列名和排序按钮紧挨着
                        Row {
                            anchors.left: parent.left
                            anchors.leftMargin: 10
                            anchors.verticalCenter: parent.verticalCenter
                            spacing: 5  // 列名和按钮之间的间距

                            // 列名文字
                            Text {
                                anchors.verticalCenter: parent.verticalCenter
                                font.family: "Helvetica"
                                font.pixelSize: 24
                                color: "#81828B"
                                text: modelData.text
                                verticalAlignment: Text.AlignVCenter
                            }

                            // 排序按钮 - 78x78，紧挨着列名
                            Rectangle {
                                width: 78
                                height: 78
                                color: "transparent"
                                visible: modelData.sortable
                                anchors.verticalCenter: parent.verticalCenter

                                property string columnName: modelData.column || ""
                                property bool isActive: columnName && sortStates[columnName] ? sortStates[columnName].active : false
                                property bool isAscending: columnName && sortStates[columnName] ? sortStates[columnName].ascending : false

                                // 使用资源文件中的图标
                                Image {
                                    id: sortIcon
                                    anchors.centerIn: parent
                                    width: 78
                                    height: 78

                                    property string columnName: parent.columnName
                                    property int refreshCounter: 0  // 用于强制刷新

                                    source: {
                                        refreshCounter; // 依赖刷新计数器
                                        // 直接从sortStates读取最新状态
                                        var state = columnName && sortStates[columnName] ? sortStates[columnName] : {active: false, ascending: false}
                                        // console.log("更新排序图标 - 列:", columnName, "活跃:", state.active, "正序:", state.ascending)

                                        if (!state.active) {
                                            // 默认未点击：显示Down_Icon
                                            return "../../Resource/Image/Down_Icon.png"
                                        } else {
                                            // 点击后：显示Down_Icon_1
                                            return "../../Resource/Image/Down_Icon_1.png"
                                        }
                                    }

                                    rotation: {
                                        refreshCounter; // 依赖刷新计数器
                                        var state = columnName && sortStates[columnName] ? sortStates[columnName] : {active: false, ascending: false}

                                        if (!state.active) {
                                            return 0  // 默认不旋转
                                        } else if (state.ascending) {
                                            return 180  // 正序时翻转180度
                                        } else {
                                            return 0    // 倒序时不翻转
                                        }
                                    }

                                    // 监听sortStates变化
                                    Connections {
                                        target: unallocatedPatientsPopup
                                        function onSortStateUpdated() {
                                            // 通过增加计数器强制刷新
                                            sortIcon.refreshCounter++
                                        }
                                    }

                                    // 快速旋转动画
                                    Behavior on rotation {
                                        PropertyAnimation {
                                            duration: 150  // 减少动画时间
                                            easing.type: Easing.OutQuad
                                        }
                                    }

                                    // 快速图片切换
                                    Behavior on source {
                                        PropertyAnimation {
                                            duration: 100  // 快速切换图片
                                        }
                                    }

                                    // 调试图片加载
                                    onStatusChanged: {
                                        if (status === Image.Error) {
                                            console.log("排序图标加载失败:", source)
                                        }
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    onClicked: {
                                        if (modelData.sortable && parent.columnName) {
                                            toggleSort(parent.columnName)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 列表头底部分割线
            Rectangle {
                anchors.bottom: parent.bottom
                width: parent.width
                height: 2
                color: "#81828B"
                opacity: 0.5
            }
        }

        // 患者列表 - 支持滚动，不限制行数
        ListView {
            id: patientsList
            anchors.top: listHeader.bottom
            width: 1860
            height: 462  // 540 - 78 (header height)

            model: patientsData
            clip: true  // 启用裁剪，支持滚动

            // 强制刷新机制
            property int refreshCounter: 0

            // 监听数据变化
            onModelChanged: {
                console.log("ListView模型已更新，数据长度:", model ? model.length : 0)
                refreshCounter++
            }

            // 滚动条样式
            ScrollBar.vertical: ScrollBar {
                active: true
                policy: ScrollBar.AsNeeded
                width: 12

                background: Rectangle {
                    color: "#2A2A2A"
                    radius: 6
                }

                contentItem: Rectangle {
                    color: "#81828B"
                    radius: 6
                    opacity: parent.pressed ? 0.8 : 0.6
                }
            }
            
            delegate: Rectangle {
                width: 1860
                height: 78
                color: "#313542"  // 基础背景色

                // 选中时的高亮叠加层
                Rectangle {
                    anchors.fill: parent
                    color: "#FFFFFF"
                    opacity: 0.25
                    visible: index === selectedIndex
                }
                
                // 行数据
                Row {
                    anchors.fill: parent
                    anchors.leftMargin: 10
                    
                    Repeater {
                        model: [
                            modelData.firstName,
                            modelData.lastName, 
                            modelData.id1,
                            modelData.id2,
                            modelData.dateOfBirth,
                            modelData.chamber
                        ]
                        
                        Rectangle {
                            width: 310
                            height: 78
                            color: "transparent"
                            
                            Text {
                                anchors.left: parent.left
                                anchors.leftMargin: 10
                                anchors.verticalCenter: parent.verticalCenter
                                font.family: "Helvetica"
                                font.pixelSize: 20
                                color: "#FFFFFF"
                                text: modelData
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }
                }
                
                // 分割线 - 按需求文档样式
                Rectangle {
                    anchors.bottom: parent.bottom
                    width: 1860
                    height: 1
                    border.width: 2
                    border.color: "#3D404D"
                    color: "transparent"
                }
                
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        selectedIndex = index
                        console.log("选中患者:", patientsData[index].firstName, patientsData[index].lastName)
                    }
                }
            }
        }
    }
    
    // 排序切换函数
    function toggleSort(column) {
        console.log("切换排序:", column)

        if (!column) return

        // 确保sortStates中有该列的状态
        if (!sortStates[column]) {
            sortStates[column] = {ascending: false, active: false}
        }

        // 重置其他列的排序状态 - 确保其他按钮变为Down_Icon
        for (var key in sortStates) {
            if (key !== column) {
                sortStates[key].active = false
                sortStates[key].ascending = false
            }
        }

        // 二状态切换：倒序 ↔ 正序（一旦点击就保持激活状态）
        if (!sortStates[column].active) {
            // 首次点击：激活倒序
            sortStates[column].active = true
            sortStates[column].ascending = false
            console.log("设置", column, "为倒序")
        } else {
            // 已激活状态：在正序和倒序之间切换
            sortStates[column].ascending = !sortStates[column].ascending
            console.log("设置", column, "为", sortStates[column].ascending ? "正序" : "倒序")
        }

        // 实际排序数据
        sortPatientData(column, sortStates[column].active ? sortStates[column].ascending : false)

        // 触发界面更新 - 直接触发，不重新创建对象
        unallocatedPatientsPopup.sortStateUpdated()

        console.log("排序状态更新完成:", JSON.stringify(sortStates[column]))
    }

    // 排序数据函数
    function sortPatientData(column, ascending) {
        console.log("开始排序数据:", column, ascending ? "正序" : "倒序")

        if (!column) {
            console.log("无效的排序列")
            return
        }

        // 创建排序后的数组 - 从当前数据开始排序以保留chamber字段更新
        var sortedData = patientsData.slice() // 从当前数据开始排序，保留chamber字段更新

        sortedData.sort(function(a, b) {
            var valueA = a[column] || ""
            var valueB = b[column] || ""

            // 字符串比较
            if (typeof valueA === "string" && typeof valueB === "string") {
                valueA = valueA.toLowerCase()
                valueB = valueB.toLowerCase()
            }

            var result = 0
            if (valueA < valueB) {
                result = -1
            } else if (valueA > valueB) {
                result = 1
            }

            // 如果是倒序，反转结果
            return ascending ? result : -result
        })

        // 更新数据
        patientsData = sortedData
        console.log("排序完成，数据已更新，当前数据长度:", patientsData.length)
        console.log("前3条数据:", JSON.stringify(patientsData.slice(0, 3)))
    }

    // 显示/隐藏方法
    function show(targetChamberId) {
        chamberId = targetChamberId || ""
        isVisible = true
        selectedIndex = -1
        // console.log("显示未分配患者列表，目标舱室:", targetChamberId)

        // 从后台同步患者分配状态
        syncPatientAssignments()

        // 调试：显示同步后的患者chamber字段状态
        // console.log("同步后患者列表chamber字段状态:")
        // for (var i = 0; i < patientsData.length; i++) {
        //     console.log("  患者", i, ":", patientsData[i].firstName, patientsData[i].lastName, "- Chamber:", patientsData[i].chamber)
        // }
    }

    // 从后台同步患者分配状态
    function syncPatientAssignments() {
        // console.log("开始同步患者分配状态...")

        if (typeof ChamberDataManager === 'undefined') {
            // console.log("ChamberDataManager不可用，无法同步")
            return
        }

        // 更新每个患者的chamber字段
        for (var i = 0; i < patientsData.length; i++) {
            var patient = patientsData[i]
            var assignedChamber = ChamberDataManager.getPatientAssignedChamber(
                patient.firstName,
                patient.lastName,
                patient.id1
            )

            // 更新chamber字段
            patientsData[i].chamber = assignedChamber
            originalPatientsData[i].chamber = assignedChamber

            // console.log("患者", patient.firstName, patient.lastName, "分配状态:", assignedChamber)
        }

        // 强制刷新ListView
        var tempData = patientsData.slice()
        patientsData = []
        patientsData = tempData

        console.log("患者分配状态同步完成")
    }

    function hide() {
        isVisible = false
        selectedIndex = -1
        console.log("隐藏未分配患者列表")
    }
}
