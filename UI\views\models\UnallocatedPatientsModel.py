# -*- coding: utf-8 -*-
"""
未分配患者列表数据模型
"""

from PySide6.QtCore import QObject, Signal, Slot, QAbstractListModel, QModelIndex, Qt
from PySide6.QtQml import qmlRegisterType
from datetime import datetime, date
import json

class PatientData:
    """患者数据类"""
    def __init__(self, first_name="", last_name="", id1="", id2="", date_of_birth="", chamber="?"):
        self.first_name = first_name
        self.last_name = last_name
        self.id1 = id1
        self.id2 = id2
        self.date_of_birth = date_of_birth
        self.chamber = chamber
    
    def to_dict(self):
        return {
            "firstName": self.first_name,
            "lastName": self.last_name,
            "id1": self.id1,
            "id2": self.id2,
            "dateOfBirth": self.date_of_birth,
            "chamber": self.chamber
        }

class UnallocatedPatientsModel(QAbstractListModel):
    """未分配患者列表模型"""
    
    # 自定义角色
    FirstNameRole = Qt.UserRole + 1
    LastNameRole = Qt.UserRole + 2
    Id1Role = Qt.UserRole + 3
    Id2Role = Qt.UserRole + 4
    DateOfBirthRole = Qt.UserRole + 5
    ChamberRole = Qt.UserRole + 6
    
    # 信号
    dataUpdated = Signal()
    selectionChanged = Signal(int)  # 选中行索引
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._patients = []
        self._selected_index = -1
        self._sort_column = ""
        self._sort_ascending = False
        
        # 加载模拟数据
        self.load_mock_data()
    
    def roleNames(self):
        """定义角色名称"""
        return {
            self.FirstNameRole: b"firstName",
            self.LastNameRole: b"lastName", 
            self.Id1Role: b"id1",
            self.Id2Role: b"id2",
            self.DateOfBirthRole: b"dateOfBirth",
            self.ChamberRole: b"chamber"
        }
    
    def rowCount(self, parent=QModelIndex()):
        """返回行数"""
        return len(self._patients)
    
    def data(self, index, role):
        """返回数据"""
        if not index.isValid() or index.row() >= len(self._patients):
            return None
            
        patient = self._patients[index.row()]
        
        if role == self.FirstNameRole:
            return patient.first_name
        elif role == self.LastNameRole:
            return patient.last_name
        elif role == self.Id1Role:
            return patient.id1
        elif role == self.Id2Role:
            return patient.id2
        elif role == self.DateOfBirthRole:
            return patient.date_of_birth
        elif role == self.ChamberRole:
            return patient.chamber
            
        return None
    
    def load_mock_data(self):
        """加载模拟数据"""
        mock_patients = [
            PatientData("Jessica", "Smith", "11456768-1-3", "893456781-2-2", "26th Nov 1978", "?"),
            PatientData("Jessica", "Smith", "11557556-2-5", "475235789-3-6", "13th Dec 1981", "?"),
            PatientData("Emily", "Johnson", "11234567-1-1", "987654321-1-1", "15th Mar 1985", "?"),
            PatientData("Sarah", "Williams", "11345678-2-2", "876543210-2-2", "22nd Jul 1990", "?"),
            PatientData("Amanda", "Brown", "11456789-3-3", "765432109-3-3", "8th Jan 1987", "?"),
            PatientData("Lisa", "Davis", "11567890-4-4", "654321098-4-4", "30th Sep 1992", "?"),
            PatientData("Michelle", "Miller", "11678901-5-5", "543210987-5-5", "12th May 1988", "?"),
            PatientData("Jennifer", "Wilson", "11789012-6-6", "432109876-6-6", "3rd Nov 1983", "?"),
        ]
        
        self.beginResetModel()
        self._patients = mock_patients
        self.endResetModel()
        self.dataUpdated.emit()
    
    @Slot(str, bool)
    def sort_by_column(self, column, ascending):
        """按列排序"""
        self._sort_column = column
        self._sort_ascending = ascending
        
        self.beginResetModel()
        
        if column == "firstName":
            self._patients.sort(key=lambda p: p.first_name.lower(), reverse=not ascending)
        elif column == "lastName":
            self._patients.sort(key=lambda p: p.last_name.lower(), reverse=not ascending)
        elif column == "id1":
            self._patients.sort(key=lambda p: p.id1, reverse=not ascending)
        elif column == "id2":
            self._patients.sort(key=lambda p: p.id2, reverse=not ascending)
        elif column == "dateOfBirth":
            self._patients.sort(key=lambda p: self._parse_date(p.date_of_birth), reverse=not ascending)
        
        self.endResetModel()
        self.dataUpdated.emit()
    
    def _parse_date(self, date_str):
        """解析日期字符串用于排序"""
        try:
            # 解析 "26th Nov 1978" 格式
            parts = date_str.replace("th", "").replace("st", "").replace("nd", "").replace("rd", "").split()
            day = int(parts[0])
            month_map = {
                "Jan": 1, "Feb": 2, "Mar": 3, "Apr": 4, "May": 5, "Jun": 6,
                "Jul": 7, "Aug": 8, "Sep": 9, "Oct": 10, "Nov": 11, "Dec": 12
            }
            month = month_map.get(parts[1], 1)
            year = int(parts[2])
            return date(year, month, day)
        except:
            return date(1900, 1, 1)
    
    @Slot(int)
    def select_row(self, index):
        """选择行"""
        if 0 <= index < len(self._patients):
            self._selected_index = index
            self.selectionChanged.emit(index)
    
    @Slot(result=int)
    def get_selected_index(self):
        """获取选中的索引"""
        return self._selected_index
    
    @Slot(result=bool)
    def has_selection(self):
        """是否有选中项"""
        return self._selected_index >= 0
    
    @Slot(result=str)
    def get_selected_patient_json(self):
        """获取选中患者的JSON数据"""
        if 0 <= self._selected_index < len(self._patients):
            patient = self._patients[self._selected_index]
            return json.dumps(patient.to_dict())
        return "{}"

# 注册QML类型
def register_types():
    qmlRegisterType(UnallocatedPatientsModel, "PatientModels", 1, 0, "UnallocatedPatientsModel")
