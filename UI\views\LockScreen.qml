import QtQuick
import QtQuick.Window
import QtMultimedia
import QtQuick.Shapes
import QtQuick.Effects

Item {
    id: lockScreen
    width: 3840
    height: 720
    
    // 对外暴露的信号
    signal unlockCompleted()
    
    // 主背景层 (#1E2233 深蓝黑色) - 确保背景色始终可见
    Rectangle {
        id: mainContainer
        width: parent.width
        height: parent.height
        color: "#1E2233"  // 强制设置背景色
        
        // 底层：视频播放 - 设置透明背景确保不遮盖主背景
        Rectangle {
            id: videoContainer
            width: parent.width
            height: parent.height
            anchors.centerIn: parent
            color: "transparent"  // 透明背景
            
            MediaPlayer {
                id: mediaPlayer
                source: Qt.resolvedUrl("../Resource/Image/lockscreen.mov")
                loops: MediaPlayer.Infinite
                autoPlay: true
                videoOutput: videoOutput
                
                onErrorOccurred: function(error, errorString) {
                    console.log("视频播放错误:", error, errorString)
                }
                
                onMediaStatusChanged: {
                    console.log("媒体状态:", mediaStatus)
                }
            }
            
            VideoOutput {
                id: videoOutput
                anchors.fill: parent
                fillMode: VideoOutput.PreserveAspectCrop  // 恢复为PreserveAspectCrop填满整个区域
                visible: true
            }
        }
        
        // 中间层：使用MultiEffect实现智能绿色光效
        MultiEffect {
            id: glowLayer
            width: parent.width
            height: parent.height
            anchors.centerIn: parent
            source: videoOutput
            
            // 颜色处理 - 修正为纯绿色，避免偏蓝
            colorization: 0.3  // 适中的绿色强度
            colorizationColor: "#00FF00"  // 纯绿色，避免偏蓝
            
            // 亮度和对比度调整 - 平衡亮度与UI可见性
            brightness: 0.2    // 适中亮度，避免过亮影响UI
            contrast: 1.2      // 适中对比度，保持白色核心但不过度
            saturation: 0.7    // 适中饱和度，避免过度绿化
            
            // 使用遮罩效果 - 进一步细化线条
            maskEnabled: true
            maskSource: ShaderEffectSource {
                sourceItem: videoOutput
                live: true
            }
            maskThresholdMin: 0.8   // 进一步提高阈值，让线条更细
            maskSpreadAtMin: 0.2    // 大幅减少散射范围，精细化线条
            maskThresholdMax: 0.99  // 极高阈值，保持核心白色
            maskSpreadAtMax: 0.05   // 核心区域几乎无散射，突出白色细线
            
            // 模糊效果 - 最小化模糊，保持线条锐利
            blurEnabled: true
            blur: 0.08          // 进一步减少模糊，让线条更锐利
            blurMax: 4          // 极小范围模糊，保持线条精细度
            blurMultiplier: 0.6 // 进一步降低模糊倍数
        }
        
        // Logo图标层
        Image {
            id: logoIcon
            source: Qt.resolvedUrl("../Resource/Image/Logo_Icon.png")
            width: 229.51
            height: 97.57
            x: 822
            y: 311
            opacity: 0.8    // 提高透明度，让图标更明显
            fillMode: Image.PreserveAspectFit
            z: 100          // 确保在最上层显示
            
            // 图片加载状态监控
            onStatusChanged: {
                if (status === Image.Error) {
                    console.log("Logo图片加载失败:", source)
                } else if (status === Image.Ready) {
                    console.log("Logo图片加载成功:", source, "尺寸:", sourceSize)
                }
            }
            
            // 添加调试边框，确认图标位置
            Rectangle {
                anchors.fill: parent
                color: "transparent"
                border.color: "red"
                border.width: 1
                visible: false  // 关闭调试边框
            }
        }
        
        // 顶层：UI元素层（按照精确设计参数）
        Rectangle {
            id: unlockArea
            width: parent.width
            height: parent.height
            anchors.centerIn: parent
            color: "transparent"
            
            // 滑动解锁组件
            Rectangle {
                id: slideContainer
                width: 546
                height: 81
                x: 2620
                y: 320
                radius: 40.5
                color: "#40FFFFFF"  // 半透明白色背景
                border.color: "transparent"
                border.width: 0
                
                property bool isUnlocked: false  // 改回isUnlocked，初始状态为锁定
                
                // 滑动文本 - 根据状态显示不同文本
                Text {
                    anchors.centerIn: parent
                    text: slideContainer.isUnlocked ? "Slide to Unlock" : "Slide to Lock"  // 修正逻辑
                    color: "white"
                    font.pixelSize: 28
                    font.bold: true
                    opacity: (slideButton.x < parent.width * 0.3 || slideButton.x > parent.width * 0.7) ? 1.0 : 0.0
                    
                    Behavior on opacity {
                        NumberAnimation { duration: 100 }  // 减少文字淡入淡出延迟
                    }
                    
                    Behavior on text {
                        SequentialAnimation {
                            NumberAnimation { target: parent; property: "opacity"; to: 0; duration: 50 }
                            PropertyAction { target: parent; property: "text" }
                            NumberAnimation { target: parent; property: "opacity"; to: 1.0; duration: 50 }
                        }
                    }
                }
                
                // 滑动按钮
                Rectangle {
                    id: slideButton
                    width: 74
                    height: 74
                    radius: 37
                    anchors.verticalCenter: parent.verticalCenter
                    x: slideContainer.isUnlocked ? parent.width - width - 4 : 4
                    
                    // 渐变背景 - 根据设计图的渐变色
                    gradient: Gradient {
                        GradientStop { position: 0.0; color: "#00B48F" }  // 线性渐变开始
                        GradientStop { position: 1.0; color: "#04FF8E" }  // 线性渐变结束
                    }
                    
                    // 箭头图标
                    Item {
                        id: arrowIcon
                        width: 30.36
                        height: 33.31
                        anchors.centerIn: parent
                        
                        // 简化的箭头形状 - 使用镜像变化
                        Text {
                            anchors.centerIn: parent
                            text: slideContainer.isUnlocked ? "←" : "→"  // 直接切换箭头方向
                            color: "#FFFFFF"
                            font.pixelSize: 36
                            font.bold: true
                        }
                    }
                    
                    // 滑动动画
                    Behavior on x {
                        NumberAnimation {
                            duration: 150  // 进一步减少滑动动画延迟，更快响应
                            easing.type: Easing.OutCubic  // 使用更快的缓动效果
                        }
                    }
                }
                
                // 拖拽处理
                MouseArea {
                    anchors.fill: parent
                    drag.target: slideButton
                    drag.axis: Drag.XAxis
                    drag.minimumX: 4
                    drag.maximumX: parent.width - slideButton.width - 4
                    
                    onReleased: {
                        var threshold = parent.width * 0.5  // 50%阈值
                        
                        if (slideButton.x > threshold) {
                            // 滑动到右侧 - 解锁操作
                            slideContainer.isUnlocked = true
                            slideButton.x = parent.width - slideButton.width - 4
                            
                            // 显示解锁成功提示
                            unlockSuccessIndicator.opacity = 1.0
                            
                            // 解锁完成后延迟触发
                            unlockTimer.start()
                        } else {
                            // 滑动到左侧 - 回到锁定状态
                            slideContainer.isUnlocked = false
                            slideButton.x = 4
                            unlockSuccessIndicator.opacity = 0.0
                        }
                    }
                }
                
                // 解锁完成延时器
                Timer {
                    id: unlockTimer
                    interval: 300  // 300毫秒后触发解锁完成，提供足够的视觉反馈时间
                    onTriggered: {
                        if (slideContainer.isUnlocked) {  // 当解锁状态时触发解锁完成
                            lockScreen.unlockCompleted()
                        }
                    }
                }
                
                // 解锁成功提示
                Rectangle {
                    id: unlockSuccessIndicator
                    width: 120
                    height: 40
                    radius: 20
                    color: "#4000FF00"  // 半透明绿色
                    border.color: "#00FF00"
                    border.width: 2
                    anchors.centerIn: slideContainer
                    opacity: 0.0
                    z: 200
                    
                    Text {
                        anchors.centerIn: parent
                        text: "解锁成功"
                        color: "#00FF00"
                        font.pixelSize: 16
                        font.bold: true
                    }
                    
                    Behavior on opacity {
                        NumberAnimation {
                            duration: 200
                            easing.type: Easing.OutCubic
                        }
                    }
                }
            }
        }
    }
} 