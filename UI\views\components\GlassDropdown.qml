import QtQuick 2.15
import QtQuick.Controls 2.15
import Qt5Compat.GraphicalEffects


// 高斯模糊下拉框控件 - 重叠设计
Item {
    id: root

    // 公开属性
    property string currentText: "选择选项"
    property var options: []
    property bool isOpen: false
    property string placeholderText: "请选择"
    property int itemHeight: 68
    property int maxVisibleItems: 4

    // 信号
    signal optionSelected(string option)

    width: 634
    height: itemHeight

    // 下拉选项容器 - 在显示框下层，从顶部开始扩展
    Rectangle {
        id: dropdownContainer
        visible: root.isOpen
        width: 634 // 设计稿宽度
        height: 360 // 修正：总高度为360px
        anchors.top: parent.top
        anchors.left: parent.left
        z: -1
        color: "transparent"

        // --- 毛玻璃背景实现 (按新设计稿调整圆角) ---
        Rectangle {
            anchors.fill: parent
            color: "transparent"
            topLeftRadius: 34
            topRightRadius: 34 // 直角
            bottomLeftRadius: 34
            bottomRightRadius: 34
            clip: true

            // 1. 捕获背景层
            ShaderEffectSource {
                id: backgroundSource
                anchors.fill: parent
                // sourceItem: mainWindow // Removed to fix QML type error; using refreshBackground() instead
            }

            // 2. 应用背景模糊
            FastBlur {
                anchors.fill: parent
                source: backgroundSource
                radius: 50
            }

            // 3. 叠加10%白色填充 (QML规范)
            Rectangle {
                anchors.fill: parent
                color: "#ffffff"
                opacity: 0.1
                // 圆角必须与父容器一致
                topLeftRadius: parent.topLeftRadius
                topRightRadius: parent.topRightRadius
                bottomLeftRadius: parent.bottomLeftRadius
                bottomRightRadius: parent.bottomRightRadius
            }
        }

        // 选项列表
        ListView {
            id: listView
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.top: parent.top
            anchors.topMargin: root.height
            height: 360 - root.height - 14 // 动态计算列表高度并预留底部空间
            clip: true
            model: root.options
            boundsBehavior: Flickable.StopAtBounds // 禁止滚动越界

            delegate: ItemDelegate {
                width: listView.width
                height: root.itemHeight
                highlighted: false

                background: Rectangle {
                    color: "transparent"
                }

                contentItem: Rectangle {
                    color: "transparent"
                    Text {
                        text: modelData
                        color: "white"
                        font.pixelSize: 20
                        font.family: "PingFangSC"
                        font.weight: Font.Normal
                        anchors.left: parent.left
                        anchors.leftMargin: 35
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                // 分割线
                Rectangle {
                    visible: index < listView.model.length - 1
                    width: 602
                    height: 1
                    color: "#ffffff"
                    opacity: 0.1
                    anchors.bottom: parent.bottom
                    anchors.left: parent.left
                    anchors.leftMargin: 17
                }

                onClicked: {
                    root.currentText = modelData
                    root.isOpen = false
                    root.optionSelected(modelData)
                }
            }

            // 自定义滚动条 (最终修复方案)
            ScrollBar.vertical: ScrollBar {
                id: scrollBar
                width: 13
                policy: ScrollBar.AlwaysOn
                height: parent.height // Set height explicitly to avoid anchor loop
                anchors.top: parent.top
                anchors.right: parent.right
                anchors.rightMargin: 10

                contentItem: Rectangle {
                    width: 13
                    height: 90
                    color: "#ffffff"
                    opacity: 0.75
                    radius: 6.5
                }
            }
        }
    }

    // 主下拉框显示区域 - 透明背景，显示毛玻璃效果
    Rectangle {
        id: mainDropdown
        anchors.fill: parent
        z: 1000  // 在下拉列表上方

        color: "#FFFFFF"  // 白色背景
        radius: 34 // 恢复固定的圆角
        border.color: "#C6C6C8"
        border.width: 1

        // 显示文字
        Text {
            id: displayText
            anchors.left: parent.left
            anchors.leftMargin: 30
            anchors.verticalCenter: parent.verticalCenter
            width: parent.width - 108
            font.family: "Helvetica-Bold"
            font.pixelSize: 24
            font.bold: true
            color: "#333333"  // 深灰色文字，在白色背景上清晰
            text: root.currentText || root.placeholderText
            elide: Text.ElideRight
        }

        // 下拉箭头
        Image {
            id: arrowIcon
            anchors.right: parent.right
            //anchors.rightMargin: 30
            anchors.verticalCenter: parent.verticalCenter
            source: "../../Resource/Image/Down_Icon.png"
            width: 78
            height: 78
            smooth: true
            rotation: root.isOpen ? 180 : 0

            // 添加Behavior确保动画稳定执行
            Behavior on rotation {
                RotationAnimation {
                    duration: 200 // 动画时长200毫秒
                    easing.type: Easing.InOutQuad
                }
            }
            mipmap: true
        }

        // 点击区域
        MouseArea {
            anchors.fill: parent
            onClicked: {
                root.toggle()
            }
        }
    }

    // 函数定义
    function toggle() {
        if (isOpen) {
            close()
        } else {
            open()
        }
    }

    function open() {
        // 直接展开下拉框，使用多层背景模拟毛玻璃
        isOpen = true
    }

    function refreshBackground() {
        var targetItem = root.parent
        var attempts = 0
        while (targetItem && targetItem.parent && attempts < 15) {
            attempts++
            if (targetItem.width > 0 && targetItem.height > 0) {
                break
            }
            targetItem = targetItem.parent
        }

        if (targetItem) {
            var designX = 1194
            var designY = 120

            targetItem.grabToImage(function(result) {
                if (result && result.url && result.image) {
                    if (!result.image.width || !result.image.height) {
                        return
                    }

                    var cropX = designX
                    var cropY = designY
                    var cropW = root.width
                    var cropH = root.height

                    if (cropX + cropW > result.image.width) {
                        cropW = result.image.width - cropX
                    }
                    if (cropY + cropH > result.image.height) {
                        cropH = result.image.height - cropY
                    }

                    if (cropX < 0 || cropY < 0 || cropW <= 0 || cropH <= 0) {
                        return
                    }

                    backgroundImage.sourceClipRect = Qt.rect(cropX, cropY, cropW, cropH)
                    backgroundImage.source = result.url
                } else {
                    backgroundImage.source = ""
                }
            })
        }
    }

    function close() {
        isOpen = false
    }

    // 展开/收起时刷新背景（只在展开时刷新）
    onIsOpenChanged: {
        if (isOpen) {
            refreshBackground()
        }
    }
}
