import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import ChamberBackend 1.0
import "../components"

Rectangle {
    id: addPatientView

    // 组件属性
    property bool isVisible: true // Should be visible when pushed
    property var keyboard: null // 接收全局键盘的引用

    // 表单数据
    property string firstName: ""
    property string lastName: ""
    property string id1: ""
    property string id2: ""
    property string birthDay: ""
    property string birthMonth: ""
    property string birthYear: ""
    property string eggAge: ""
    property string cycleType: ""

    // 周期类型下拉框状态
    property bool cycleTypeDropdownOpen: false

    // 信号
    signal cancelled()
    signal confirmed(var newPatientData)

    // Function to set initial focus, called from Home.qml
    function requestInitialFocus() {
        firstNameInput.forceActiveFocus()
        if (keyboard) {
            keyboard.showKeyboard(firstNameInput)
        }
    }

    // 位置和尺寸 - 正确的弹窗尺寸
    x: 20
    y: 0 // 固定y坐标，移除动画
    width: 1890
    height: 620

    // 样式 - 背景颜色改为#1E2233
    color: "#1E2233"
    radius: 40




    // 只有顶部圆角
    Rectangle {
        anchors.fill: parent
        anchors.topMargin: 40
        color: parent.color
    }

    // 可见性控制
    visible: opacity > 0
    opacity: isVisible ? 1.0 : 0.0

    // 移除了从底部弹出的动画效果

    // 移除了透明度变化的动画效果

    // 标题栏
    Rectangle {
        id: titleBar
        width: 1890
        height: 78
        color: "transparent"

        // 标题图标 - 使用newPatient_icon.png
        Image {
            id: titleIcon
            x: 856
            y: 8
            width: 90.08
            height: 41.98
            source: "../../Resource/Image/newPatient_icon.png"
            fillMode: Image.PreserveAspectFit
        }

        // 标题文字 - 固定显示"New Patient"
        Text {
            anchors.left: titleIcon.right
            anchors.leftMargin: 21
            anchors.verticalCenter: parent.verticalCenter
            width: 300
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            text: "New Patient"
            horizontalAlignment: Text.AlignLeft
            verticalAlignment: Text.AlignVCenter
        }

        // Cancel按钮
        Rectangle {
            id: cancelButton
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            width: 153
            height: 78
            color: "transparent"

            Row {
                anchors.centerIn: parent
                spacing: 0

                Image {
                    width: 78
                    height: 78
                    source: "../../Resource/Image/Cancel_Icon.png"
                    rotation: 90
                }

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 75
                    height: 29
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: "Cancel"
                    verticalAlignment: Text.AlignVCenter
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    addPatientView.cancelled()
                }
            }
        }

        // 确认按钮
        Rectangle {
            id: confirmButton
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            width: 150
            height: 78
            color: "transparent"

            Row {
                anchors.centerIn: parent
                spacing: 24

                Text {
                    x: 17
                    anchors.verticalCenter: parent.verticalCenter
                    width: 35
                    height: 29
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: "OK"
                    horizontalAlignment: Text.AlignRight
                    verticalAlignment: Text.AlignVCenter
                }

                Image {
                    x: 76
                    anchors.verticalCenter: parent.verticalCenter
                    width: 60
                    height: 60
                    source: "../../Resource/Image/OK_Icon_2.png"
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    var newPatientData = {
                        "firstName": addPatientView.firstName,
                        "lastName": addPatientView.lastName,
                        "id1": addPatientView.id1,
                        "id2": addPatientView.id2,
                        "dateOfBirth": formatBirthDate(),
                        "eggAge": addPatientView.eggAge,
                        "cycleType": addPatientView.cycleType
                    }
                    addPatientView.confirmed(newPatientData)
                }
            }
        }
    }

    // 表单区域
    Rectangle {
        id: formArea
        anchors.top: titleBar.bottom
        width: 1890
        height: 542
        color: "transparent"

        // 第一列字段 - 使用绝对定位

        // First name 标签
        Text {
            x: 60
            y: 40
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "First name:"
            verticalAlignment: Text.AlignVCenter
        }

        // First name 输入框
        Rectangle {
            x: 280
            y: 40
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: firstNameInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.firstName
                onTextChanged: addPatientView.firstName = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = firstNameInput
                    }
                }
            }
        }

        // Last name 标签
        Text {
            x: 60
            y: 120  // 40 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Last name:"
            verticalAlignment: Text.AlignVCenter
        }

        // Last name 输入框
        Rectangle {
            x: 280
            y: 120
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: lastNameInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.lastName
                onTextChanged: addPatientView.lastName = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = lastNameInput
                    }
                }
            }
        }

        // ID1 标签
        Text {
            x: 60
            y: 200  // 第三行：120 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "ID1:"
            verticalAlignment: Text.AlignVCenter
        }

        // ID1 输入框
        Rectangle {
            x: 280
            y: 200
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: id1Input
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.id1
                onTextChanged: addPatientView.id1 = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = id1Input
                    }
                }
            }
        }

        // ID2 标签
        Text {
            x: 60
            y: 280  // 第四行：200 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "ID2:"
            verticalAlignment: Text.AlignVCenter
        }

        // ID2 输入框
        Rectangle {
            x: 280
            y: 280
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: id2Input
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.id2
                onTextChanged: addPatientView.id2 = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = id2Input
                    }
                }
            }
        }

        // Date of birth 标签
        Text {
            x: 60
            y: 360  // 第五行：280 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Date of birth:"
            verticalAlignment: Text.AlignVCenter
        }

        // Day 输入框
        Rectangle {
            x: 280
            y: 360
            width: 171
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: dayInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                horizontalAlignment: TextInput.AlignHCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.birthDay
                onTextChanged: addPatientView.birthDay = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = dayInput
                    }
                }
            }
        }

        // Day 标签
        Text {
            x: 344  // 280 + 171/2 - 43/2 = 280 + 85.5 - 21.5 = 344
            y: 436  // 360 + 68 + 8
            width: 43
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Day"
            horizontalAlignment: Text.AlignHCenter
        }

        // Month 输入框
        Rectangle {
            x: 505  // 280 + 171 + 54
            y: 360
            width: 171
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: monthInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                horizontalAlignment: TextInput.AlignHCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.birthMonth
                onTextChanged: addPatientView.birthMonth = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = monthInput
                    }
                }
            }
        }

        // Month 标签
        Text {
            x: 569  // 505 + 171/2 - 43/2 = 505 + 85.5 - 21.5 = 569
            y: 436
            width: 43
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Month"
            horizontalAlignment: Text.AlignHCenter
        }

        // Year 输入框
        Rectangle {
            x: 730  // 505 + 171 + 54
            y: 360
            width: 171
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: yearInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                horizontalAlignment: TextInput.AlignHCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.birthYear
                onTextChanged: addPatientView.birthYear = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = yearInput
                    }
                }
            }
        }

        // Year 标签
        Text {
            x: 794  // 730 + 171/2 - 43/2 = 730 + 85.5 - 21.5 = 794
            y: 436
            width: 43
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Year"
            horizontalAlignment: Text.AlignHCenter
        }

        // Egg age 标签
        Text {
            x: 974
            y: 40
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Egg age (Years):"
            verticalAlignment: Text.AlignVCenter
        }

        // Egg age 输入框
        Rectangle {
            x: 1194  // 974 + 200 + 20
            y: 40
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: eggAgeInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: addPatientView.eggAge
                onTextChanged: addPatientView.eggAge = text

                onActiveFocusChanged: {
                    if (activeFocus && keyboard) {
                        keyboard.currentInputField = eggAgeInput
                    }
                }
            }
        }

        // Cycle type 标签
        Text {
            x: 974  // 第二列起始位置
            y: 120  // 第二行：40 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Cycle type:"
            verticalAlignment: Text.AlignVCenter
        }

        // 周期类型下拉框 - 使用高斯模糊控件
        GlassDropdown {
            id: cycleTypeDropdown
            x: 1194  // 974 + 200 + 20
            y: 120   // 第二行：40 + 68 + 12
            width: 634
            height: 68
            currentText: addPatientView.cycleType
            placeholderText: "选择周期类型"
            options: [
                "卵胞浆内单精子显微注射 (ICSI)",
                "温热/解冻卵母细胞",
                "体外人工授精(IVF)",
                "温热/解冻第2天的胚胎",
                "温热/解冻第3天的胚胎",
                "温热/解冻第4天的胚胎",
                "温热/解冻第5天的胚胎",
                "温热/解冻第6天的胚胎",
                "ICSI补救"
            ]
            isOpen: addPatientView.cycleTypeDropdownOpen

            onOptionSelected: function(option) {
                addPatientView.cycleType = option
                addPatientView.cycleTypeDropdownOpen = false
            }

            onIsOpenChanged: {
                addPatientView.cycleTypeDropdownOpen = isOpen
            }
        }
    }





    // Helper function to format the birth date
    function formatBirthDate() {
        if (!birthDay || !birthMonth || !birthYear) {
            return ""
        }

        // Month conversion to abbreviation
        var monthNames = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                         "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        var monthNum = parseInt(birthMonth)
        var monthName = (monthNum >= 1 && monthNum <= 12) ? monthNames[monthNum] : birthMonth

        // Day suffix (st, nd, rd, th)
        var daySuffix = "th"
        var dayNum = parseInt(birthDay)
        if (dayNum % 10 === 1 && dayNum !== 11) daySuffix = "st"
        else if (dayNum % 10 === 2 && dayNum !== 12) daySuffix = "nd"
        else if (dayNum % 10 === 3 && dayNum !== 13) daySuffix = "rd"

        return birthDay + daySuffix + " " + monthName + " " + birthYear
    }
}
