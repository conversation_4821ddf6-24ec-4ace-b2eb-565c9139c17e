# 舱室详情界面需求文档 (已实现)

## 作为舱室数据区域（左）和 操作区（右）    
舱室数据区域（左）
1.环境监控数据
2.患者操作及患者信息显示
 1）当前舱室未分配患者时，显示“分配患者”按钮
 2）点击“分配患者”按钮，右侧弹出患者列表弹窗![分配患者](<Chamber - New - Add Patient.png>)
 3）当前舱室已分配患者时，根据患者后台推送患者信息并显示到对应为止，并隐藏“分配患者”按钮和文字，显示“移除患者”和“编辑患者”按钮
 4）点击“移除患者”按钮，进入移除患者确认界面![移除患者](<Well Dish View - Remove Patient pop pup.png>)
 5）点击“编辑患者”按钮，进入编辑患者界面![编辑患者](<Chamber - New - Image Capture – Edit Patient.png>)

3.图像控制及孔位图像显示
  当患者分配好，显示图像控制区域，包括捕获图像按钮、调整图像按钮，以及操作引导文字
  
  3）图像预览显示区域
  4）图像信息显示
4.引导文字显示




## 📋 实现状态概览
- ✅ **底层背景结构** - 完全实现
- ✅ **16孔椭圆排列** - 完全实现，支持胚胎状态显示
- ✅ **患者信息显示** - 完全实现，支持中英文姓名
- ✅ **环境监控数据** - 完全实现，实时数据同步
- ✅ **图像控制功能** - 新增实现
- ✅ **患者管理按钮** - 完全实现
- ✅ **数据同步机制** - 完全实现

## 🏗️ 界面层级结构 (三层架构)

### 1. 底层 - 患者信息显示区域 ✅
![舱室界面未分配患者状态](<Chamber - New.png>)
**使用Home界面背景** (#1E2233)，在该背景之上显示舱室详情模块

![舱室区域的背景](image-2.png)
**底层背景规格**：
- 位置：x: 20px, y: 110px
- 尺寸：width: 1890px, height: 590px
- 颜色：#313542
- 圆角：border-radius: 40px 295px 295px 40px
- **实现方式**：使用Canvas绘制精确圆角路径


#### 中间引导文字区域 ✅
![Step1](image-9.png)
当前舱室未分配患者时，显示引导文字区域,默认显示该图标和文字
- **图标**：
- x:952
- y:161
  width: 38px;
height: 15px;
transform: rotate(-90deg);
background: #FFFFFF;
source:triangle.png
- **文字内容**：
- Step 1 of 2
- 换行
- Allocate Patient 
-  Press 'Image preview'
to get a preview of
all the wells and detect
any empty wells.

- **文字规格**：
- x:1009
- Y:62
  - width: 250px;
height: 116px;
font-family: Helvetica;
font-size: 24px;
color: #FFFFFF;
  - 

  ![step2](image-10.png)
  当点击OK按钮后，显示step2引导文字区域,默认显示该图标和文字
  - **图标**：
- x:952
- y:375
  width: 38px;
height: 15px;
transform: rotate(-90deg);
background: #FFFFFF;
source:triangle.png
- **文字内容**：
- Step 2 of 2
- 换行
- Press 'Adjust image' to adjust the camera and image settings

- **文字规格**：
- x:1009
- Y:370
  - width: 250px;
height: 116px;
font-family: Helvetica;
font-size: 24px;
color: #FFFFFF;


图像捕获过程引导
1. 图像捕获
  · 拍照：按well1至well16顺序进行图像捕获，默认拍z-stack_0基准焦平面画面
  · 裁切1：按照“相机调整-对齐”设置的参数，裁切well画面为1472*1472
  · 识别：图像算法识别well中心位置
  · 裁切2：以算法模型返回的well中心位置为中心点，进行well图像裁切928*928
  · 映射：将裁切后图像映射至对应微孔中。
![图像捕获](image-12.png)
  捕获文字显示
  x:1009
  y:207
  width: 250px;
  height: 87px;
  font-family: Helvetica;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  text:"Images being
       captured.
       Please wait..."

  捕获等待图标（定时旋转，示意运行中的意思）
  x:1014
  y:324
  width: 59.5px;
  height: 58.95px;
  source:wait.png

  当16个孔的图像按序都显示图像后，将捕获文字和捕获等待图标隐藏不显示

1. 空孔标记
  · 识别：图像算法识别well区域是否存在胚胎
  · 标注：按照算法模型返回的结果，将无胚胎的well，自动标注上空孔标签
  · 确认：自动标准完成后，进入人工确认空孔页面，支持人工修正空孔标注结果。
  ![空孔识别](image-11.png)
  空孔识别文字
  x:1009
  y:311
  width: 250px;
  height: 87px;
  font-family: Helvetica;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  text:"Empty wells
        being detected"

  空孔识别图标（定时旋转，示意运行中的意思）
  x:1104
  y:222
  width: 59.5px;
  height: 58.95px;
  source:wait.png

  当16个孔的图像空孔识别完成后，16个孔中按序自动识别为空孔的孔外围显示"空孔"图标，并将空孔识别文字和空孔识别图标隐藏不显示，显示空孔标注图标、文字、确认按钮
  ![空孔标注](<Chamber - New - Image Capture 4 - empty wells.png>)
  空孔图标
  x:1115
  y:152
  width: 39px;
  height: 39px;
  source:No_Embryo_Well.png

  空孔标注文字
  x:1009
  y:221
  width: 250px;
  height: 87px;
  font-family: Helvetica;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  text:"Empty wells detected.

        Tap on the well to 
        add or remove
        the tag to confirm
        the selection."

  确认按钮（复用患者列表页右上角的确认按钮模块）
  x:1059
  y:400
  width: 150;
  height: 78;
  点击OK按钮后，将空孔标注图标、文字、确认按钮均隐藏不显示，adjustImage按钮变为可点击状态，图片资源改为Adjust_Image_1.png，显示step2引导文字区域，并显示开始录制图标


  调整图像引导文字区域
  点击调整图像按钮后，显示调整图像引导文字区域，同时只有1, 5, 9 ， 13孔可点击，其他孔灰色不可点击，默认1号孔为选中状态显示边框width: 83px;height: 83px;border: 4px solid #00FFFF;

  x:1009
  y:252
  width: 250px;
  height: 87px;
  font-family: Helvetica;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  text:"Select wells\n1, 5, 9 or 13\n to adjust image"
  

#### 右侧16孔显示区域 ✅
![16孔显示](image-6.png)![单个孔的图片](image-7.png)![序号](image-8.png)
- **椭圆排列算法**：16个孔位按椭圆形精确排列
- **孔位规格**：
  - 尺寸：width: 83px, height: 83px
  - 形状：圆形 (radius: 41.5px)
  - 1、5、9、13号孔距离边缘20px
- **序号显示**：
  - 字体：Helvetica, 30px, #81828B
  - 位置：在16个孔的内侧区域，距离圆边缘1px
- **胚胎状态显示** ✅：
  - 空孔位：#3D404D
  - A级胚胎：#2196F3 (蓝色)
  - B级胚胎：#FF9800 (橙色)
  - C级胚胎：#F44336 (红色)
  - 已选择：#4CAF50 (绿色)
  - 发育中：#9C27B0 (紫色)
- **数据同步**：从Python后端实时获取孔位数据
  **开始录制**:在顶层居中显示（默认不显示，当空孔标记完成后，点击OK按钮后显示）
  图标
  width: 176px;
  height: 73px;
  source：Start_Icon.png

  文字（显示在图标之上）

  width: 55px;
  height: 29px;
  font-family: Helvetica-Bold;
  font-weight: 700;
  font-size: 24px;
  color: #DA0000;
  text：Start或者Stop

### 2. 中间层 - 图像控制区域 ✅ (新增功能)
- **位置**：x: 20px, y: 110px
- **尺寸**：width: 950px, height: 590px
- **颜色**：#3D404D
- **圆角**：border-radius: 40px 0 0 40px

**左侧控制按钮区域**：
width: 120px;
height: 80px;
- **捕获图像按钮**：
  - 颜色：#00D9FB (蓝色)
  - 图标：Image_Preview.png
  - 标签：Image preview
- **调整图像按钮**：
  - 颜色：#666666 (灰色)
  - 图标：默认不可点击为Adjust_Image.png，需捕获图像后变为可点击，图标变为Adjust_Image_1.png
  - 标签：Adjust image


### 3. 顶层 - 患者管理按钮区域 ✅
- **位置**：x: 590px, y: 110px
- **尺寸**：width: 108px, height: 175px
#### 左侧数据显示区域 ✅
- 尺寸：width: 708px, height: 590px
- 颜色：#3D404D
- 圆角：border-radius: 40px 0 0 40px (左侧圆角，右侧直角)
- 阴影：渐变阴影效果

**舱室序号显示** ✅：
![舱室号码区域——第3层](image-5.png)
- 位置：x: 19px, y: -5px
- 尺寸：width: 57px, height: 138px
- 字体：Helvetica, 115px, #FFFFFF
- 字间距：-7.5px
- **数据源**：从Python后端动态获取舱室编号

**患者信息区域** ✅：
- **姓名显示**：支持中英文模式切换
  - 英文模式：名 + 姓 分行显示
  - 中文模式：完整姓名单行显示
- **患者ID**：ID1、ID2 富文本显示
- **详细信息**：出生日期、卵龄、发育时长、预计受精时间、周期类型、培养基
- **分隔线**：信息区域间的视觉分隔

**环境监控数据** ✅：
![舱室数据区域——第3层](image-4.png)
- 位置：距离底部19px，距离左侧20px
- 尺寸：width: 289px, height: 155px
- **实时数据同步**：与看板界面对应舱室数据完全同步
- **三个监控区域**：
  1. **温度区域**：实时温度显示，状态指示器
  2. **流量区域**：流量值、吹扫状态显示
  3. **湿度区域**：湿度状态显示
- **报警/警告区域**：
  - 报警：开盖超时、热切断、舱室检修、照明异常
  - 警告：相机故障、舱室断联
  - 
**管理按钮**：
width: 108px;
height: 78px;
- **分配患者按钮**：#00A605 (绿色) - 未分配时显示  Allocate_Patient_2.png
- **移除患者按钮**：#DA0000 (红色) - 已分配时显示 Delete_Patient_Icon.png
- **编辑患者按钮**：#0041EE (蓝色) - 已分配时显示 E dit_patient_icon.png

## 🔧 技术实现特性

### 数据同步机制 ✅
- **Python后端集成**：`import ChamberBackend 1.0`
- **实时数据绑定**：`ChamberDataManager.getChamberDataByDeviceId()`
- **信号槽机制**：监听数据变化并自动更新界面
- **属性绑定**：使用readonly property确保数据一致性

### 交互功能 ✅
- **孔位点击**：支持孔位选择和状态切换
- **按钮交互**：患者管理、图像控制功能
- **键盘支持**：ESC键返回上级界面
- **导航集成**：与看板界面无缝切换

### 视觉效果 ✅
- **精确圆角**：使用Canvas实现特殊圆角效果
- **渐变阴影**：柔和的阴影过渡效果
- **状态颜色**：根据胚胎质量动态显示颜色
- **悬停效果**：鼠标交互反馈

## 📊 数据显示规格

### 患者信息 ✅
- **中英文姓名**：自动检测并切换显示模式
- **患者ID**：ID1、ID2 分别显示
- **详细信息**：6项完整患者数据
- **标签系统**：VIP、Priority等状态标签

### 环境数据 ✅
- **温度**：实时温度值 + 状态指示
- **流量**：流量值 + 吹扫状态
- **湿度**：湿度状态显示
- **报警/警告**：4项报警 + 2项警告状态

### 孔位数据 ✅
- **胚胎阶段**：发育阶段显示
- **质量等级**：A/B/C级别标识
- **选择状态**：选中标记显示
- **空孔检测**：自动识别空孔位

