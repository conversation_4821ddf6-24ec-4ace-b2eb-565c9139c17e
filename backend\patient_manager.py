import sys
from typing import Any, Dict, List, Optional
from PySide6.QtCore import QObject, Signal, Slot
from PySide6.QtQml import qmlRegisterSingletonInstance

class PatientManager(QObject):
    """
    Patient Manager - <PERSON><PERSON>
    Manages all patient data, providing an interface for QML.
    """
    patientsChanged = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self._patients = self._initialize_patients()
        self._current_patient_index = 0

    def _initialize_patients(self) -> List[Dict[str, Any]]:
        """Initializes patient data with sample records."""
        return [
            {
                "firstName": "<PERSON>",
                "lastName": "<PERSON>",
                "id1": "113456789-1-3",
                "id2": "893456781-2-2",
                "birthDay": "26",
                "birthMonth": "11",
                "birthYear": "1978",
                "eggAge": "37",
                "cycleType": "IVF"
            },
            {
                "firstName": "<PERSON>",
                "lastName": "<PERSON>",
                "id1": "987654321-0-1",
                "id2": "123456789-0-2",
                "birthDay": "15",
                "birthMonth": "05",
                "birthYear": "1985",
                "eggAge": "39",
                "cycleType": "ICSI"
            }
        ]

    @Slot(result='QVariant')
    def getAllPatients(self) -> List[Dict[str, Any]]:
        """Returns a list of all patients."""
        return self._patients

    @Slot(int, result='QVariant')
    def getPatient(self, index: int) -> Optional[Dict[str, Any]]:
        """Returns a patient by index."""
        if 0 <= index < len(self._patients):
            return self._patients[index]
        return None

    @Slot(int, 'QVariant')
    def updatePatient(self, index: int, data: Dict[str, Any]):
        """Updates a patient's data."""
        if 0 <= index < len(self._patients):
            self._patients[index].update(data)
            self.patientsChanged.emit()

    @Slot('QVariant')
    def addPatient(self, data: Dict[str, Any]):
        """Adds a new patient."""
        self._patients.append(data)
        self.patientsChanged.emit()

# Global singleton instance
_patient_manager_instance = None

def get_patient_manager() -> PatientManager:
    """Gets the singleton instance of the PatientManager."""
    global _patient_manager_instance
    if _patient_manager_instance is None:
        _patient_manager_instance = PatientManager()
    return _patient_manager_instance

def register_patient_manager_qml():
    """Registers the PatientManager as a QML singleton."""
    patient_manager = get_patient_manager()
    qmlRegisterSingletonInstance(
        PatientManager,
        "PatientBackend",
        1, 0,
        "PatientManager",
        patient_manager
    )

