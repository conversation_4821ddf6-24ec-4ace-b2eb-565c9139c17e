import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Effects
import ChamberBackend 1.0
import "../components"

Rectangle {
    id: editPatientPopup

    // 组件属性
    property bool isVisible: false
    property string chamberId: ""
    property var patientData: null
    property var keyboard: null // 接收键盘组件的引用

    // 表单数据
    property string firstName: ""
    property string lastName: ""
    property string id1: ""
    property string id2: ""
    property string dateOfBirth: ""
    property string birthDay: ""
    property string birthMonth: ""
    property string birthYear: ""
    property string eggAge: ""
    property string cycleType: ""

    // 周期类型下拉框状态
    property bool cycleTypeDropdownOpen: false

    // 信号
    signal cancelled()
    signal confirmed(var updatedData)
    signal dataChanged()
    signal popupOpened(var inputField) // 弹窗已打开信号，并传递输入框对象

    // 位置和尺寸 - 正确的弹窗尺寸
    x: 20
    y: isVisible ? 0 : 720  // 从底部渐入弹出到y:0位置
    width: 1890
    height: 620

    // 样式 - 与患者列表弹窗一致
    color: "#313542"
    radius: 40




    // 只有顶部圆角
    Rectangle {
        anchors.fill: parent
        anchors.topMargin: 40
        color: parent.color
    }

    // 可见性控制
    visible: opacity > 0
    opacity: isVisible ? 1.0 : 0.0

    // 动画效果 - 从底部渐入弹出
    Behavior on y {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    Behavior on opacity {
        NumberAnimation {
            duration: 300
            easing.type: Easing.OutCubic
        }
    }

    // 标题栏
    Rectangle {
        id: titleBar
        width: 1890
        height: 78
        color: "transparent"

        // 标题图标
        Image {
            id: titleIcon
            x: 856
            y: 8
            width: 56.51
            height: 63.96
            source: "../../Resource/Image/Active_Patients.png"
            fillMode: Image.PreserveAspectFit
        }

        // 标题文字 - 显示患者姓名（与舱室详情界面一致）
        Text {
            anchors.left: titleIcon.right
            anchors.leftMargin: 21
            anchors.verticalCenter: parent.verticalCenter
            width: 300
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#FFFFFF"
            // 使用与舱室详情界面相同的姓名显示逻辑
            text: getPatientDisplayName()
            horizontalAlignment: Text.AlignLeft
            verticalAlignment: Text.AlignVCenter
        }

        // Cancel按钮
        Rectangle {
            id: cancelButton
            anchors.left: parent.left
            anchors.leftMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            width: 153
            height: 78
            color: "transparent"

            Row {
                anchors.centerIn: parent
                spacing: 0

                Image {
                    width: 78
                    height: 78
                    source: "../../Resource/Image/Cancel_Icon.png"
                    rotation: 90
                }

                Text {
                    anchors.verticalCenter: parent.verticalCenter
                    width: 75
                    height: 29
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: "Cancel"
                    verticalAlignment: Text.AlignVCenter
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    editPatientPopup.cancelled()
                    editPatientPopup.hide()
                }
            }
        }

        // 确认按钮
        Rectangle {
            id: confirmButton
            anchors.right: parent.right
            anchors.rightMargin: 20
            anchors.verticalCenter: parent.verticalCenter
            width: 150
            height: 78
            color: "transparent"

            Row {
                anchors.centerIn: parent
                spacing: 24

                Text {
                    x: 17
                    anchors.verticalCenter: parent.verticalCenter
                    width: 35
                    height: 29
                    font.family: "Helvetica"
                    font.pixelSize: 24
                    color: "#FFFFFF"
                    text: "OK"
                    horizontalAlignment: Text.AlignRight
                    verticalAlignment: Text.AlignVCenter
                }

                Image {
                    x: 76
                    anchors.verticalCenter: parent.verticalCenter
                    width: 60
                    height: 60
                    source: "../../Resource/Image/OK_Icon_2.png"
                }
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    var updatedData = {
                        firstName: editPatientPopup.firstName,
                        lastName: editPatientPopup.lastName,
                        id1: editPatientPopup.id1,
                        id2: editPatientPopup.id2,
                        dateOfBirth: editPatientPopup.dateOfBirth,
                        eggAge: editPatientPopup.eggAge,
                        cycleType: editPatientPopup.cycleType
                    }
                    editPatientPopup.confirmed(updatedData)
                    editPatientPopup.hide()
                }
            }
        }
    }

    // 表单区域
    Rectangle {
        id: formArea
        anchors.top: titleBar.bottom
        width: 1890
        height: 542
        color: "transparent"

        // 第一列字段 - 使用绝对定位

        // First name 标签
        Text {
            x: 60
            y: 40
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "First name:"
            verticalAlignment: Text.AlignVCenter
        }

        // First name 输入框
        Rectangle {
            x: 280
            y: 40
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: firstNameInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.firstName
                onTextChanged: editPatientPopup.firstName = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = firstNameInput
                    }
                }
            }
        }

        // Last name 标签
        Text {
            x: 60
            y: 120  // 40 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Last name:"
            verticalAlignment: Text.AlignVCenter
        }

        // Last name 输入框
        Rectangle {
            x: 280
            y: 120
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: lastNameInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.lastName
                onTextChanged: editPatientPopup.lastName = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = lastNameInput
                    }
                }
            }
        }

        // ID1 标签
        Text {
            x: 60
            y: 200  // 第三行：120 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "ID1:"
            verticalAlignment: Text.AlignVCenter
        }

        // ID1 输入框
        Rectangle {
            x: 280
            y: 200
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: id1Input
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.id1
                onTextChanged: editPatientPopup.id1 = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = id1Input
                    }
                }
            }
        }

        // ID2 标签
        Text {
            x: 60
            y: 280  // 第四行：200 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "ID2:"
            verticalAlignment: Text.AlignVCenter
        }

        // ID2 输入框
        Rectangle {
            x: 280
            y: 280
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: id2Input
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.id2
                onTextChanged: editPatientPopup.id2 = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = id2Input
                    }
                }
            }
        }

        // Date of birth 标签
        Text {
            x: 60
            y: 360  // 第五行：280 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Date of birth:"
            verticalAlignment: Text.AlignVCenter
        }

        // Day 输入框
        Rectangle {
            x: 280
            y: 360
            width: 171
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: dayInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                horizontalAlignment: TextInput.AlignHCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.birthDay
                onTextChanged: editPatientPopup.birthDay = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = dayInput
                    }
                }
            }
        }

        // Day 标签
        Text {
            x: 344  // 280 + 171/2 - 43/2 = 280 + 85.5 - 21.5 = 344
            y: 436  // 360 + 68 + 8
            width: 43
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Day"
            horizontalAlignment: Text.AlignHCenter
        }

        // Month 输入框
        Rectangle {
            x: 505  // 280 + 171 + 54
            y: 360
            width: 171
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: monthInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                horizontalAlignment: TextInput.AlignHCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.birthMonth
                onTextChanged: editPatientPopup.birthMonth = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = monthInput
                    }
                }
            }
        }

        // Month 标签
        Text {
            x: 569  // 505 + 171/2 - 43/2 = 505 + 85.5 - 21.5 = 569
            y: 436
            width: 43
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Month"
            horizontalAlignment: Text.AlignHCenter
        }

        // Year 输入框
        Rectangle {
            x: 730  // 505 + 171 + 54
            y: 360
            width: 171
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: yearInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                horizontalAlignment: TextInput.AlignHCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.birthYear
                onTextChanged: editPatientPopup.birthYear = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = yearInput
                    }
                }
            }
        }

        // Year 标签
        Text {
            x: 794  // 730 + 171/2 - 43/2 = 730 + 85.5 - 21.5 = 794
            y: 436
            width: 43
            height: 29
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Year"
            horizontalAlignment: Text.AlignHCenter
        }

        // Egg age 标签
        Text {
            x: 974
            y: 40
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Egg age (Years):"
            verticalAlignment: Text.AlignVCenter
        }

        // Egg age 输入框
        Rectangle {
            x: 1194  // 974 + 200 + 20
            y: 40
            width: 620
            height: 68
            color: "#FFFFFF"
            radius: 34

            TextInput {
                id: eggAgeInput
                anchors.fill: parent
                anchors.leftMargin: 30
                anchors.rightMargin: 30
                font.family: "Helvetica-Bold"
                font.pixelSize: 24
                font.bold: true
                color: "#000000"
                verticalAlignment: TextInput.AlignVCenter
                cursorDelegate: Rectangle {
                    width: 2
                    height: parent.font.pixelSize * 1.2 // 增加光标高度
                    color: "#008FB9"
                    anchors.verticalCenter: parent.verticalCenter // 确保垂直居中
                    visible: parent.cursorVisible
                }
                text: editPatientPopup.eggAge
                onTextChanged: editPatientPopup.eggAge = text

                onActiveFocusChanged: {
                    if (activeFocus) {
                        keyboard.currentInputField = eggAgeInput
                    }
                }
            }
        }

        // Cycle type 标签
        Text {
            x: 974  // 第二列起始位置
            y: 120  // 第二行：40 + 68 + 12
            width: 200
            height: 68
            font.family: "Helvetica"
            font.pixelSize: 24
            color: "#81828B"
            text: "Cycle type:"
            verticalAlignment: Text.AlignVCenter
        }

        // 周期类型下拉框 - 使用高斯模糊控件
        GlassDropdown {
            id: cycleTypeDropdown
            x: 1194  // 974 + 200 + 20
            y: 120   // 第二行：40 + 68 + 12
            width: 634
            height: 68
            currentText: editPatientPopup.cycleType
            placeholderText: "选择周期类型"
            options: [
                "卵胞浆内单精子显微注射 (ICSI)",
                "温热/解冻卵母细胞",
                "体外人工授精(IVF)",
                "温热/解冻第2天的胚胎",
                "温热/解冻第3天的胚胎",
                "温热/解冻第4天的胚胎",
                "温热/解冻第5天的胚胎",
                "温热/解冻第6天的胚胎",
                "ICSI补救"
            ]
            isOpen: editPatientPopup.cycleTypeDropdownOpen

            onOptionSelected: function(option) {
                editPatientPopup.cycleType = option
                editPatientPopup.cycleTypeDropdownOpen = false
            }

            onIsOpenChanged: {
                editPatientPopup.cycleTypeDropdownOpen = isOpen
            }
        }
    }  // formArea 结束

    // 函数定义
    function show(targetChamberId) {
        chamberId = targetChamberId
        loadPatientData()
        isVisible = true
        popupOpened(firstNameInput) // 发出弹窗已打开信号，并传递输入框
    }

    function hide() {
        isVisible = false
    }

    function loadPatientData() {
        // 直接访问ChamberDataManager获取数据
        if (typeof ChamberDataManager !== 'undefined' && chamberId) {
            var chamberData = ChamberDataManager.getChamberDataByDeviceId(chamberId)

            if (chamberData) {
                // 使用正确的字段名（与舱室详情界面一致）
                firstName = chamberData.patientFirstName || ""
                lastName = chamberData.patientLastName || ""
                id1 = chamberData.patientId1 || ""
                id2 = chamberData.patientId2 || ""
                dateOfBirth = chamberData.birthDate || ""
                eggAge = chamberData.eggAge || ""
                cycleType = chamberData.cycleType || ""

                // 解析生日字符串到三个独立字段
                parseBirthDate(dateOfBirth)
            }
        }
    }

    // 解析生日字符串函数
    function parseBirthDate(birthDateStr) {
        // 重置字段
        birthDay = ""
        birthMonth = ""
        birthYear = ""

        if (!birthDateStr) return

        // 解析格式如 "26th Nov 1978" 或 "22nd Jul 1990"
        var parts = birthDateStr.split(' ')
        if (parts.length >= 3) {
            // 提取日期数字（去掉 st, nd, rd, th 后缀）
            var dayStr = parts[0].replace(/(st|nd|rd|th)/g, "")
            birthDay = dayStr

            // 月份转换
            var monthStr = parts[1]
            var monthMap = {
                "Jan": "1", "Feb": "2", "Mar": "3", "Apr": "4",
                "May": "5", "Jun": "6", "Jul": "7", "Aug": "8",
                "Sep": "9", "Oct": "10", "Nov": "11", "Dec": "12"
            }
            birthMonth = monthMap[monthStr] || monthStr

            // 年份
            birthYear = parts[2]
        }
    }

    // 合并生日字段为原格式
    function formatBirthDate() {
        if (!birthDay || !birthMonth || !birthYear) {
            return ""
        }

        // 添加序数后缀
        var daySuffix = ""
        var dayNum = parseInt(birthDay)
        if (dayNum >= 11 && dayNum <= 13) {
            daySuffix = "th"
        } else {
            switch (dayNum % 10) {
                case 1: daySuffix = "st"; break
                case 2: daySuffix = "nd"; break
                case 3: daySuffix = "rd"; break
                default: daySuffix = "th"; break
            }
        }

        // 月份转换回缩写
        var monthNames = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                         "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
        var monthNum = parseInt(birthMonth)
        var monthName = (monthNum >= 1 && monthNum <= 12) ? monthNames[monthNum] : birthMonth

        return birthDay + daySuffix + " " + monthName + " " + birthYear
    }

    // 获取更新后的患者数据
    function getUpdatedPatientData() {
        return {
            "firstName": firstName,
            "lastName": lastName,
            "id1": id1,
            "id2": id2,
            "dateOfBirth": formatBirthDate(),
            "eggAge": eggAge,
            "cycleType": cycleType
        }
    }


    function getPatientDisplayName() {
        // 与舱室详情界面相同的姓名显示逻辑
        // 支持中英文模式切换，这里使用英文模式的显示方式
        if (firstName && lastName) {
            return firstName + " " + lastName
        } else if (firstName) {
            return firstName
        } else if (lastName) {
            return lastName
        } else {
            return "Patient"
        }
    }



    // 双击弹窗空白区域触发键盘，单击关闭下拉框
    MouseArea {
        anchors.fill: parent
        z: -1  // 确保在其他控件下方
        onClicked: {
            // 点击空白区域关闭下拉框
            if (cycleTypeDropdownOpen) {
                cycleTypeDropdownOpen = false
            }
        }
        onDoubleClicked: {
            console.log("🎹 EditPatientPopup - 双击触发键盘弹出")
            if (parent && parent.showKeyboard) {
                parent.showKeyboard(null)
            }
        }
    }
}
